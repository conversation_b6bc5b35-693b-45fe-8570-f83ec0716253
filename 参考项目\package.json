{"name": "update-webpack4-hcwl", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider && webpack-dev-server --mode=development --config webpack.config.js", "build": "set NODE_OPTIONS=--openssl-legacy-provider && webpack --mode=production --config webpack.prod.js"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "babel-core": "^6.26.3", "babel-loader": "^8.0.6", "babel-plugin-dynamic-import-webpack": "^1.1.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.7.0", "clean-webpack-plugin": "^3.0.0", "css-loader": "^3.0.0", "file-loader": "^4.0.0", "html-webpack-plugin": "^3.2.0", "less": "^3.9.0", "less-loader": "^5.0.0", "mini-css-extract-plugin": "^0.7.0", "mockjs": "^1.0.1-beta3", "style-loader": "^0.23.1", "url-loader": "^2.0.0", "vue-loader": "^15.7.0", "vue-template-compiler": "^2.6.10", "webpack": "^4.34.0", "webpack-cli": "^3.3.5", "webpack-dev-server": "^3.7.2"}, "dependencies": {"@aspnet/signalr": "^1.0.27", "axios": "^0.18.0", "bip39": "^2.5.0", "bitcoinjs-lib": "^4.0.2", "clipboard": "^2.0.11", "countup": "^1.8.2", "echarts": "^4.0.4", "ethereumjs-tx": "^1.3.7", "ethereumjs-wallet": "^0.6.3", "html2canvas": "^1.0.0-rc.3", "jquery": "^3.3.1", "js-cookie": "^2.2.0", "lightweight-charts": "^3.8.0", "lodash": "^4.17.11", "print-js": "^1.0.54", "qrcode": "^1.3.3", "qs": "^6.7.0", "swiper": "^4.5.1", "tron-wallet-hd": "2.0.2", "tronweb": "^4.3.0", "vant": "^2.12.48", "vue": "^2.6.10", "vue-cli": "^2.9.6", "vue-i18n": "^7.8.0", "vue-jsonp": "^0.1.8", "vue-print-nb": "^1.0.3", "vue-router": "^3.0.1", "vuedraggable": "^2.24.3", "vuex": "^3.0.1", "wangeditor": "^3.1.1", "web3": "^1.7.4", "xlsx": "^0.13.5"}}