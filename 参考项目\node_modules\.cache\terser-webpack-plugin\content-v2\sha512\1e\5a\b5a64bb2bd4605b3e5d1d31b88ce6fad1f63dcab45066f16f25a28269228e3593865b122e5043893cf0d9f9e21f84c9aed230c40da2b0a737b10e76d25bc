{"code": "(window.webpackJsonp=window.webpackJsonp||[]).push([[13],{640:function(t,e,s){\"use strict\";s.r(e);var a=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"recharge\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"img\",{staticClass:\"jsx-78668d0f5e0b59ae absolute bottom-0 right-0 w-full h-full object-contain pointer-events-none select-none\",staticStyle:{width:\"100%\",height:\"100%\",\"object-fit\":\"contain\"},attrs:{src:s(667),alt:\"\",draggable:\"false\"}}),t._v(\" \"),a(\"div\",{staticClass:\"cot\"},[a(\"h2\",{staticClass:\"flex\"},[t._v(\"Withdraw \"),a(\"div\",[0==t.userinfo.status?a(\"button\",{staticClass:\"btn btn-primary btn-sm cursor-pointer\",staticStyle:{\"margin-left\":\"0.32rem\"},attrs:{type:\"button\"},on:{click:function(e){return t.showShouquan(!1)}}},[t._v(\"Sign in to show details\")]):t._e()])]),t._v(\" \"),0==t.userinfo.status?a(\"div\",{staticClass:\"ant-skeleton p-8 ant-skeleton-active bg-[rgba(255,255,255,0.85)] [box-shadow:-10px_-10px_10px_8px_rgba(227,230,255,0.25)] rounded-[32px]\",staticStyle:{\"margin-bottom\":\"0.64rem\"}},[t._m(0)]):t._e(),t._v(\" \"),0==t.userinfo.status?a(\"div\",{staticClass:\"ant-skeleton  p-8 ant-skeleton-active bg-[rgba(255,255,255,0.85)] [box-shadow:-10px_-10px_10px_8px_rgba(227,230,255,0.25)] rounded-[32px]\"},[t._m(1)]):t._e(),t._v(\" \"),1==t.userinfo.status?a(\"div\",{staticClass:\"gap-8 m-32 p-8 grow bg-[rgba(255,255,255,0.85)] [box-shadow:-10px_-10px_10px_8px_rgba(227,230,255,0.25)] rounded-[32px]\"},[a(\"div\",{staticClass:\"flex flex-col gap-4\"},[a(\"img\",{staticClass:\"w-16 h-16 mx-auto\",staticStyle:{margin:\"0 0.2rem\"},attrs:{src:s(685),\"data-v-4e295514\":\"\"}}),t._v(\" \"),a(\"div\",{staticClass:\"mt-2\"},[a(\"p\",{staticClass:\"text-center text-2xl m-0\"},[t._v(t._s(t.walletInfo.balance>0?t.walletInfo.balance:\"...\")+\"\\n                    \")]),t._v(\" \"),a(\"h3\",{staticClass:\"text-center text-sm m-0\"},[t._v(\"Available amount\")])])])]):t._e(),t._v(\" \"),1==t.userinfo.status?a(\"div\",{staticClass:\"gap-8 m-32 p-8 grow bg-[rgba(255,255,255,0.85)] [box-shadow:-10px_-10px_10px_8px_rgba(227,230,255,0.25)] rounded-[32px]\"},[a(\"div\",{staticClass:\"flex \"},[t._m(2),t._v(\" \"),a(\"input\",{directives:[{name:\"model\",rawName:\"v-model\",value:t.num.sendNum,expression:\"num.sendNum\"}],staticClass:\"ant-input\",attrs:{type:\"text\",placeholder:t.$t(\"key9\"),readonly:\"\",\"data-v-4e295514\":\"\"},domProps:{value:t.num.sendNum},on:{click:function(e){return t.changeshownumberKey(!0)},input:function(e){e.target.composing||t.$set(t.num,\"sendNum\",e.target.value)}}}),t._v(\" \"),a(\"div\",{staticClass:\"ant-input-group-addon\"},[a(\"button\",{staticClass:\"font-bold text-black border-0 bg-transparent\",staticStyle:{padding:\"1px 6px\"},on:{click:function(e){t.num.sendNum=t.walletInfo.balance}}},[t._v(\"All\")])])]),t._v(\" \"),a(\"button\",{staticClass:\"btn btn-primary mt-8 block w-full\",attrs:{type:\"button\"},on:{click:t.withdraw}},[t._v(\"withdraw\")])]):t._e(),t._v(\" \"),a(\"h2\",{staticClass:\"mb-4 mt-16\"},[t._v(\"Withdraw History\")]),t._v(\" \"),0==t.userinfo.status?a(\"div\",{staticClass:\"ant-skeleton  p-8 ant-skeleton-active bg-[rgba(255,255,255,0.85)] [box-shadow:-10px_-10px_10px_8px_rgba(227,230,255,0.25)] rounded-[32px]\"},[t._m(3)]):t._e(),t._v(\" \"),1==t.userinfo.status?a(\"div\",{staticClass:\"list\"},[a(\"table\",{staticClass:\"w-full table-fixed\"},[t._m(4),t._v(\" \"),a(\"tbody\",{staticClass:\" text-sm\"},t._l(t.list,function(e,s){return a(\"tr\",[a(\"td\",{staticClass:\" text-left\"},[t._v(t._s(t.timetrans(e.withdraw_time)))]),t._v(\" \"),a(\"td\",{staticClass:\" text-right\"},[t._v(t._s(e.real_amount))]),t._v(\" \"),a(\"td\",{staticClass:\" text-right\"},[t._v(t._s(t.zhuantai(e.status)))]),t._v(\" \"),a(\"td\",{staticClass:\" text-right\"},[t._v(t._s(e.remark))])])}),0)])]):t._e()]),t._v(\" \"),a(\"van-number-keyboard\",{attrs:{theme:\"custom\",\"extra-key\":\".\",\"close-button-text\":\"Close\",show:t.shownumberKey},on:{blur:function(e){t.shownumberKey=!1},input:t.onInput,delete:t.onDelete}}),t._v(\" \"),t.shouquanModal?a(\"approve\",{attrs:{isShowToast:t.isShowToast},model:{value:t.shouquanModal,callback:function(e){t.shouquanModal=e},expression:\"shouquanModal\"}}):t._e()],1)},i=[function(){var t=this.$createElement,e=this._self._c||t;return e(\"div\",{staticClass:\"ant-skeleton-content\"},[e(\"h3\",{staticClass:\"ant-skeleton-title\",staticStyle:{width:\"38%\"}}),this._v(\" \"),e(\"ul\",{staticClass:\"ant-skeleton-paragraph\"},[e(\"li\"),this._v(\" \"),e(\"li\"),this._v(\" \"),e(\"li\",{staticStyle:{width:\"61%\"}})])])},function(){var t=this.$createElement,e=this._self._c||t;return e(\"div\",{staticClass:\"ant-skeleton-content\"},[e(\"h3\",{staticClass:\"ant-skeleton-title\",staticStyle:{width:\"38%\"}}),this._v(\" \"),e(\"ul\",{staticClass:\"ant-skeleton-paragraph\"},[e(\"li\"),this._v(\" \"),e(\"li\"),this._v(\" \"),e(\"li\",{staticStyle:{width:\"61%\"}})])])},function(){var t=this.$createElement,e=this._self._c||t;return e(\"div\",{staticClass:\"ant-input-group-addon\"},[e(\"img\",{staticClass:\"w-5 h-5\",attrs:{src:s(685),\"data-v-4e295514\":\"\"}})])},function(){var t=this.$createElement,e=this._self._c||t;return e(\"div\",{staticClass:\"ant-skeleton-content\"},[e(\"h3\",{staticClass:\"ant-skeleton-title\",staticStyle:{width:\"38%\"}}),this._v(\" \"),e(\"ul\",{staticClass:\"ant-skeleton-paragraph\"},[e(\"li\"),this._v(\" \"),e(\"li\"),this._v(\" \"),e(\"li\",{staticStyle:{width:\"61%\"}})])])},function(){var t=this.$createElement,e=this._self._c||t;return e(\"thead\",[e(\"tr\",[e(\"th\",{staticClass:\"  text-left\"},[this._v(\"Date\")]),this._v(\" \"),e(\"th\",{staticClass:\" text-right\"},[this._v(\"USDC\")]),this._v(\" \"),e(\"th\",{staticClass:\" text-right\"},[this._v(\"Status\")]),this._v(\" \"),e(\"th\",{staticClass:\" text-right\"},[this._v(\"Remark\")])])])}];a._withStripped=!0;var n=s(659),r=s(156),o=s.n(r),l=s(155),c=s(34),h=s(732),u=s.n(h),d={props:{},components:{tab:n.default,approve:l.default},data:()=>({fileList:[],exchange_rate:\"\",shownumberKey:!1,shouquanModal:!1,isShowToast:!1,num:{convertNum:\"\",sendNum:\"\",rechargeNumber:\"\"},tabindex:1,codeTabIndex:0,sendTabIndex:0,coin:\"\",walletInfo:{},receive_asset:[],send_asset:[],receiveAddress:\"\",sendAddress:\"\",rechargeImgBase64:\"\",file:null,list:\"\"}),computed:{userinfo(){return this.$store.state.user.userinfo},configInfo(){return this.$store.state.user.configInfo},walletObj(){return this.$store.state.user.walletObj}},mounted(){this.coin=this.$route.query.coin,this.getWalletDetails(),this.WithdrawList()},methods:{zhuantai(t){switch(t){case 0:return\"Pending approval\";case 1:return\"agree with\";case 2:return\"refuse\"}},WithdrawList(){Object(c.WithdrawList)({page:1,limit:50}).then(t=>{let e=t.data;1==e.code&&(this.list=e.data.lst)})},timetrans(t){var e=(t=new Date(1e3*t)).getFullYear()+\"-\",s=(t.getMonth()+1<10?\"0\"+(t.getMonth()+1):t.getMonth()+1)+\"-\",a=t.getDate()<10?\"0\"+t.getDate():t.getDate();t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return`${e}${s}${a}`},showShouquan(t){this.isShowToast=t,this.shouquanModal=!0},deleteFile(){this.rechargeImgBase64=\"\",this.file=null,this.fileList=[]},afterRead(t){this.fileList=[{url:t.content,isImage:!0}],this.rechargeImgBase64=t.content,this.file=t.file},recharge(){if(!this.num.rechargeNumber)return this.$toast(this.$t(\"key30\"));if(!this.file)return this.$toast(\"key95\");var t=new FormData;t.append(\"coin\",this.walletInfo.coin),t.append(\"amount\",this.num.rechargeNumber),t.append(\"image\",this.file),Object(c.recharge)(t).then(t=>{let e=t.data;this.$toast(e.msg),1===e.code&&(this.getWalletDetails(),this.num.rechargeNumber=\"\",this.deleteFile())})},withdraw(){if(!this.num.sendNum)return this.$toast(this.$t(\"key30\"));Object(c.withdraw)({coin:this.walletInfo.coin,amount:this.num.sendNum,link:this.sendAddress}).then(t=>{let e=t.data;this.$toast(e.msg),1===e.code&&(this.getWalletDetails(),this.WithdrawList(),this.num.sendNum=\"\")})},exchangeCoin(){if(!this.num.convertNum)return this.$toast(this.$t(\"key30\"));Object(c.exchangeCoin)({coin:this.walletInfo.coin,amount:this.num.convertNum}).then(t=>{let e=t.data;this.$toast(e.msg),1===e.code&&(this.num.convertNum=\"\",this.changeRate(),this.getWalletDetails())})},useqrcode(){var t=document.getElementById(\"yqrcode\");u.a.toCanvas(t,this.receiveAddress,{width:180,height:180,margin:1})},getWalletDetails(){Object(c.getWalletDetails)({coin:\"USDC\"}).then(t=>{let e=t.data;if(1===e.code){this.walletInfo=e.data,this.receive_asset=[],this.send_asset=[];for(let t in e.data.receive_asset)this.receive_asset.push({name:t,address:e.data.receive_asset[t]});for(let t in e.data.send_asset)this.send_asset.push({name:t,address:e.data.send_asset[t]});this.receiveAddress=this.receive_asset[0].address,this.sendAddress=this.send_asset[0].address}})},back(){this.$router.back()},go(t,e){this.$router.push({path:t,query:e})},changeshownumberKey(t){this.shownumberKey=t},onInput(t){this.num.sendNum+=t},onDelete(){let t=\"sendNum\";this.num[t]=this.num[t].substring(0,this.num[t].length-1)},changeRate(){this.num.convertNum?this.exchange_rate=(Number(this.num.convertNum)*this.walletInfo.exchange_rate).toFixed(4):this.exchange_rate=\"\"},copy(){var t=new o.a(\".btn-copy\");t.on(\"success\",e=>{this.$toast({message:this.$t(\"key96\"),icon:\"success\"}),t.destroy()}),t.on(\"error\",e=>{this.$toast({message:this.$t(\"key97\"),icon:\"cross\"}),t.destroy()})},changesendTabIndex(t){this.sendAddress=this.send_asset[t].address,this.sendTabIndex=t},changecodeTabIndex(t){this.receiveAddress=this.receive_asset[t].address,this.codeTabIndex=t,this.useqrcode()}}},m=(s(871),s(25)),_=Object(m.a)(d,a,i,!1,null,\"138aafe9\",null);_.options.__file=\"src/view/home/<USER>\";e.default=_.exports},653:function(t,e,s){},659:function(t,e,s){\"use strict\";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s(\"div\",{staticClass:\"switch_container\",attrs:{\"data-v-7932180b\":\"\"}},[s(\"div\",{staticClass:\"switch_content\",style:\"width: \"+t.width,attrs:{\"data-v-7932180b\":\"\"}},t._l(t.title,function(e,a){return s(\"div\",{key:a,staticClass:\"switch_item\",class:[t.value===a?\"active\":\"\"],attrs:{\"data-v-7932180b\":\"\"},on:{click:function(e){return t.changeTabindex(a)}}},[t._v(t._s(e))])}),0)])};a._withStripped=!0;var i={name:\"tab\",props:{title:{default:[]},width:{default:\"4.44rem\"},value:{default:0}},components:{},data:()=>({}),mounted(){},methods:{changeTabindex(t){this.$emit(\"input\",t)}}},n=(s(670),s(25)),r=Object(n.a)(i,a,[],!1,null,null,null);r.options.__file=\"src/components/tab.vue\";e.default=r.exports},667:function(t,e){t.exports=\"/img/content_bg_8be77b506eeee184dcb5b566cab5f619.png\"},670:function(t,e,s){\"use strict\";var a=s(653);s.n(a).a},685:function(t,e){t.exports=\"/img/usd-coin-usdc-logo-300x300_0c8e2f57245b2d52acf21e0516b5f62f.png\"},719:function(t,e,s){},871:function(t,e,s){\"use strict\";var a=s(719);s.n(a).a}}]);", "extractedComments": []}