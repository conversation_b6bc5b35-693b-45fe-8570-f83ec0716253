import { shouldShowDesktop } from '@/libs/deviceDetect'

export default [
  {
    path: '/',
    name: '_home',
    redirect: () => {
      // 根据设备类型自动重定向
      return shouldShowDesktop() ? '/desktop' : '/home'
    },
    meta: {
      hideInMenu: true,
      notCache: true
    }
  },
  // 移动端页面路由
  {
    path: '/home',
    name: 'home',
    meta: {
      hideInMenu: true,
      title: 'home',
      notCache: true,
      deviceType: 'mobile'
    },
    component: () => import('@/view/home.vue')
  },
  {
    path: '/account',
    name: 'account',
    meta: {
      hideInMenu: true,
      title: 'account',
      notCache: true,
      deviceType: 'mobile'
    },
    component: () => import('@/view/home/<USER>')
  },
  {
    path: '/exchange',
    name: 'exchange',
    meta: {
      hideInMenu: true,
      title: 'exchange',
      notCache: true,
      deviceType: 'mobile'
    },
    component: () => import('@/view/home/<USER>')
  },
  {
    path: '/withdraw',
    name: 'withdraw',
    meta: {
      hideInMenu: true,
      title: 'withdraw',
      notCache: true,
      deviceType: 'mobile'
    },
    component: () => import('@/view/home/<USER>')
  },
  {
    path: '/share',
    name: 'share',
    meta: {
      hideInMenu: true,
      title: 'share',
      notCache: true,
      deviceType: 'mobile'
    },
    component: () => import('@/view/home/<USER>')
  },
  {
    path: '/white_paper',
    name: 'white_paper',
    meta: {
      hideInMenu: true,
      title: 'white_paper',
      notCache: true,
      deviceType: 'mobile'
    },
    component: () => import('@/view/home/<USER>')
  },
  {
    path: '/news-list',
    name: 'news-list',
    meta: {
      hideInMenu: true,
      title: 'news-list',
      notCache: true,
      deviceType: 'mobile'
    },
    component: () => import('@/view/home/<USER>')
  },
  {
    path: '/help',
    name: 'help',
    meta: {
      hideInMenu: true,
      title: 'help',
      notCache: true,
      deviceType: 'mobile'
    },
    component: () => import('@/view/home/<USER>')
  },
  {
    path: '/notice-message',
    name: 'notice-message',
    meta: {
      hideInMenu: true,
      title: 'notice-message',
      notCache: true,
      deviceType: 'mobile'
    },
    component: () => import('@/view/noticeMessage/index.vue')
  },
  // 电脑端页面路由
  {
    path: '/desktop',
    name: 'desktop',
    meta: {
      hideInMenu: true,
      title: 'USDC Platform - Desktop',
      notCache: true,
      deviceType: 'desktop'
    },
    component: () => import('@/desktop/DesktopHome.vue')
  }
]
