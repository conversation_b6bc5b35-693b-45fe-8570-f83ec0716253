import { userinfo } from '@/api/user'
import { setToken, getToken } from '@/libs/util'
import { setStore, getStore, clearStore } from '@/libs/storage'
import { configapi, exchangeRate } from '@/api/user'
import { resolveConstraits } from 'vue-i18n'

export default {
  state: {
    userInfo: {},
    showNotice: false,
    currency: 'USDC',
    coinType: 'usdc',
    Hdtoken: '',
    walletlistsHome: [],
    walletTotol_price: 0,
    walletObj: {
      web3: null,
      provider: null,
      userAddress: '',
      connected: false,
      chainId: 1,
      networkId: 1
    },
    walletassets: 0,
    configInfo: {
      home: { pool: {} }
    },
    userinfo: {},
    token_id: getStore('token_id') || '',
    auth_token: getStore('auth_token') || '',
  },
  mutations: {
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo
    },
    setShowNotice(state, showNotice) {
      state.showNotice = showNotice
    },
    setCurrency(state, currency) {
      state.currency = currency
    },
    setCoinType(state, coinType) {
      state.coinType = coinType.toLowerCase()
      if (coinType.toLowerCase() === 'usdt') {
        state.currency = 'USDT'
      } else {
        state.currency = 'USDC'
      }
    },
    setHdToken(state, Hdtoken) {
      state.Hdtoken = Hdtoken
    },
    setHomeWallet(state, data) {
      state.walletlistsHome = data.list
      state.walletTotol_price = data.totol_price
    },
    setWalletObj(state, walletObj) {
      state.walletObj = walletObj
    },
    setWalletassets(state, walletassets) {
      state.walletassets = walletassets
    },
    setConfigInfo(state, configInfo) {
      state.configInfo = configInfo
    },
    setUserInfoData(state, userinfo) {
      state.userinfo = userinfo
    },
    set_token_id(state, token_id) {
      state.token_id = token_id
      setStore('token_id', token_id)
    },
    set_auth_token(state, auth_token) {
      state.auth_token = auth_token
      setStore('auth_token', auth_token)
    },
  },
  getters: {
    // 根据合约地址返回币种名称
    getCoinNameByCAddress: (state) => {
      if (!state.userinfo || !state.userinfo.contract_address) return 'USDC'; // 默认返回USDC
      
      // USDT合约地址
      const USDT_CONTRACT = '******************************************';
      // USDC合约地址
      const USDC_CONTRACT = '******************************************';
      
      // 不区分大小写比较
      if (state.userinfo.contract_address.toLowerCase() === USDT_CONTRACT.toLowerCase()) {
        return 'USDT';
      } else if (state.userinfo.contract_address.toLowerCase() === USDC_CONTRACT.toLowerCase()) {
        return 'USDC';
      } else {
        // 默认返回USDC
        return 'USDC';
      }
    }
  },
  actions: {
    setHdTokenA({ commit }, Hdtoken) {
      commit('setHdToken', Hdtoken)
    },
    setWalletObj({ commit }, walletObj) {
      commit('setWalletObj', walletObj)
    },
    setWalletassets({ commit }, walletassets) {
      commit('setWalletassets', walletassets)
    },
    setConfigInfo({ commit }, configInfo) {
      commit('setConfigInfo', configInfo)
    },
    setUserInfo({ commit }, userinfo) {
      commit('setUserInfoData', userinfo)
    },
    setCoinType({ commit }, coinType) {
      commit('setCoinType', coinType)
    },
    // 获取用户相关信息
    getuserinfo({ state, commit }) {
      return new Promise((resolve, reject) => {
        try {
          userinfo().then(res => {
            const data = res.data
            if (data.code == 1) {
              commit('setUserInfoData', data.data)
            }
            resolve(data)
          }).catch(error => {
            console.error('获取用户信息出错:', error)
            reject(error)
          })
        } catch (error) {
          console.error('getuserinfo方法错误:', error)
          reject(error)
        }
      })
    },
    // 获取配置信息
    async configapi({ commit }) {
      try {
        const res = await configapi()
        commit('setConfigInfo', res.data.data)
      } catch (error) {
        console.error('获取配置信息出错:', error)
      }
    },
    // 获取用户信息 - 重命名避免重复
    async getUserInfoData({ commit, state }) {
      return new Promise((resolve, reject) => {
        try {
          userinfo().then(res => {
            const data = res.data
            if (data.code == 1) {
              commit('setUserInfoData', data.data)
            }
            resolve(data)
          }).catch(error => {
            console.error('获取用户信息出错:', error)
            reject(error)
          })
        } catch (error) {
          console.error('getUserInfoData方法错误:', error)
          reject(error)
        }
      })
    },
    // 获取汇率
    async getExchangeRate({ commit, state }) {
      try {
        const res = await exchangeRate({})
        if (res.data.code === 1) {
          console.log('获取汇率', res.data.data)
          return res.data.data
        }
      } catch (error) {
        console.error('获取汇率出错:', error)
      }
    },
  }
}
