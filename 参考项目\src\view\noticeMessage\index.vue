<template>
  <div class="notice-message">
    <img
      src="@/assets/icons/content_bg.png"
      alt=""
      draggable="false"
      class="jsx-78668d0f5e0b59ae absolute bottom-0 right-0 w-full h-full object-contain pointer-events-none select-none"
      style="width: 100%; height: 100%; object-fit: contain"
    />
    <div class="content">
      <h2>Notice</h2>
      <!-- <div v-html="content"></div> -->
      <van-collapse v-model="activeNames">
        <van-collapse-item
          title-class="item-title"
          :name="x.token"
          v-for="x in content"
        >
          <template #title>
            <div @click="handleClick(x)">
              <van-badge :dot="!x.status">
                {{ x.title }}
              </van-badge>
            </div>
          </template>
          <div v-html="x.content"></div>
        </van-collapse-item>
        <!-- <van-collapse-item title="标题2" name="2">内容</van-collapse-item>
  <van-collapse-item title="标题3" name="3">内容</van-collapse-item> -->
      </van-collapse>
    </div>
  </div>
</template>
<script>
import { findNotices, readSystemMessage } from '@/api/user'
export default {
  name: 'NoticeMessage',
  data() {
    return {
      content: [],
      activeNames: [],
    }
  },
  mounted() {
    this.findNotices()
  },
  methods: {
    async findNotices() {
      const result = await findNotices({ type: 3 })
      console.log('result', result)
      const { data } = result
      if (data.data.length) {
        this.content = data.data
        console.log('进入')
      }
    },
    async handleClick(x) {
      console.log('handleClick', x)
      try {
        if ((x.status = 1)) return
        await readSystemMessage({ id: x.id })
        x.status = 1
      } catch (error) {
        console.log('err', error)
      }
    },
  },
}
</script>
<style scoped>
.absolute {
  position: absolute;
  z-index: 0;
}

.clock {
  width: 2.24rem;
  margin: 0.32rem 0 0.64rem;
}

.notice-message {
  padding-bottom: 1.6rem;
}

.notice-message .content {
  padding: 0.32rem 0.32rem 0;
  position: relative;
}
::v-deep .item-title {
  font-size: 18px;
  font-weight: 600;
}
.notice-message .content h2 {
  font-size: 0.6rem;
  color: #294ae0;
  margin-bottom: 0.32rem;
  padding: 0.64rem 0;
  /* text-align: center; */
  margin-top: 0;
}
</style>
