﻿html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  font-family: sans-serif;
}

body {
  margin: 0;
}

article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
  display: block;
}

audio, canvas, progress, video {
  vertical-align: baseline;
  display: inline-block;
}

audio:not([controls]) {
  height: 0;
  display: none;
}

[hidden], template {
  display: none;
}

a {
  background-color: #0000;
}

a:active, a:hover {
  outline: 0;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b, strong {
  font-weight: bold;
}

dfn {
  font-style: italic;
}

h1 {
  margin: .67em 0;
  font-size: 2em;
}

mark {
  color: #000;
  background: #ff0;
}

small {
  font-size: 80%;
}

sub, sup {
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0;
  position: relative;
}

sup {
  top: -.5em;
}

sub {
  bottom: -.25em;
}

img {
  border: 0;
}

svg:not(:root) {
  overflow: hidden;
}

hr {
  box-sizing: content-box;
  height: 0;
}

pre {
  overflow: auto;
}

code, kbd, pre, samp {
  font-family: monospace;
  font-size: 1em;
}

button, input, optgroup, select, textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}

button {
  overflow: visible;
}

button, select {
  text-transform: none;
}

button, html input[type="button"], input[type="reset"] {
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled], html input[disabled] {
  cursor: default;
}

button::-moz-focus-inner, input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input {
  line-height: normal;
}

input[type="checkbox"], input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}

input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

input[type="search"] {
  -webkit-appearance: none;
}

input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

legend {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
}

optgroup {
  font-weight: bold;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td, th {
  padding: 0;
}

@font-face {
  font-family: webflow-icons;
  src: url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype");
  font-weight: normal;
  font-style: normal;
}

[class^="w-icon-"], [class*=" w-icon-"] {
  speak: none;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  font-family: webflow-icons !important;
}

.w-icon-slider-right:before {
  content: "";
}

.w-icon-slider-left:before {
  content: "";
}

.w-icon-nav-menu:before {
  content: "";
}

.w-icon-arrow-down:before, .w-icon-dropdown-toggle:before {
  content: "";
}

.w-icon-file-upload-remove:before {
  content: "";
}

.w-icon-file-upload-icon:before {
  content: "";
}

* {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  color: #333;
  background-color: #fff;
  min-height: 100%;
  margin: 0;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 20px;
}

img {
  vertical-align: middle;
  max-width: 100%;
  display: inline-block;
}

html.w-mod-touch * {
  background-attachment: scroll !important;
}

.w-block {
  display: block;
}

.w-inline-block {
  max-width: 100%;
  display: inline-block;
}

.w-clearfix:before, .w-clearfix:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-clearfix:after {
  clear: both;
}

.w-hidden {
  display: none;
}

.w-button {
  color: #fff;
  line-height: inherit;
  cursor: pointer;
  background-color: #3898ec;
  border: 0;
  border-radius: 0;
  padding: 9px 15px;
  text-decoration: none;
  display: inline-block;
}

input.w-button {
  -webkit-appearance: button;
}

html[data-w-dynpage] [data-w-cloak] {
  color: #0000 !important;
}

.w-code-block {
  margin: unset;
}

pre.w-code-block code {
  all: inherit;
}

.w-optimization {
  display: contents;
}

.w-webflow-badge, .w-webflow-badge > img {
  box-sizing: unset;
  width: unset;
  height: unset;
  max-height: unset;
  max-width: unset;
  min-height: unset;
  min-width: unset;
  margin: unset;
  padding: unset;
  float: unset;
  clear: unset;
  border: unset;
  border-radius: unset;
  background: unset;
  background-image: unset;
  background-position: unset;
  background-size: unset;
  background-repeat: unset;
  background-origin: unset;
  background-clip: unset;
  background-attachment: unset;
  background-color: unset;
  box-shadow: unset;
  transform: unset;
  direction: unset;
  font-family: unset;
  font-weight: unset;
  color: unset;
  font-size: unset;
  line-height: unset;
  font-style: unset;
  font-variant: unset;
  text-align: unset;
  letter-spacing: unset;
  -webkit-text-decoration: unset;
  text-decoration: unset;
  text-indent: unset;
  text-transform: unset;
  list-style-type: unset;
  text-shadow: unset;
  vertical-align: unset;
  cursor: unset;
  white-space: unset;
  word-break: unset;
  word-spacing: unset;
  word-wrap: unset;
  transition: unset;
}

.w-webflow-badge {
  white-space: nowrap;
  cursor: pointer;
  box-shadow: 0 0 0 1px #0000001a, 0 1px 3px #0000001a;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 2147483647 !important;
  color: #aaadb0 !important;
  overflow: unset !important;
  background-color: #fff !important;
  border-radius: 3px !important;
  width: auto !important;
  height: auto !important;
  margin: 0 !important;
  padding: 6px !important;
  font-size: 12px !important;
  line-height: 14px !important;
  text-decoration: none !important;
  display: inline-block !important;
  position: fixed !important;
  inset: auto 12px 12px auto !important;
  transform: none !important;
}

.w-webflow-badge > img {
  position: unset;
  visibility: unset !important;
  opacity: 1 !important;
  vertical-align: middle !important;
  display: inline-block !important;
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: 10px;
  font-weight: bold;
}

h1 {
  margin-top: 20px;
  font-size: 38px;
  line-height: 44px;
}

h2 {
  margin-top: 20px;
  font-size: 32px;
  line-height: 36px;
}

h3 {
  margin-top: 20px;
  font-size: 24px;
  line-height: 30px;
}

h4 {
  margin-top: 10px;
  font-size: 18px;
  line-height: 24px;
}

h5 {
  margin-top: 10px;
  font-size: 14px;
  line-height: 20px;
}

h6 {
  margin-top: 10px;
  font-size: 12px;
  line-height: 18px;
}

p {
  margin-top: 0;
  margin-bottom: 10px;
}

blockquote {
  border-left: 5px solid #e2e2e2;
  margin: 0 0 10px;
  padding: 10px 20px;
  font-size: 18px;
  line-height: 22px;
}

figure {
  margin: 0 0 10px;
}

figcaption {
  text-align: center;
  margin-top: 5px;
}

ul, ol {
  margin-top: 0;
  margin-bottom: 10px;
  padding-left: 40px;
}

.w-list-unstyled {
  padding-left: 0;
  list-style: none;
}

.w-embed:before, .w-embed:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-embed:after {
  clear: both;
}

.w-video {
  width: 100%;
  padding: 0;
  position: relative;
}

.w-video iframe, .w-video object, .w-video embed {
  border: none;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

button, [type="button"], [type="reset"] {
  cursor: pointer;
  -webkit-appearance: button;
  border: 0;
}

.w-form {
  margin: 0 0 15px;
}

.w-form-done {
  text-align: center;
  background-color: #ddd;
  padding: 20px;
  display: none;
}

.w-form-fail {
  background-color: #ffdede;
  margin-top: 10px;
  padding: 10px;
  display: none;
}

label {
  margin-bottom: 5px;
  font-weight: bold;
  display: block;
}

.w-input, .w-select {
  color: #333;
  vertical-align: middle;
  background-color: #fff;
  border: 1px solid #ccc;
  width: 100%;
  height: 38px;
  margin-bottom: 10px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.42857;
  display: block;
}

.w-input::placeholder, .w-select::placeholder {
  color: #999;
}

.w-input:focus, .w-select:focus {
  border-color: #3898ec;
  outline: 0;
}

.w-input[disabled], .w-select[disabled], .w-input[readonly], .w-select[readonly], fieldset[disabled] .w-input, fieldset[disabled] .w-select {
  cursor: not-allowed;
}

.w-input[disabled]:not(.w-input-disabled), .w-select[disabled]:not(.w-input-disabled), .w-input[readonly], .w-select[readonly], fieldset[disabled]:not(.w-input-disabled) .w-input, fieldset[disabled]:not(.w-input-disabled) .w-select {
  background-color: #eee;
}

textarea.w-input, textarea.w-select {
  height: auto;
}

.w-select {
  background-color: #f3f3f3;
}

.w-select[multiple] {
  height: auto;
}

.w-form-label {
  cursor: pointer;
  margin-bottom: 0;
  font-weight: normal;
  display: inline-block;
}

.w-radio {
  margin-bottom: 5px;
  padding-left: 20px;
  display: block;
}

.w-radio:before, .w-radio:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-radio:after {
  clear: both;
}

.w-radio-input {
  float: left;
  margin: 3px 0 0 -20px;
  line-height: normal;
}

.w-file-upload {
  margin-bottom: 10px;
  display: block;
}

.w-file-upload-input {
  opacity: 0;
  z-index: -100;
  width: .1px;
  height: .1px;
  position: absolute;
  overflow: hidden;
}

.w-file-upload-default, .w-file-upload-uploading, .w-file-upload-success {
  color: #333;
  display: inline-block;
}

.w-file-upload-error {
  margin-top: 10px;
  display: block;
}

.w-file-upload-default.w-hidden, .w-file-upload-uploading.w-hidden, .w-file-upload-error.w-hidden, .w-file-upload-success.w-hidden {
  display: none;
}

.w-file-upload-uploading-btn {
  cursor: pointer;
  background-color: #fafafa;
  border: 1px solid #ccc;
  margin: 0;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: normal;
  display: flex;
}

.w-file-upload-file {
  background-color: #fafafa;
  border: 1px solid #ccc;
  flex-grow: 1;
  justify-content: space-between;
  margin: 0;
  padding: 8px 9px 8px 11px;
  display: flex;
}

.w-file-upload-file-name {
  font-size: 14px;
  font-weight: normal;
  display: block;
}

.w-file-remove-link {
  cursor: pointer;
  width: auto;
  height: auto;
  margin-top: 3px;
  margin-left: 10px;
  padding: 3px;
  display: block;
}

.w-icon-file-upload-remove {
  margin: auto;
  font-size: 10px;
}

.w-file-upload-error-msg {
  color: #ea384c;
  padding: 2px 0;
  display: inline-block;
}

.w-file-upload-info {
  padding: 0 12px;
  line-height: 38px;
  display: inline-block;
}

.w-file-upload-label {
  cursor: pointer;
  background-color: #fafafa;
  border: 1px solid #ccc;
  margin: 0;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: normal;
  display: inline-block;
}

.w-icon-file-upload-icon, .w-icon-file-upload-uploading {
  width: 20px;
  margin-right: 8px;
  display: inline-block;
}

.w-icon-file-upload-uploading {
  height: 20px;
}

.w-container {
  max-width: 940px;
  margin-left: auto;
  margin-right: auto;
}

.w-container:before, .w-container:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-container:after {
  clear: both;
}

.w-container .w-row {
  margin-left: -10px;
  margin-right: -10px;
}

.w-row:before, .w-row:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-row:after {
  clear: both;
}

.w-row .w-row {
  margin-left: 0;
  margin-right: 0;
}

.w-col {
  float: left;
  width: 100%;
  min-height: 1px;
  padding-left: 10px;
  padding-right: 10px;
  position: relative;
}

.w-col .w-col {
  padding-left: 0;
  padding-right: 0;
}

.w-col-1 {
  width: 8.33333%;
}

.w-col-2 {
  width: 16.6667%;
}

.w-col-3 {
  width: 25%;
}

.w-col-4 {
  width: 33.3333%;
}

.w-col-5 {
  width: 41.6667%;
}

.w-col-6 {
  width: 50%;
}

.w-col-7 {
  width: 58.3333%;
}

.w-col-8 {
  width: 66.6667%;
}

.w-col-9 {
  width: 75%;
}

.w-col-10 {
  width: 83.3333%;
}

.w-col-11 {
  width: 91.6667%;
}

.w-col-12 {
  width: 100%;
}

.w-hidden-main {
  display: none !important;
}

@media screen and (max-width: 991px) {
  .w-container {
    max-width: 728px;
  }

  .w-hidden-main {
    display: inherit !important;
  }

  .w-hidden-medium {
    display: none !important;
  }

  .w-col-medium-1 {
    width: 8.33333%;
  }

  .w-col-medium-2 {
    width: 16.6667%;
  }

  .w-col-medium-3 {
    width: 25%;
  }

  .w-col-medium-4 {
    width: 33.3333%;
  }

  .w-col-medium-5 {
    width: 41.6667%;
  }

  .w-col-medium-6 {
    width: 50%;
  }

  .w-col-medium-7 {
    width: 58.3333%;
  }

  .w-col-medium-8 {
    width: 66.6667%;
  }

  .w-col-medium-9 {
    width: 75%;
  }

  .w-col-medium-10 {
    width: 83.3333%;
  }

  .w-col-medium-11 {
    width: 91.6667%;
  }

  .w-col-medium-12 {
    width: 100%;
  }

  .w-col-stack {
    width: 100%;
    left: auto;
    right: auto;
  }
}

@media screen and (max-width: 767px) {
  .w-hidden-main, .w-hidden-medium {
    display: inherit !important;
  }

  .w-hidden-small {
    display: none !important;
  }

  .w-row, .w-container .w-row {
    margin-left: 0;
    margin-right: 0;
  }

  .w-col {
    width: 100%;
    left: auto;
    right: auto;
  }

  .w-col-small-1 {
    width: 8.33333%;
  }

  .w-col-small-2 {
    width: 16.6667%;
  }

  .w-col-small-3 {
    width: 25%;
  }

  .w-col-small-4 {
    width: 33.3333%;
  }

  .w-col-small-5 {
    width: 41.6667%;
  }

  .w-col-small-6 {
    width: 50%;
  }

  .w-col-small-7 {
    width: 58.3333%;
  }

  .w-col-small-8 {
    width: 66.6667%;
  }

  .w-col-small-9 {
    width: 75%;
  }

  .w-col-small-10 {
    width: 83.3333%;
  }

  .w-col-small-11 {
    width: 91.6667%;
  }

  .w-col-small-12 {
    width: 100%;
  }
}

@media screen and (max-width: 479px) {
  .w-container {
    max-width: none;
  }

  .w-hidden-main, .w-hidden-medium, .w-hidden-small {
    display: inherit !important;
  }

  .w-hidden-tiny {
    display: none !important;
  }

  .w-col {
    width: 100%;
  }

  .w-col-tiny-1 {
    width: 8.33333%;
  }

  .w-col-tiny-2 {
    width: 16.6667%;
  }

  .w-col-tiny-3 {
    width: 25%;
  }

  .w-col-tiny-4 {
    width: 33.3333%;
  }

  .w-col-tiny-5 {
    width: 41.6667%;
  }

  .w-col-tiny-6 {
    width: 50%;
  }

  .w-col-tiny-7 {
    width: 58.3333%;
  }

  .w-col-tiny-8 {
    width: 66.6667%;
  }

  .w-col-tiny-9 {
    width: 75%;
  }

  .w-col-tiny-10 {
    width: 83.3333%;
  }

  .w-col-tiny-11 {
    width: 91.6667%;
  }

  .w-col-tiny-12 {
    width: 100%;
  }
}

.w-widget {
  position: relative;
}

.w-widget-map {
  width: 100%;
  height: 400px;
}

.w-widget-map label {
  width: auto;
  display: inline;
}

.w-widget-map img {
  max-width: inherit;
}

.w-widget-map .gm-style-iw {
  text-align: center;
}

.w-widget-map .gm-style-iw > button {
  display: none !important;
}

.w-widget-twitter {
  overflow: hidden;
}

.w-widget-twitter-count-shim {
  vertical-align: top;
  text-align: center;
  background: #fff;
  border: 1px solid #758696;
  border-radius: 3px;
  width: 28px;
  height: 20px;
  display: inline-block;
  position: relative;
}

.w-widget-twitter-count-shim * {
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
}

.w-widget-twitter-count-shim .w-widget-twitter-count-inner {
  text-align: center;
  color: #999;
  font-family: serif;
  font-size: 15px;
  line-height: 12px;
  position: relative;
}

.w-widget-twitter-count-shim .w-widget-twitter-count-clear {
  display: block;
  position: relative;
}

.w-widget-twitter-count-shim.w--large {
  width: 36px;
  height: 28px;
}

.w-widget-twitter-count-shim.w--large .w-widget-twitter-count-inner {
  font-size: 18px;
  line-height: 18px;
}

.w-widget-twitter-count-shim:not(.w--vertical) {
  margin-left: 5px;
  margin-right: 8px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large {
  margin-left: 6px;
}

.w-widget-twitter-count-shim:not(.w--vertical):before, .w-widget-twitter-count-shim:not(.w--vertical):after {
  content: " ";
  pointer-events: none;
  border: solid #0000;
  width: 0;
  height: 0;
  position: absolute;
  top: 50%;
  left: 0;
}

.w-widget-twitter-count-shim:not(.w--vertical):before {
  border-width: 4px;
  border-color: #75869600 #5d6c7b #75869600 #75869600;
  margin-top: -4px;
  margin-left: -9px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large:before {
  border-width: 5px;
  margin-top: -5px;
  margin-left: -10px;
}

.w-widget-twitter-count-shim:not(.w--vertical):after {
  border-width: 4px;
  border-color: #fff0 #fff #fff0 #fff0;
  margin-top: -4px;
  margin-left: -8px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large:after {
  border-width: 5px;
  margin-top: -5px;
  margin-left: -9px;
}

.w-widget-twitter-count-shim.w--vertical {
  width: 61px;
  height: 33px;
  margin-bottom: 8px;
}

.w-widget-twitter-count-shim.w--vertical:before, .w-widget-twitter-count-shim.w--vertical:after {
  content: " ";
  pointer-events: none;
  border: solid #0000;
  width: 0;
  height: 0;
  position: absolute;
  top: 100%;
  left: 50%;
}

.w-widget-twitter-count-shim.w--vertical:before {
  border-width: 5px;
  border-color: #5d6c7b #75869600 #75869600;
  margin-left: -5px;
}

.w-widget-twitter-count-shim.w--vertical:after {
  border-width: 4px;
  border-color: #fff #fff0 #fff0;
  margin-left: -4px;
}

.w-widget-twitter-count-shim.w--vertical .w-widget-twitter-count-inner {
  font-size: 18px;
  line-height: 22px;
}

.w-widget-twitter-count-shim.w--vertical.w--large {
  width: 76px;
}

.w-background-video {
  color: #fff;
  height: 500px;
  position: relative;
  overflow: hidden;
}

.w-background-video > video {
  object-fit: cover;
  z-index: -100;
  background-position: 50%;
  background-size: cover;
  width: 100%;
  height: 100%;
  margin: auto;
  position: absolute;
  inset: -100%;
}

.w-background-video > video::-webkit-media-controls-start-playback-button {
  -webkit-appearance: none;
  display: none !important;
}

.w-background-video--control {
  background-color: #0000;
  padding: 0;
  position: absolute;
  bottom: 1em;
  right: 1em;
}

.w-background-video--control > [hidden] {
  display: none !important;
}

.w-slider {
  text-align: center;
  clear: both;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  background: #ddd;
  height: 300px;
  position: relative;
}

.w-slider-mask {
  z-index: 1;
  white-space: nowrap;
  height: 100%;
  display: block;
  position: relative;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-slide {
  vertical-align: top;
  white-space: normal;
  text-align: left;
  width: 100%;
  height: 100%;
  display: inline-block;
  position: relative;
}

.w-slider-nav {
  z-index: 2;
  text-align: center;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  height: 40px;
  margin: auto;
  padding-top: 10px;
  position: absolute;
  inset: auto 0 0;
}

.w-slider-nav.w-round > div {
  border-radius: 100%;
}

.w-slider-nav.w-num > div {
  font-size: inherit;
  line-height: inherit;
  width: auto;
  height: auto;
  padding: .2em .5em;
}

.w-slider-nav.w-shadow > div {
  box-shadow: 0 0 3px #3336;
}

.w-slider-nav-invert {
  color: #fff;
}

.w-slider-nav-invert > div {
  background-color: #2226;
}

.w-slider-nav-invert > div.w-active {
  background-color: #222;
}

.w-slider-dot {
  cursor: pointer;
  background-color: #fff6;
  width: 1em;
  height: 1em;
  margin: 0 3px .5em;
  transition: background-color .1s, color .1s;
  display: inline-block;
  position: relative;
}

.w-slider-dot.w-active {
  background-color: #fff;
}

.w-slider-dot:focus {
  outline: none;
  box-shadow: 0 0 0 2px #fff;
}

.w-slider-dot:focus.w-active {
  box-shadow: none;
}

.w-slider-arrow-left, .w-slider-arrow-right {
  cursor: pointer;
  color: #fff;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  -webkit-user-select: none;
  user-select: none;
  width: 80px;
  margin: auto;
  font-size: 40px;
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.w-slider-arrow-left [class^="w-icon-"], .w-slider-arrow-right [class^="w-icon-"], .w-slider-arrow-left [class*=" w-icon-"], .w-slider-arrow-right [class*=" w-icon-"] {
  position: absolute;
}

.w-slider-arrow-left:focus, .w-slider-arrow-right:focus {
  outline: 0;
}

.w-slider-arrow-left {
  z-index: 3;
  right: auto;
}

.w-slider-arrow-right {
  z-index: 4;
  left: auto;
}

.w-icon-slider-left, .w-icon-slider-right {
  width: 1em;
  height: 1em;
  margin: auto;
  inset: 0;
}

.w-slider-aria-label {
  clip: rect(0 0 0 0);
  border: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.w-slider-force-show {
  display: block !important;
}

.w-dropdown {
  text-align: left;
  z-index: 900;
  margin-left: auto;
  margin-right: auto;
  display: inline-block;
  position: relative;
}

.w-dropdown-btn, .w-dropdown-toggle, .w-dropdown-link {
  vertical-align: top;
  color: #222;
  text-align: left;
  white-space: nowrap;
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  text-decoration: none;
  position: relative;
}

.w-dropdown-toggle {
  -webkit-user-select: none;
  user-select: none;
  cursor: pointer;
  padding-right: 40px;
  display: inline-block;
}

.w-dropdown-toggle:focus {
  outline: 0;
}

.w-icon-dropdown-toggle {
  width: 1em;
  height: 1em;
  margin: auto 20px auto auto;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
}

.w-dropdown-list {
  background: #ddd;
  min-width: 100%;
  display: none;
  position: absolute;
}

.w-dropdown-list.w--open {
  display: block;
}

.w-dropdown-link {
  color: #222;
  padding: 10px 20px;
  display: block;
}

.w-dropdown-link.w--current {
  color: #0082f3;
}

.w-dropdown-link:focus {
  outline: 0;
}

@media screen and (max-width: 767px) {
  .w-nav-brand {
    padding-left: 10px;
  }
}

.w-lightbox-backdrop {
  cursor: auto;
  letter-spacing: normal;
  text-indent: 0;
  text-shadow: none;
  text-transform: none;
  visibility: visible;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  color: #fff;
  text-align: center;
  z-index: 2000;
  opacity: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -webkit-tap-highlight-color: transparent;
  background: #000000e6;
  outline: 0;
  font-family: Helvetica Neue, Helvetica, Ubuntu, Segoe UI, Verdana, sans-serif;
  font-size: 17px;
  font-style: normal;
  font-weight: 300;
  line-height: 1.2;
  list-style: disc;
  position: fixed;
  inset: 0;
  -webkit-transform: translate(0);
}

.w-lightbox-backdrop, .w-lightbox-container {
  -webkit-overflow-scrolling: touch;
  height: 100%;
  overflow: auto;
}

.w-lightbox-content {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.w-lightbox-view {
  opacity: 0;
  width: 100vw;
  height: 100vh;
  position: absolute;
}

.w-lightbox-view:before {
  content: "";
  height: 100vh;
}

.w-lightbox-group, .w-lightbox-group .w-lightbox-view, .w-lightbox-group .w-lightbox-view:before {
  height: 86vh;
}

.w-lightbox-frame, .w-lightbox-view:before {
  vertical-align: middle;
  display: inline-block;
}

.w-lightbox-figure {
  margin: 0;
  position: relative;
}

.w-lightbox-group .w-lightbox-figure {
  cursor: pointer;
}

.w-lightbox-img {
  width: auto;
  max-width: none;
  height: auto;
}

.w-lightbox-image {
  float: none;
  max-width: 100vw;
  max-height: 100vh;
  display: block;
}

.w-lightbox-group .w-lightbox-image {
  max-height: 86vh;
}

.w-lightbox-caption {
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #0006;
  padding: .5em 1em;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-lightbox-embed {
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0;
}

.w-lightbox-control {
  cursor: pointer;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 24px;
  width: 4em;
  transition: all .3s;
  position: absolute;
  top: 0;
}

.w-lightbox-left {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii0yMCAwIDI0IDQwIiB3aWR0aD0iMjQiIGhlaWdodD0iNDAiPjxnIHRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHBhdGggZD0ibTAgMGg1djIzaDIzdjVoLTI4eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDN2MjNoMjN2M2gtMjZ6IiBmaWxsPSIjZmZmIi8+PC9nPjwvc3ZnPg==");
  display: none;
  bottom: 0;
  left: 0;
}

.w-lightbox-right {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMjQgNDAiIHdpZHRoPSIyNCIgaGVpZ2h0PSI0MCI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMC0waDI4djI4aC01di0yM2gtMjN6IiBvcGFjaXR5PSIuNCIvPjxwYXRoIGQ9Im0xIDFoMjZ2MjZoLTN2LTIzaC0yM3oiIGZpbGw9IiNmZmYiLz48L2c+PC9zdmc+");
  display: none;
  bottom: 0;
  right: 0;
}

.w-lightbox-close {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMTggMTciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxNyI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMCAwaDd2LTdoNXY3aDd2NWgtN3Y3aC01di03aC03eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDd2LTdoM3Y3aDd2M2gtN3Y3aC0zdi03aC03eiIgZmlsbD0iI2ZmZiIvPjwvZz48L3N2Zz4=");
  background-size: 18px;
  height: 2.6em;
  right: 0;
}

.w-lightbox-strip {
  white-space: nowrap;
  padding: 0 1vh;
  line-height: 0;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: auto hidden;
}

.w-lightbox-item {
  box-sizing: content-box;
  cursor: pointer;
  width: 10vh;
  padding: 2vh 1vh;
  display: inline-block;
  -webkit-transform: translate3d(0, 0, 0);
}

.w-lightbox-active {
  opacity: .3;
}

.w-lightbox-thumbnail {
  background: #222;
  height: 10vh;
  position: relative;
  overflow: hidden;
}

.w-lightbox-thumbnail-image {
  position: absolute;
  top: 0;
  left: 0;
}

.w-lightbox-thumbnail .w-lightbox-tall {
  width: 100%;
  top: 50%;
  transform: translate(0, -50%);
}

.w-lightbox-thumbnail .w-lightbox-wide {
  height: 100%;
  left: 50%;
  transform: translate(-50%);
}

.w-lightbox-spinner {
  box-sizing: border-box;
  border: 5px solid #0006;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  margin-left: -20px;
  animation: .8s linear infinite spin;
  position: absolute;
  top: 50%;
  left: 50%;
}

.w-lightbox-spinner:after {
  content: "";
  border: 3px solid #0000;
  border-bottom-color: #fff;
  border-radius: 50%;
  position: absolute;
  inset: -4px;
}

.w-lightbox-hide {
  display: none;
}

.w-lightbox-noscroll {
  overflow: hidden;
}

@media (min-width: 768px) {
  .w-lightbox-content {
    height: 96vh;
    margin-top: 2vh;
  }

  .w-lightbox-view, .w-lightbox-view:before {
    height: 96vh;
  }

  .w-lightbox-group, .w-lightbox-group .w-lightbox-view, .w-lightbox-group .w-lightbox-view:before {
    height: 84vh;
  }

  .w-lightbox-image {
    max-width: 96vw;
    max-height: 96vh;
  }

  .w-lightbox-group .w-lightbox-image {
    max-width: 82.3vw;
    max-height: 84vh;
  }

  .w-lightbox-left, .w-lightbox-right {
    opacity: .5;
    display: block;
  }

  .w-lightbox-close {
    opacity: .8;
  }

  .w-lightbox-control:hover {
    opacity: 1;
  }
}

.w-lightbox-inactive, .w-lightbox-inactive:hover {
  opacity: 0;
}

.w-richtext:before, .w-richtext:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-richtext:after {
  clear: both;
}

.w-richtext[contenteditable="true"]:before, .w-richtext[contenteditable="true"]:after {
  white-space: initial;
}

.w-richtext ol, .w-richtext ul {
  overflow: hidden;
}

.w-richtext .w-richtext-figure-selected.w-richtext-figure-type-video div:after, .w-richtext .w-richtext-figure-selected[data-rt-type="video"] div:after, .w-richtext .w-richtext-figure-selected.w-richtext-figure-type-image div, .w-richtext .w-richtext-figure-selected[data-rt-type="image"] div {
  outline: 2px solid #2895f7;
}

.w-richtext figure.w-richtext-figure-type-video > div:after, .w-richtext figure[data-rt-type="video"] > div:after {
  content: "";
  display: none;
  position: absolute;
  inset: 0;
}

.w-richtext figure {
  max-width: 60%;
  position: relative;
}

.w-richtext figure > div:before {
  cursor: default !important;
}

.w-richtext figure img {
  width: 100%;
}

.w-richtext figure figcaption.w-richtext-figcaption-placeholder {
  opacity: .6;
}

.w-richtext figure div {
  color: #0000;
  font-size: 0;
}

.w-richtext figure.w-richtext-figure-type-image, .w-richtext figure[data-rt-type="image"] {
  display: table;
}

.w-richtext figure.w-richtext-figure-type-image > div, .w-richtext figure[data-rt-type="image"] > div {
  display: inline-block;
}

.w-richtext figure.w-richtext-figure-type-image > figcaption, .w-richtext figure[data-rt-type="image"] > figcaption {
  caption-side: bottom;
  display: table-caption;
}

.w-richtext figure.w-richtext-figure-type-video, .w-richtext figure[data-rt-type="video"] {
  width: 60%;
  height: 0;
}

.w-richtext figure.w-richtext-figure-type-video iframe, .w-richtext figure[data-rt-type="video"] iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.w-richtext figure.w-richtext-figure-type-video > div, .w-richtext figure[data-rt-type="video"] > div {
  width: 100%;
}

.w-richtext figure.w-richtext-align-center {
  clear: both;
  margin-left: auto;
  margin-right: auto;
}

.w-richtext figure.w-richtext-align-center.w-richtext-figure-type-image > div, .w-richtext figure.w-richtext-align-center[data-rt-type="image"] > div {
  max-width: 100%;
}

.w-richtext figure.w-richtext-align-normal {
  clear: both;
}

.w-richtext figure.w-richtext-align-fullwidth {
  text-align: center;
  clear: both;
  width: 100%;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}

.w-richtext figure.w-richtext-align-fullwidth > div {
  padding-bottom: inherit;
  display: inline-block;
}

.w-richtext figure.w-richtext-align-fullwidth > figcaption {
  display: block;
}

.w-richtext figure.w-richtext-align-floatleft {
  float: left;
  clear: none;
  margin-right: 15px;
}

.w-richtext figure.w-richtext-align-floatright {
  float: right;
  clear: none;
  margin-left: 15px;
}

.w-nav {
  z-index: 1000;
  background: #ddd;
  position: relative;
}

.w-nav:before, .w-nav:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-nav:after {
  clear: both;
}

.w-nav-brand {
  float: left;
  color: #333;
  text-decoration: none;
  position: relative;
}

.w-nav-link {
  vertical-align: top;
  color: #222;
  text-align: left;
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  text-decoration: none;
  display: inline-block;
  position: relative;
}

.w-nav-link.w--current {
  color: #0082f3;
}

.w-nav-menu {
  float: right;
  position: relative;
}

[data-nav-menu-open] {
  text-align: center;
  background: #c8c8c8;
  min-width: 200px;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  overflow: visible;
  display: block !important;
}

.w--nav-link-open {
  display: block;
  position: relative;
}

.w-nav-overlay {
  width: 100%;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-nav-overlay [data-nav-menu-open] {
  top: 0;
}

.w-nav[data-animation="over-left"] .w-nav-overlay {
  width: auto;
}

.w-nav[data-animation="over-left"] .w-nav-overlay, .w-nav[data-animation="over-left"] [data-nav-menu-open] {
  z-index: 1;
  top: 0;
  right: auto;
}

.w-nav[data-animation="over-right"] .w-nav-overlay {
  width: auto;
}

.w-nav[data-animation="over-right"] .w-nav-overlay, .w-nav[data-animation="over-right"] [data-nav-menu-open] {
  z-index: 1;
  top: 0;
  left: auto;
}

.w-nav-button {
  float: right;
  cursor: pointer;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  -webkit-user-select: none;
  user-select: none;
  padding: 18px;
  font-size: 24px;
  display: none;
  position: relative;
}

.w-nav-button:focus {
  outline: 0;
}

.w-nav-button.w--open {
  color: #fff;
  background-color: #c8c8c8;
}

.w-nav[data-collapse="all"] .w-nav-menu {
  display: none;
}

.w-nav[data-collapse="all"] .w-nav-button, .w--nav-dropdown-open, .w--nav-dropdown-toggle-open {
  display: block;
}

.w--nav-dropdown-list-open {
  position: static;
}

@media screen and (max-width: 991px) {
  .w-nav[data-collapse="medium"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="medium"] .w-nav-button {
    display: block;
  }
}

@media screen and (max-width: 767px) {
  .w-nav[data-collapse="small"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="small"] .w-nav-button {
    display: block;
  }

  .w-nav-brand {
    padding-left: 10px;
  }
}

@media screen and (max-width: 479px) {
  .w-nav[data-collapse="tiny"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="tiny"] .w-nav-button {
    display: block;
  }
}

.w-tabs {
  position: relative;
}

.w-tabs:before, .w-tabs:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-tabs:after {
  clear: both;
}

.w-tab-menu {
  position: relative;
}

.w-tab-link {
  vertical-align: top;
  text-align: left;
  cursor: pointer;
  color: #222;
  background-color: #ddd;
  padding: 9px 30px;
  text-decoration: none;
  display: inline-block;
  position: relative;
}

.w-tab-link.w--current {
  background-color: #c8c8c8;
}

.w-tab-link:focus {
  outline: 0;
}

.w-tab-content {
  display: block;
  position: relative;
  overflow: hidden;
}

.w-tab-pane {
  display: none;
  position: relative;
}

.w--tab-active {
  display: block;
}

@media screen and (max-width: 479px) {
  .w-tab-link {
    display: block;
  }
}

.w-ix-emptyfix:after {
  content: "";
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.w-dyn-empty {
  background-color: #ddd;
  padding: 10px;
}

.w-dyn-hide, .w-dyn-bind-empty, .w-condition-invisible {
  display: none !important;
}

.wf-layout-layout {
  display: grid;
}

@font-face {
  font-family: Esbuildtrial;
  src: url("https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/67e2b272d55a5b472d752829_ESBuildTRIAL-Regular-BF6618ac1e2c29f.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Esbuildtrial;
  src: url("https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/67e2b272d55a5b472d752825_ESBuildTRIAL-Medium-BF6618ac1e0ca90.otf") format("opentype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Esbuildtrial;
  src: url("https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/67e2b272d55a5b472d75282a_ESBuildTRIAL-Semibold-BF6618ac1dd0b22.otf") format("opentype");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

:root {
  --accent-color: #6a19fe;
  --background-color: #000;
  --font-title: Esbuildtrial, Arial, sans-serif;
  --primary-text: white;
  --h1: 6rem;
  --h1-line-height: 1.2;
  --h4-line-height: 1;
  --font-body: Inter, sans-serif;
  --secondary-text: #a3a3a3;
  --body-text: 1rem;
  --body-line-height: 1.5;
  --h4: 2rem;
  --body-small-text: .75rem;
  --h5: 1rem;
  --h3: 3rem;
  --accessible-components--dark-grey: #9b9b9b;
  --h2: 5.5rem;
  --font-button: Inter, sans-serif;
  --h2-line-height: 2;
  --h3-line-height: 3;
  --h5-line-height: 2;
  --body-small-line-height: 2;
}

.w-layout-grid {
  grid-row-gap: 16px;
  grid-column-gap: 16px;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

a {
  color: var(--accent-color);
  text-decoration: underline;
}

.hero {
  background-image: none;
  background-position: 0 0;
  background-repeat: repeat;
  background-size: auto;
  justify-content: flex-start;
  align-items: center;
  min-height: 85vh;
  display: flex;
  position: relative;
}

.container {
  text-align: left;
  width: 100%;
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 3rem;
  padding-right: 3rem;
}

.container.center {
  justify-content: center;
  align-items: flex-start;
  display: flex;
}

.container.center.down {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
}

.container.ontop {
  z-index: 1;
  position: relative;
}

.container.center-down {
  z-index: 1;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
  position: relative;
}

.container.justify-center {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.container.blog {
  padding-left: 12rem;
  padding-right: 12rem;
}

.body {
  background-color: var(--background-color);
}

.h1 {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--h1);
  line-height: var(--h1-line-height);
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: 600;
}

.h1.light {
  line-height: var(--h4-line-height);
  font-weight: 500;
}

.text-block {
  font-family: var(--font-body);
  color: var(--secondary-text);
  font-size: var(--body-text);
  line-height: var(--body-line-height);
  font-weight: 400;
}

.quick-stack {
  padding: 0;
}

.hero-content {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 54%;
  display: flex;
}

.menu {
  z-index: 999;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background-color: #0000001a;
  border-bottom: 1px solid #ffffff0d;
  width: 100%;
  position: fixed;
  inset: 0% 0% auto;
}

.menu-logo {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  place-content: start space-between;
  place-items: center start;
  display: grid;
}

.menu-container {
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  padding-bottom: 1rem;
  display: flex;
}

.text-block-2 {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--h4);
  line-height: var(--h4-line-height);
}

.menu-logo-grid {
  grid-template-rows: auto;
  grid-template-columns: auto auto;
  grid-auto-flow: row dense;
  place-items: center start;
}

.menu-link-grid {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  grid-template-rows: auto;
  grid-template-columns: auto auto auto auto;
}

.text-block-3 {
  font-family: var(--font-body);
  color: var(--primary-text);
  font-size: var(--body-text);
  line-height: var(--body-line-height);
  text-align: center;
  text-decoration: none;
}

.link-block {
  text-decoration: none;
}

.div-block {
  display: flex;
}

.primary-btn {
  background-color: var(--accent-color);
  color: var(--primary-text);
  border-radius: .5rem;
  justify-content: center;
  align-items: center;
  padding: .5rem 1rem;
  text-decoration: none;
  display: flex;
}

.secondary-btn {
  color: var(--primary-text);
  background-color: #ffffff1a;
  border-radius: .5rem;
  padding: .5rem 1rem;
  text-decoration: none;
}

.div-block-2 {
  margin-top: 2rem;
}

.section {
  position: relative;
}

.section.bg-blur {
  background-image: radial-gradient(circle farthest-side, #6a19fe33, #000 61%);
}

.section.testimonial {
  background-image: url("../image/67e2b272d55a5b472d75282f_Testimonials.png");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: auto;
}

.section.grey-bg {
  background-color: #0a0a0a;
}

.section.block {
  overflow: hidden;
}

.section.faq {
  display: none;
}

.div-block-3 {
  text-align: center;
  max-width: 40rem;
  margin-top: 6rem;
}

.divider {
  background-color: #ffffff26;
  height: .5px;
}

.bottom-divider-container {
  position: absolute;
  inset: auto 0% 0%;
}

.code-embed {
  z-index: -1;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.hero-overlay {
  z-index: 0;
  background-image: url("../image/67e2b272d55a5b472d75282c_HeroEffect.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.grid {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  border: 1px solid #ffffff1a;
  border-radius: 1rem;
  grid-template-rows: auto;
  grid-template-columns: auto auto auto;
  width: 100%;
  margin-top: 5rem;
  margin-bottom: 5rem;
  position: relative;
  overflow: hidden;
}

.multi-step-grid {
  outline-offset: 0px;
  border: 1px solid #ffffff26;
  border-radius: 1rem;
  outline: 4px solid #ffffff1a;
  flex-flow: column;
  flex: 25%;
  height: 35rem;
  padding: 0;
  transition: flex-basis .4s;
  display: flex;
  position: relative;
  overflow: hidden;
}

.multi-step-grid:hover {
  flex-basis: 100%;
}

.multi-step-grid._1 {
  flex-basis: 50%;
}

.text-block-4 {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--h4);
  line-height: var(--h1-line-height);
}

.text-block-5 {
  font-family: var(--font-body);
  color: var(--secondary-text);
  font-size: var(--body-small-text);
  line-height: var(--body-line-height);
}

.multi-step-grid-top {
  z-index: 1;
  grid-column-gap: .75rem;
  grid-row-gap: .75rem;
  background-image: linear-gradient(#000, #0000);
  grid-template-rows: auto auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  width: 100%;
  padding: 2rem 2rem 4rem;
  display: block;
  position: relative;
}

.multi-step-grid-top.flip {
  z-index: 100;
  background-image: linear-gradient(0deg, #000, #0000);
  padding-top: 4rem;
  padding-bottom: 2rem;
}

.code-embed-2 {
  position: static;
  inset: 0%;
}

.code-embed-3 {
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.code-embed-4 {
  position: absolute;
  inset: 0%;
}

.title-subtext {
  font-family: var(--font-body);
  color: var(--secondary-text);
  font-size: var(--body-text);
  line-height: var(--body-line-height);
}

.div-block-5 {
  background-color: #ffffff1a;
  width: 1px;
  height: 100%;
}

.div-block-6 {
  grid-column-gap: .75rem;
  grid-row-gap: .75rem;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  max-width: 20rem;
  display: grid;
}

.multistep-container {
  width: 100%;
  margin: 4rem auto;
}

.multistep-card-component {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  flex-flow: row;
  justify-content: space-between;
  align-items: stretch;
  display: flex;
}

.div-block-7 {
  width: 20rem;
  height: 20rem;
  position: absolute;
  inset: 0%;
}

.section-bg-content {
  z-index: 0;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  inset: 0%;
}

.div-block-8 {
  width: 20rem;
  height: 20rem;
}

.featuregrid {
  grid-column-gap: 5rem;
  grid-row-gap: 5rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  width: 100%;
}

.featuregrid.strokes {
  grid-column-gap: 3rem;
  grid-row-gap: 3rem;
  border: 1px solid #ffffff26;
  grid-template-columns: 1fr auto 1fr auto 1fr auto 1fr;
  margin-top: 4rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.featuregrid.top-border {
  border-top: 1px #ffffff1a;
  border-bottom: 1px solid #ffffff1a;
  padding-bottom: 4rem;
}

.featuregrid.bento {
  padding-bottom: 6rem;
}

.grid-3 {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  grid-template-rows: auto auto auto;
  grid-template-columns: 1fr;
}

.grid-3.alt {
  grid-row-gap: .5rem;
  padding-top: 4rem;
}

.grid-3._2row {
  grid-column-gap: .2rem;
  grid-row-gap: .2rem;
  grid-template-rows: .5fr auto;
}

.text-block-6 {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--body-text);
  font-weight: 500;
}

.div-block-9 {
  max-width: 50rem;
  margin-top: 6rem;
}

.bento-box {
  border: 1px solid #ffffff1a;
  border-radius: 1rem;
  justify-content: space-between;
  align-items: flex-end;
  height: 30rem;
  margin-top: 2rem;
  display: flex;
  position: relative;
  overflow: hidden;
}

.bento-box._16by9 {
  height: 24rem;
}

.bento-box._16by9.no-bounds {
  border-style: none;
  overflow: visible;
}

.bento-box.data {
  background-image: url("../image/67e2b272d55a5b472d75283f_Data.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.bento-box.zaps {
  background-image: url("../image/67e2b272d55a5b472d752840_Zaps.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.bento-box.use-cases {
  height: 22rem;
  margin-top: 0;
}

.bento-box.use-cases._1 {
  background-image: url("../image/67f3c59540f662d89a8a191e_Swap.jpg");
  background-position: 50% 100%;
  background-repeat: no-repeat;
  background-size: contain;
}

.bento-box.use-cases._2 {
  background-image: url("../image/67f3c59c98f153399f091f15_Aggregation.jpg");
  background-position: 0 0;
  background-size: cover;
}

.bento-box.use-cases._3 {
  background-image: url("../image/67f3c5a3ee654927b21505c7_Dex.jpg");
  background-position: 0 0;
  background-size: cover;
}

.text-block-7 {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--h4);
  line-height: var(--h1-line-height);
}

.grid-4 {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  padding-bottom: 6rem;
}

.grid-5 {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  margin-top: 4rem;
  padding-bottom: 6rem;
  position: relative;
}

.div-block-10 {
  margin-top: 1rem;
}

.div-block-11 {
  text-align: center;
  margin-top: 6rem;
}

.text-block-8 {
  font-family: var(--font-body);
  color: var(--secondary-text);
  font-size: var(--h5);
  line-height: var(--body-line-height);
}

.div-block-12 {
  text-align: center;
  max-width: 50rem;
  margin-bottom: 4rem;
}

.code-embed-5 {
  width: 100%;
  height: 30vh;
}

.grid-6 {
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

.div-block-13 {
  background-color: #fff3;
  width: .5px;
  height: 100%;
}

.div-block-14 {
  text-align: center;
  padding-top: 20rem;
  padding-bottom: 24rem;
}

.text-block-9 {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--h3);
  line-height: var(--body-line-height);
}

.grid-7 {
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  width: 100%;
  margin-top: 4em;
  margin-bottom: 6rem;
}

.grid-7.alt {
  display: none;
}

.benefit-grid {
  border: 1px solid #ffffff1a;
  border-radius: 1em;
  width: 100%;
  padding: 1em 1em 2em;
}

.benefit-grid-image {
  background-image: url("../image/67e2b272d55a5b472d752834_users.png");
  background-position: 0 0;
  background-size: auto;
  border-radius: 1em;
  justify-content: center;
  align-items: center;
  height: 10em;
  margin-bottom: 2em;
  display: flex;
}

.benefit-grid-image._1 {
  background-image: url("../image/67e2b272d55a5b472d752831_developers.png");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.benefit-grid-image._2 {
  background-image: url("../image/67e2b272d55a5b472d752833_protocols.png");
}

.list {
  color: #fff;
  padding-left: 16px;
  list-style-type: disc;
}

.grid-8 {
  grid-template-rows: auto;
  grid-template-columns: auto 1fr;
}

.image {
  margin-bottom: .5rem;
}

.grid-9 {
  grid-template-columns: 1fr;
}

.text-block-10 {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--h3);
  font-weight: 400;
}

.text-block-11 {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--h3);
}

.grid-10 {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  grid-template-columns: 1fr;
  margin-top: 2em;
}

.text-block-12, .text-block-13 {
  font-family: var(--font-body);
  color: var(--secondary-text);
  font-size: var(--body-text);
  font-weight: 400;
}

.div-block-15 {
  border-bottom: 1px solid #ffffff17;
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.grid-11 {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  color: var(--secondary-text);
  background-color: #bdbdbd12;
  border: 1px solid #ffffff0d;
  border-radius: .2rem;
  grid-template-rows: auto;
  grid-template-columns: auto auto;
  padding: .2rem 1rem .2rem .5rem;
}

.grid-11.green {
  color: #6eff86;
  background-color: #00ff2a1c;
  border-color: #00ff2b14;
  border-radius: .2rem;
}

.grid-11.blue {
  color: #6e92ff;
  background-color: #00b7ff1c;
  border-color: #008cff14;
}

.grid-11.red {
  color: #ff736e;
  background-color: #ff2b001c;
  border-color: #ff000014;
}

.div-block-16 {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  grid-template-rows: auto;
  grid-template-columns: auto auto auto;
  grid-auto-columns: 1fr;
  justify-content: flex-start;
  place-items: start;
  margin-top: 2rem;
  display: grid;
}

.div-block-16.alt {
  margin-top: 1rem;
}

.image-2 {
  opacity: .51;
  filter: brightness(200%);
  width: 1rem;
  height: 1rem;
}

.pill-text {
  font-family: var(--font-body);
  font-size: var(--body-small-text);
}

.secondary-button {
  color: #6a19fe;
  background-color: #6a19fe33;
  border: 1px solid #6a19fe;
  border-radius: .5rem;
  justify-content: center;
  align-items: center;
  padding: .5rem 1rem;
  text-decoration: none;
  display: flex;
}

.text-block-14 {
  color: #fff;
  text-align: center;
  font-family: Inter, sans-serif;
  font-size: 1rem;
  line-height: 1.5;
  text-decoration: none;
}

.explorer-content-block {
  flex-flow: column;
  justify-content: space-between;
  align-items: flex-start;
  display: flex;
}

.div-block-17 {
  margin-top: 1rem;
}

.text-block-15 {
  font-family: var(--font-body);
  font-size: var(--body-text);
  line-height: var(--body-line-height);
}

.div-block-18 {
  margin-top: 6rem;
  margin-bottom: 2rem;
}

.text-span, .text-span-2 {
  opacity: .5;
}

.grid-12 {
  grid-template: "."
  / 1fr 1fr 1fr;
  grid-auto-flow: row dense;
  place-items: start;
  margin-bottom: 6rem;
}

.testimonial-block {
  background-color: #111;
  border-radius: 1em;
  padding: 4rem 2rem 2rem;
  position: relative;
}

.text-block-16 {
  font-family: var(--font-body);
  color: var(--primary-text);
  font-size: var(--body-text);
  line-height: var(--body-line-height);
}

.image-3 {
  border-radius: 100rem;
  width: 3rem;
  height: 3rem;
}

.image-3.shift {
  filter: hue-rotate(180deg);
}

.grid-13 {
  grid-template-rows: auto;
  grid-template-columns: auto 1fr;
  margin-top: 2rem;
}

.image-4 {
  position: absolute;
  inset: 8% auto auto 4%;
}

.grid-14 {
  grid-template-rows: auto;
  grid-template-columns: 1fr;
}

.text-block-17 {
  font-family: var(--font-body);
  color: var(--secondary-text);
  font-size: var(--body-small-text);
  font-weight: 500;
}

.text-block-17.center {
  text-align: center;
}

.fs_accordion-1_item {
  border: 1px solid #ffffff1a;
  border-radius: 1rem;
  width: 100%;
  max-width: 40rem;
  overflow: hidden;
}

.fs_accordion-1_icon {
  color: #fff;
  margin-right: 0;
  font-size: 1.125rem;
  position: relative;
}

.fs_accordion-1_header {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  cursor: pointer;
  background-color: #111;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 40rem;
  padding: 1rem 1.5rem;
  display: flex;
}

.fs_accordion-1_header:focus-visible, .fs_accordion-1_header[data-wf-focus-visible] {
  outline-color: var(--accessible-components--dark-grey);
  outline-offset: 4px;
  outline-width: 2px;
  outline-style: solid;
}

.fs_accordion-1_paragraph {
  font-family: var(--font-body);
  color: var(--secondary-text);
  margin-bottom: 0;
}

.fs_accordion-1_component {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  width: 100%;
  max-width: 40rem;
  padding-bottom: 4rem;
  display: grid;
}

.fs_accordion-1_content {
  overflow: hidden;
}

.fs_accordion-1_body {
  background-color: #111;
  padding: 1rem 1.5rem;
}

.fs_accordion-1_label {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: 1.25rem;
  font-weight: 500;
}

.fs_accordion-1_embed {
  margin-bottom: 0;
}

.fs_accordion-1_message {
  color: var(--accessible-components--dark-grey);
}

.text-span-3 {
  -webkit-text-decoration-skip-ink: auto;
  text-decoration-skip-ink: auto;
  font-style: italic;
}

.text-span-4 {
  font-style: italic;
}

.image-5 {
  width: 100%;
  height: 100%;
}

.grid-15 {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  width: 100%;
  margin-top: 4rem;
  margin-bottom: 4rem;
}

.div-block-19 {
  text-align: left;
  padding-bottom: 2rem;
}

.block1, .block2 {
  border-right: 1px solid #ffffff14;
  width: 100%;
  padding-left: 2rem;
  padding-right: 2rem;
}

.block3 {
  width: 100%;
  padding-left: 2rem;
  padding-right: 2rem;
}

.text-span-5 {
  font-family: var(--font-body);
  color: var(--secondary-text);
  font-size: var(--body-small-text);
  line-height: var(--body-line-height);
}

.text-block-18 {
  margin-top: .5rem;
}

.div-block-20 {
  background-image: url("../image/6810fdfb80209d2597d3a6ed_Explorer.png");
  background-position: 50% 0;
  background-repeat: no-repeat;
  background-size: contain;
  width: 100%;
  height: 35em;
  margin-top: 4rem;
  margin-bottom: 2rem;
}

.bold-text {
  font-weight: 400;
}

.grid-16 {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  padding-top: 6rem;
}

.grid-17 {
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  margin-top: 2rem;
  padding-bottom: 6rem;
}

.grid-18 {
  grid-template-rows: auto;
  width: 100%;
  padding-top: 6rem;
  padding-bottom: 6rem;
}

.tab-link {
  font-family: var(--font-body);
  color: var(--secondary-text);
  font-size: var(--body-text);
  background-color: #000;
  border: 1px solid #ffffff1a;
  border-radius: 100rem;
}

.tab-link.w--current {
  color: var(--primary-text);
  background-color: #000;
  border-color: #fff;
}

.tabs-menu {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  grid-template-rows: auto;
  grid-template-columns: auto auto 1fr;
  grid-auto-columns: 1fr;
  place-items: center start;
  display: grid;
}

.tabs-content {
  margin-top: 3rem;
}

.tabs {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  width: 100%;
  margin-top: 2rem;
  display: grid;
}

.div-block-21 {
  width: 100%;
  padding-top: 6rem;
  padding-bottom: 6rem;
}

.grid-19 {
  grid-template-rows: auto;
}

.benefits-grid {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto auto;
}

.image-7 {
  object-fit: contain;
  width: 100%;
  height: 100%;
  padding-top: .1em;
  padding-bottom: .1em;
}

.card-header {
  background-image: url("../image/680fb3ce69410e4d22d9f41c_david-lundgren-1677087-unsplash.jpg");
  background-position: 50% 30%;
  background-size: cover;
  height: 150px;
}

.card {
  color: #b8b9c2;
  width: 100%;
  height: 100%;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
  box-shadow: 0 5px 2rem #0000004d;
}

.arrow-left {
  width: 4rem;
  height: 4rem;
  inset: auto auto -2% 0%;
}

.carousel-slider {
  height: 100%;
}

.carousel-slider._2 {
  right: -21%;
}

.carousel-slider._3 {
  right: -42%;
}

.carousel-slider._4 {
  right: -63%;
}

.card-body {
  height: 100%;
  padding: 30px;
}

.carousel-mask {
  width: 35%;
  height: 100%;
  overflow: visible;
}

.arrow-right {
  width: 4rem;
  height: 4rem;
  inset: auto 0% -2% auto;
}

.carousel {
  background-color: #0000;
  justify-content: flex-end;
  align-items: center;
  width: 100vw;
  height: 400px;
}

.carousel.is_contained {
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  height: auto;
  margin-top: 4rem;
  margin-bottom: 4rem;
  padding-bottom: 75px;
}

.btn {
  letter-spacing: 1px;
  text-transform: uppercase;
  background-color: #3862ec;
  border-radius: 5px;
  padding: 15px 25px;
  font-size: 1rem;
  transition: background-color .3s ease-out;
}

.btn:hover {
  background-color: #213ea0;
}

.inner-card {
  background-color: #ffffff0d;
  border: 1px solid #ffffff1a;
  border-radius: 1rem;
  height: 34rem;
  padding-top: 2rem;
  padding-bottom: 0;
  position: relative;
}

.card-divider {
  z-index: 1;
  background-color: #ffffff1a;
  width: 100%;
  height: 1px;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  position: relative;
}

.card-content-block {
  z-index: 1;
  min-height: 6rem;
  padding-left: 2rem;
  padding-right: 2rem;
  position: relative;
}

.div-block-25 {
  height: 3.5rem;
  margin-top: 1rem;
}

.image-8 {
  z-index: 0;
  filter: saturate(500%);
  position: absolute;
  inset: auto 0% 0%;
  transform: translate(0, -1.1rem)scale(2);
}

.image-9 {
  z-index: 1;
  position: relative;
}

.div-block-26 {
  text-align: center;
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  margin-top: 4rem;
  display: flex;
}

.image-10 {
  margin-top: 4rem;
  margin-bottom: 4rem;
}

.grid-20 {
  grid-column-gap: 4rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  margin-bottom: 4rem;
}

.h2 {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--h2);
  line-height: var(--h2);
  font-weight: 500;
}

.image-11, .image-12 {
  width: 100%;
  height: 100%;
}

.slide-nav {
  opacity: 0;
}

.div-block-27 {
  justify-content: space-between;
  align-items: flex-start;
  display: flex;
}

.image-13 {
  transform-style: preserve-3d;
  margin-top: 2rem;
  margin-bottom: 2rem;
  transform: rotateX(12deg)rotateY(4deg)rotateZ(0);
}

.div-block-28 {
  perspective-origin: 0 0;
  transform: perspective(2000px);
}

.div-block-29 {
  perspective: 606px;
  perspective-origin: 0 100%;
}

.div-block-30 {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  place-items: center;
  padding-top: 2rem;
  padding-bottom: 2rem;
  display: grid;
}

.div-block-31 {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.div-block-49 {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.footer-social {
  filter: grayscale();
}

.image-15 {
  width: 1.125em;
  height: 1.125em;
}

.image-16 {
  width: 1.125em;
  height: 1.1255em;
}

.image-17 {
  width: 1.125em;
  height: 1.125em;
}

.div-block-50 {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: auto auto auto 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.image-18 {
  margin-left: .5rem;
  transform: rotate(180deg);
}

.grid-21 {
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  width: 100%;
}

.div-block-51 {
  text-align: center;
  width: 100%;
  margin-top: 4rem;
  margin-bottom: 2rem;
  padding-left: 2rem;
  padding-right: 2rem;
}

.div-block-52 {
  margin-bottom: 2rem;
}

.div-block-53 {
  margin-top: 1.5rem;
}

.grid-22 {
  grid-template-rows: auto;
  grid-template-columns: .5fr 1fr;
  width: 100%;
}

.div-block-54 {
  text-align: left;
}

.carousel-2 {
  flex-direction: row;
  width: 100%;
  display: flex;
  overflow: hidden;
}

.logo-carousel-wrap {
  backface-visibility: hidden;
  background-color: #0000000d;
  flex: none;
  width: auto;
  margin-top: 2rem;
  margin-bottom: 2rem;
  display: flex;
  transform: perspective(1px);
}

.logo-carousel {
  grid-column-gap: 1rem;
  justify-content: flex-start;
  width: 100%;
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 0;
  list-style-type: none;
  display: flex;
  overflow: hidden;
}

.logo-carousel-item {
  flex: none;
  padding-left: .5rem;
  padding-right: .5rem;
}

.logo-link {
  filter: grayscale();
  flex-direction: column;
  justify-content: center;
  align-items: center;
  display: flex;
}

.logo-carousel-clone {
  grid-column-gap: 1rem;
  justify-content: flex-start;
  width: 100%;
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 0;
  list-style-type: none;
  display: flex;
  overflow: hidden;
}

.copy-animation {
  height: 0;
}

.logo-carousel-item-2 {
  flex: none;
  padding: 1rem;
}

.small-instructions {
  font-size: 1rem;
}

.instructions {
  color: #33333387;
  text-align: center;
  margin-top: 2rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-family: Inconsolata, monospace;
  font-size: 2rem;
  font-weight: 400;
  line-height: 1.2em;
}

.utility-page-wrap {
  justify-content: center;
  align-items: center;
  width: 100vw;
  max-width: 100%;
  height: 100vh;
  max-height: 100%;
  display: flex;
}

.utility-page-content {
  text-align: center;
  flex-direction: column;
  width: 260px;
  display: flex;
}

.heading {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--h4);
  line-height: var(--h1-line-height);
  font-weight: 500;
}

.body-2 {
  background-color: var(--background-color);
}

.link-block-2 {
  text-decoration: none;
}

.div-block-55 {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--h3);
  text-align: center;
  margin-top: 8rem;
  font-weight: 500;
}

.image-32 {
  border-radius: 1em;
  width: 100%;
  margin-top: 4rem;
}

.text-block-19 {
  font-size: var(--h2);
  line-height: var(--h1-line-height);
}

.rich-text-block {
  font-family: var(--font-body);
  color: #d8d8d8;
  text-align: left;
  margin-top: 2em;
  margin-bottom: 2rem;
  font-size: 1rem;
}

.heading-2, .heading-3, .heading-4, .heading-5, .heading-6 {
  font-family: var(--font-title);
}

.link {
  color: var(--accent-color);
}

.paragraph {
  font-size: var(--body-small-text);
  line-height: var(--body-line-height);
}

.list-2, .list-3 {
  font-size: var(--body-small-text);
}

.link-2 {
  color: var(--accent-color);
}

.block-quote {
  font-size: var(--body-text);
}

.link-block-3 {
  background-color: #ffffff0d;
  border: 1px solid #ffffff1a;
  border-radius: 1rem;
  width: 100%;
  height: 100%;
  text-decoration: none;
  overflow: hidden;
}

.blog-post-img {
  background-image: url("../image/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  height: 20em;
}

.div-block-128 {
  flex-flow: column;
  justify-content: space-between;
  align-items: flex-start;
  height: auto;
  padding: 1em 2em;
  text-decoration: none;
  display: flex;
}

.div-block-126 {
  margin-top: 1em;
  margin-bottom: 1em;
  text-decoration: none;
}

.blog-title {
  font-family: var(--font-title);
  color: var(--primary-text);
  margin-bottom: .5em;
  font-size: 1.2em;
  line-height: 1.25;
}

.opacity-50 {
  opacity: .5;
  color: var(--primary-text);
}

.div-block-127 {
  margin-top: 2rem;
}

.grid-23 {
  margin-top: 8rem;
}

.collection-list {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.collection-list-wrapper, .collection-list-wrapper-2 {
  margin-top: 8rem;
  margin-bottom: 8rem;
}

.collection-list-2 {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.heading-7 {
  font-family: var(--font-title);
  color: var(--primary-text);
  font-size: var(--h4);
  font-weight: 500;
}

.div-block-129 {
  text-align: center;
  margin-top: 8rem;
  padding-left: 8rem;
  padding-right: 8rem;
}

.text-block-20 {
  font-family: var(--font-body);
  color: var(--secondary-text);
  font-size: var(--h5);
}

.link-3, .paragraph-2 {
  color: var(--accent-color);
}

.m-nav-toggle-description {
  color: #fff;
  margin-bottom: 0;
  font-size: 18px;
  line-height: 1;
}

.m-nav-link-item {
  text-align: center;
}

.m-nav-link-item.last-item {
  padding-bottom: 135px;
}

.m-nav-link-item.first-item {
  padding-top: 65px;
}

.m-nav-list {
  z-index: 2;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  max-height: 100%;
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 0;
  list-style-type: none;
  display: block;
}

.nav-overlay-gradient-bottom {
  z-index: 4;
  background-image: linear-gradient(to top, #242423 15%, #24242300);
  height: 130px;
  position: absolute;
  bottom: 0;
  left: 0%;
  right: 0%;
}

.m-nav-toggle-bg {
  z-index: 1;
  background-image: linear-gradient(#eee0, #fff 98%);
  height: 125px;
  position: fixed;
  bottom: 0%;
  left: 0%;
  right: 0%;
}

.m-nav-toggle-inner {
  box-shadow: none;
  transform: scale3d(1none, 1none, 1none);
  transform-style: preserve-3d;
  background-color: #000;
  border-radius: 99em;
  justify-content: center;
  align-items: center;
  padding: 17px 25px;
  transition: transform .25s cubic-bezier(.215, .61, .355, 1);
  display: flex;
  position: relative;
}

.m-nav-toggle-inner:active {
  transform: scale(.88);
}

.m-nav-toggle-inner.close {
  padding: 18px;
}

.nav-overlay-gradient-top {
  z-index: 4;
  background-image: linear-gradient(#242423 15%, #24242300);
  height: 65px;
  position: absolute;
  inset: 0% 0% auto;
}

.mobile-nav {
  display: none;
  position: relative;
}

.m-nav-overlay {
  z-index: 95;
  background-color: #242423;
  align-items: flex-end;
  width: 100%;
  height: 90%;
  display: none;
  position: fixed;
  bottom: 0%;
  left: 0%;
  right: 0%;
  overflow: hidden;
}

.m-nav-content {
  justify-content: center;
  align-items: flex-end;
  width: 100%;
  display: flex;
  position: absolute;
  top: 0;
  bottom: 0;
  overflow: auto;
}

.m-nav-close-icon {
  width: 23px;
  padding: 1px;
}

.m-nav-toggle-open {
  z-index: 1;
  text-decoration: none;
  display: block;
  position: absolute;
  bottom: 55px;
}

.m-nav-toggle {
  z-index: 99;
  text-align: center;
  justify-content: center;
  align-items: center;
  display: flex;
  position: fixed;
  bottom: 0%;
  left: 0%;
  right: 0%;
}

.m-nav-link {
  color: #fff;
  font-size: 18px;
  font-weight: 400;
  line-height: 54px;
  text-decoration: none;
}

.m-nav-toggle-close {
  z-index: 1;
  text-decoration: none;
  display: none;
  position: absolute;
  bottom: 55px;
}

.code-embed-6 {
  margin-top: 2rem;
}

@media screen and (max-width: 991px) {
  .container.blog {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .hero-content {
    text-align: left;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
  }

  .menu-link-grid {
    display: none;
  }

  .code-embed {
    opacity: .5;
  }

  .multi-step-grid, .multi-step-grid:hover, .multi-step-grid._1 {
    flex: 0 auto;
  }

  .div-block-6 {
    width: 100%;
  }

  .multistep-card-component {
    flex-flow: column;
  }

  .featuregrid {
    grid-column-gap: 2rem;
    grid-row-gap: 2rem;
  }

  .featuregrid.strokes {
    border-style: none;
    grid-template-columns: 1fr auto 1fr auto;
    padding-left: 0;
  }

  .bento-box.use-cases._1 {
    background-position: 50%;
  }

  .bento-box.use-cases._2 {
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: contain;
  }

  .bento-box.use-cases._3 {
    background-position: 50%;
    background-repeat: no-repeat;
  }

  .grid-4 {
    grid-column-gap: 0rem;
    grid-row-gap: 0rem;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr;
  }

  .grid-5 {
    grid-template-columns: 1fr;
  }

  .div-block-13 {
    display: none;
  }

  .grid-7 {
    grid-template-columns: 1fr;
  }

  .grid-12 {
    grid-template-columns: 1fr 1fr;
  }

  .image-5 {
    transform: none;
  }

  .block1, .block2, .block3 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .grid-19 {
    grid-template-columns: 1fr;
  }

  .image-7, .div-block-50 {
    display: none;
  }

  .div-block-129 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .m-nav-link-item.first-item {
    font-family: var(--font-title);
    color: var(--primary-text);
    font-size: var(--body-text);
  }

  .m-nav-toggle-bg {
    background-image: linear-gradient(#eee0, #000 98%);
  }

  .m-nav-toggle-inner {
    background-color: var(--accent-color);
  }

  .mobile-nav {
    display: block;
  }
}

@media screen and (max-width: 767px) {
  .container, .container.blog {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .div-block-3 {
    margin-top: 4rem;
  }

  .grid {
    grid-template-rows: auto auto auto;
    grid-template-columns: 1fr;
  }

  .multi-step-grid {
    height: 25rem;
  }

  .div-block-5 {
    width: 100%;
    height: 1px;
  }

  .featuregrid {
    grid-column-gap: 2rem;
    grid-row-gap: 2rem;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    margin-bottom: 4rem;
  }

  .div-block-9 {
    margin-top: 4rem;
  }

  .grid-5 {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    grid-template-columns: 1fr;
  }

  .grid-15 {
    grid-template-columns: 1fr;
  }

  .block1 {
    border-bottom: 1px solid #ffffff14;
    border-right-style: none;
    padding-bottom: 2rem;
  }

  .block2 {
    border-bottom: 1px solid #ffffff14;
    border-right-style: none;
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .block3 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .carousel-slider._2 {
    right: -9%;
  }

  .carousel-slider._3 {
    right: -18%;
  }

  .carousel-slider._4 {
    right: -27%;
  }

  .carousel-mask {
    width: 60%;
  }

  .div-block-50 {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    display: none;
  }

  .collection-list, .collection-list-2 {
    grid-template-columns: 1fr;
  }

  .div-block-129 {
    padding-left: 0;
    padding-right: 0;
  }

  .m-nav-toggle-description {
    font-family: var(--font-title);
  }

  .m-nav-overlay {
    display: none;
  }
}

@media screen and (max-width: 479px) {
  .container, .container.blog {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .multi-step-grid-top {
    padding-top: 1.5rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .featuregrid {
    grid-column-gap: 2rem;
    grid-row-gap: 2rem;
  }

  .grid-12 {
    grid-template-columns: 1fr;
  }

  .div-block-20 {
    height: 10em;
  }

  .grid-16, .grid-17 {
    grid-template-columns: 1fr;
  }

  .tabs-menu {
    grid-template-columns: auto auto;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .carousel-slider {
    padding-left: 10px;
    padding-right: 10px;
  }

  .carousel-slider._2, .carousel-slider._3, .carousel-slider._4 {
    right: 0%;
  }

  .card-body {
    padding: 20px;
  }

  .carousel-mask {
    width: 100%;
  }

  .div-block-30 {
    grid-template-columns: 1fr;
    place-items: start center;
  }

  .div-block-50 {
    display: none;
  }

  .small-instructions {
    line-height: 1em;
  }
}

#w-node-aa0fb6db-bd83-453a-b665-39c159ced0c4-2d752801 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_5425096f-44c6-5ea8-31eb-89eff4bb8cb8-2d752801, #w-node-a3d69e11-a635-0d1a-0a83-3e136ffc144f-2d752801, #w-node-a3d69e11-a635-0d1a-0a83-3e136ffc1451-2d752801 {
  align-self: center;
}

#w-node-_4c3a6064-78eb-208a-7467-a7ba6c6d4a6a-2d752801 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_7168f71b-8641-a07b-601f-4bd9aaea5d7f-2d752801, #w-node-_3f777b97-32e0-734a-7855-23e85d6812e1-2d752801 {
  align-self: start;
}

#w-node-_1bbcb9c7-7be5-bab6-13f8-760a33a8e793-2d752801 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  place-self: center;
}

#w-node-_48e0f62d-e133-41cf-5a2b-86bc379b8c43-2d752801, #w-node-_796c8767-145e-a940-f0c7-66bf2a996507-2d752801, #w-node-_4b69354d-e73f-c59d-a49b-24ea597b719f-2d752801, #w-node-_8e36ebc9-901f-0f40-b3c3-8a088b927b55-2d752801, #w-node-_1ac83d59-5190-4528-83a3-4c96eaa4dae3-2d752801, #w-node-_1a5a0993-0c40-43f2-9ded-9847d7d78006-2d752801 {
  align-self: center;
}

#w-node-_26bdedef-732d-89bb-4cbe-cb27dff0287b-2d752801 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_26bdedef-732d-89bb-4cbe-cb27dff02880-2d752801, #w-node-ec14f5ed-5521-1602-4e56-5a6380543955-2d752801, #w-node-_038afe4f-892a-1b9d-65db-63ef69114f2f-2d752801 {
  align-self: center;
}

#w-node-_9d9bd8ef-4ad1-aeba-b700-c90562999d51-2d752801 {
  justify-self: start;
}

#w-node-_0b824103-edf3-1925-060f-80374296ab73-2d752801 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: end;
}

#w-node-_0b824103-edf3-1925-060f-80374296ab74-2d752801, #w-node-_0b824103-edf3-1925-060f-80374296ab76-2d752801, #w-node-_0b824103-edf3-1925-060f-80374296ab78-2d752801, #w-node-_0b824103-edf3-1925-060f-80374296ab7a-2d752801, #w-node-_2f7c612f-f286-3e9c-a996-f3899c08c6b8-9c08c6b3 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_2f7c612f-f286-3e9c-a996-f3899c08c6bc-9c08c6b3, #w-node-_2f7c612f-f286-3e9c-a996-f3899c08c6bf-9c08c6b3, #w-node-_2f7c612f-f286-3e9c-a996-f3899c08c6c2-9c08c6b3 {
  place-self: center end;
}

#w-node-_8acf5864-2905-aae3-4e5b-99e97951ef45-5999af97 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_32d57c4b-d482-08a9-3f43-8aef881f9bdc-5999af97 {
  justify-self: start;
}

#w-node-_32d57c4b-d482-08a9-3f43-8aef881f9be4-5999af97 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: end;
}

#w-node-_32d57c4b-d482-08a9-3f43-8aef881f9be5-5999af97, #w-node-_32d57c4b-d482-08a9-3f43-8aef881f9be7-5999af97, #w-node-_32d57c4b-d482-08a9-3f43-8aef881f9be9-5999af97, #w-node-_32d57c4b-d482-08a9-3f43-8aef881f9beb-5999af97 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_419c7fc0-6735-311d-489e-fccff8707d0a-2e741414 {
  justify-self: start;
}

#w-node-_419c7fc0-6735-311d-489e-fccff8707d12-2e741414 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: end;
}

#w-node-_419c7fc0-6735-311d-489e-fccff8707d13-2e741414, #w-node-_419c7fc0-6735-311d-489e-fccff8707d15-2e741414, #w-node-_419c7fc0-6735-311d-489e-fccff8707d17-2e741414, #w-node-_419c7fc0-6735-311d-489e-fccff8707d19-2e741414 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_32d57c4b-d482-08a9-3f43-8aef881f9bdc-1af79123 {
  justify-self: start;
}

#w-node-_32d57c4b-d482-08a9-3f43-8aef881f9be4-1af79123 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: end;
}

#w-node-_32d57c4b-d482-08a9-3f43-8aef881f9be5-1af79123, #w-node-_32d57c4b-d482-08a9-3f43-8aef881f9be7-1af79123, #w-node-_32d57c4b-d482-08a9-3f43-8aef881f9be9-1af79123, #w-node-_32d57c4b-d482-08a9-3f43-8aef881f9beb-1af79123 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

@media screen and (max-width: 479px) {
  #w-node-_9d9bd8ef-4ad1-aeba-b700-c90562999d51-2d752801, #w-node-_0b824103-edf3-1925-060f-80374296ab73-2d752801, #w-node-_32d57c4b-d482-08a9-3f43-8aef881f9bdc-5999af97, #w-node-_32d57c4b-d482-08a9-3f43-8aef881f9be4-5999af97, #w-node-_419c7fc0-6735-311d-489e-fccff8707d0a-2e741414, #w-node-_419c7fc0-6735-311d-489e-fccff8707d12-2e741414, #w-node-_32d57c4b-d482-08a9-3f43-8aef881f9bdc-1af79123, #w-node-_32d57c4b-d482-08a9-3f43-8aef881f9be4-1af79123 {
    justify-self: center;
  }
}
