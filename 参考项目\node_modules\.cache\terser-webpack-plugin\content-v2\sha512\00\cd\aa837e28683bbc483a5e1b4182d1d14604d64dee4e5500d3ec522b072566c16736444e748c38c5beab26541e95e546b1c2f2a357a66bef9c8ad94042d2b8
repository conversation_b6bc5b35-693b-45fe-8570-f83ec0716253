{"code": "(window.webpackJsonp=window.webpackJsonp||[]).push([[19],{653:function(t,a,e){\"use strict\";e.r(a);var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e(\"div\",{staticClass:\"record\"},[e(\"header-nav\",{attrs:{backIconType:2,title:t.$t(\"key98\")}}),t._v(\" \"),e(\"div\",{staticClass:\"list_container\",attrs:{\"data-v-15480ecf\":\"\"}},[e(\"van-pull-refresh\",{attrs:{\"pulling-text\":\"Pull down to refresh..\",\"loosing-text\":\"Release to refresh...\",\"loading-text\":\"loading...\"},on:{refresh:t.getTransferRecordXiala},model:{value:t.loading,callback:function(a){t.loading=a},expression:\"loading\"}},[e(\"van-list\",{attrs:{finished:t.finished,\"loading-text\":\"loading...\"},on:{load:t.getTransferRecord},model:{value:t.loading,callback:function(a){t.loading=a},expression:\"loading\"}},t._l(t.List,function(a,s){return e(\"div\",{key:s,staticClass:\"list_item\",attrs:{\"data-v-15480ecf\":\"\"}},[e(\"div\",{staticClass:\"info\",attrs:{\"data-v-15480ecf\":\"\"}},[e(\"div\",{staticClass:\"titles\",attrs:{\"data-v-15480ecf\":\"\"}},[t._v(t._s(a.title)+\"\\n                            \")]),t._v(\" \"),e(\"div\",{staticClass:\"subtitles\",attrs:{\"data-v-15480ecf\":\"\"}})]),t._v(\" \"),e(\"div\",{staticClass:\"value\",attrs:{\"data-v-15480ecf\":\"\"}},[e(\"div\",{staticClass:\"amount ygreen\",class:[Number(a.amount)>0?\"green\":\"yred\"],attrs:{\"data-v-15480ecf\":\"\"}},[t._v(t._s(a.amount))]),t._v(\" \"),e(\"div\",{staticClass:\"status\",attrs:{\"data-v-15480ecf\":\"\"}},[t._v(t._s(a.create_time))])])])}),0),t._v(\" \"),t.List.length<=0?e(\"no-data\"):t._e()],1)],1)],1)};s._withStripped=!0;var i=e(111),n=e(678),r=e(40),o={name:\"airecord\",props:{},components:{noData:n.default,headerNav:i.default},data:()=>({page:1,page_size:20,loading:!1,finished:!1,List:[],coin:\"\"}),mounted(){this.coin=this.$route.query.coin,this.getTransferRecord()},methods:{reset(){this.page=1,this.loading=!1,this.finished=!1,this.List=[]},getTransferRecordXiala(){this.reset(),this.getTransferRecord()},getTransferRecord(){this.loading=!0,Object(r.getTransferRecord)({page:this.page,page_size:this.page_size,coin:this.coin}).then(t=>{let a=t.data;this.loading=!1,1==a.code&&(this.List.push(...a.data.list),this.page++),this.List.length>=a.data.total&&(this.finished=!0)})}}},c=(e(884),e(32)),d=Object(c.a)(o,s,[],!1,null,null,null);d.options.__file=\"src/view/center/wallet-record.vue\";a.default=d.exports},672:function(t,a,e){},678:function(t,a,e){\"use strict\";e.r(a);var s=function(){var t=this.$createElement,a=this._self._c||t;return a(\"div\",{attrs:{\"data-v-7932180b\":\"\"}},[a(\"div\",{staticClass:\"empty-state\",attrs:{\"data-v-b05b7c2c\":\"\",\"data-v-7932180b\":\"\"}},[a(\"img\",{staticClass:\"img-empty-state\",attrs:{src:e(688),\"data-v-b05b7c2c\":\"\"}}),this._v(\" \"),a(\"div\",{staticClass:\"no-data\",attrs:{\"data-v-b05b7c2c\":\"\"}},[this._v(this._s(this.$t(\"key1\")))])])])};s._withStripped=!0;var i={name:\"no-data\",props:{},components:{},data:()=>({}),mounted(){},methods:{}},n=(e(689),e(32)),r=Object(n.a)(i,s,[],!1,null,null,null);r.options.__file=\"src/components/no-data.vue\";a.default=r.exports},688:function(t,a){t.exports=\"/img/img_nodata.f22e393b.bf2e71bc_c2044823c1562056f491093a3f2a4450.png\"},689:function(t,a,e){\"use strict\";var s=e(672);e.n(s).a},731:function(t,a,e){},884:function(t,a,e){\"use strict\";var s=e(731);e.n(s).a}}]);", "extractedComments": []}