<!DOCTYPE html><!-- Last Published: Tue May 20 2025 20:47:45 GMT+0000 (Coordinated Universal Time) --><html data-wf-domain="www.portals.fi" data-wf-page="67e2b272d55a5b472d752801" data-wf-site="67e2b272d55a5b472d752803" lang="en"><head><meta charset="utf-8"><title>Portals</title><meta content="width=device-width, initial-scale=1" name="viewport"><link href="static/css/portals-8610b1-d5559d2501cb8e948aeeaba3.webflow.shared.66307c2be.css" rel="stylesheet" type="text/css"><link href="https://fonts.googleapis.com" rel="preconnect"><link href="https://fonts.gstatic.com" rel="preconnect" crossorigin="anonymous"><script src="static/js/webfont.js" type="text/javascript"></script><script type="text/javascript">WebFont.load({  google: {    families: ["Inconsolata:400,700","Inter:regular,500,600,700,800,900"]  }});</script><script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script><link href="https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/682348ddc0e5ac6b15de0c2a_663a5aa139582283ec4d23b6_favicon.ico" rel="shortcut icon" type="image/x-icon"><link href="https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/682348e05f3e0d3c52acbb9f_663a651a1fdd350e7440d3ef_logo-512x512.webp" rel="apple-touch-icon"><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-EHJMB0F1B2"></script><script type="text/javascript">window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}gtag('js', new Date());gtag('set', 'developer_id.dZGVlNj', true);gtag('config', 'G-EHJMB0F1B2');</script><!-- move the css to head --><style>[data-gsap] { visibility: hidden;}</style><!-- keep js in the body -->
</head><body class="body"><style>:root {--h1: clamp(1.75rem, 1.2205882352941178rem + 2.6470588235294117vw, 4rem);--h2: clamp(1.625rem, 1.1544117647058822rem + 2.3529411764705883vw, 3.625rem);--h3: clamp(1.375rem, 1.286764705882353rem + 0.4411764705882353vw, 1.75rem);--h4: clamp(1.25rem, 1.1911764705882353rem + 0.29411764705882354vw, 1.5rem);--h5: clamp(1.0625rem, 1.0183823529411764rem + 0.22058823529411764vw, 1.25rem);--body-text: clamp(0.875rem, 0.8455882352941176rem + 0.14705882352941177vw, 1rem);--body-small-text: clamp(0.6875rem, 0.6433823529411765rem + 0.22058823529411764vw, 0.875rem);}</style><div class="menu"><div class="container"><div class="menu-container"><a href="" aria-current="page" class="link-block-2 w-inline-block w--current"><div class="w-layout-grid menu-logo-grid"><img src="static/picture/67e2b272d55a5b472d75282b_Portals.svg" loading="lazy" id="w-node-_2f7c612f-f286-3e9c-a996-f3899c08c6b8-9c08c6b3" alt=""><div class="text-block-2">Portals</div></div></a><div class="div-block-50"><a id="w-node-_2f7c612f-f286-3e9c-a996-f3899c08c6bc-9c08c6b3" href="/blog" class="link-block w-inline-block"><div class="text-block-3">Blog</div></a><a id="w-node-_2f7c612f-f286-3e9c-a996-f3899c08c6bf-9c08c6b3" href="https://build.portals.fi/auth/signin?callbackUrl=https%3A%2F%2Fbuild.portals.fi%2Fdocs" target="_blank" class="link-block w-inline-block"><div class="text-block-3">Developers</div></a><a id="w-node-_2f7c612f-f286-3e9c-a996-f3899c08c6c2-9c08c6b3" href="/contact" class="link-block w-inline-block"><div class="text-block-3">Contact</div></a><a data-gsap="btn.1" href="https://explorer.portals.fi/" target="_blank" class="primary-btn w-inline-block"><div class="text-block-3">Launch App</div></a></div></div></div></div><div class="hero"><div class="container ontop"><div class="hero-content"><h1 data-gsap="txt.5" class="h1">Welcome to One-Click DeFi</h1><div data-gsap="par.4" class="text-block">From swaps to yield, Portals combines instant execution with unmatched coverage—millions of assets, thousands of protocols, and every major chain—all tradable in one click.</div><div class="div-block-2"><a data-gsap="btn.3" href="https://explorer.portals.fi/" target="_blank" class="primary-btn w-inline-block"><div class="text-block-3">View Explorer</div><img src="static/picture/6810b9722378dedc4935213a_chevron-left.svg" loading="lazy" alt="" class="image-18"></a></div></div></div><div class="bottom-divider-container"><div class="container"><div class="divider"></div></div></div><div class="hero-overlay"></div><div class="code-embed w-embed w-iframe w-script"><div class="vimeo-hls-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; z-index: -1;">
  <video class="vimeo-hls-video" data-src="https://player.vimeo.com/external/1063634108.m3u8?s=f6731f80870ef215958fef97782b87e57c2a38b6&logging=false" style="width: 100%; height: 100%; object-fit: cover;" autoplay muted loop playsinline></video>
</div>

<script>
(function() {
  // Try a direct approach - set the source directly on the video
  function tryDirectVideo() {
    const videos = document.querySelectorAll('.vimeo-hls-video:not([data-tried-direct])');
    
    videos.forEach(function(video) {
      video.setAttribute('data-tried-direct', 'true');
      const videoSrc = video.getAttribute('data-src');
      if (!videoSrc) return;
      
      video.src = videoSrc;
      video.load();
      try {
        video.play();
      } catch(e) {}
    });
  }
  
  // Try native HLS playback
  function tryNativeHLS() {
    const videos = document.querySelectorAll('.vimeo-hls-video:not([data-tried-native])');
    let hasNativeSupport = false;
    
    videos.forEach(function(video) {
      video.setAttribute('data-tried-native', 'true');
      
      if (video.canPlayType('application/vnd.apple.mpegurl')) {
        hasNativeSupport = true;
        
        const videoSrc = video.getAttribute('data-src');
        if (!videoSrc) return;
        
        video.src = videoSrc;
        video.load();
        try {
          video.play();
        } catch(e) {}
      }
    });
    
    return hasNativeSupport;
  }
  
  // Try using HLS.js
  function tryHlsJs() {
    if (!window.Hls || !Hls.isSupported()) {
      return false;
    }
    
    const videos = document.querySelectorAll('.vimeo-hls-video:not([data-tried-hlsjs])');
    
    videos.forEach(function(video) {
      video.setAttribute('data-tried-hlsjs', 'true');
      const videoSrc = video.getAttribute('data-src');
      if (!videoSrc) return;
      
      try {
        // Clean up any previous instance
        if (video._hls) {
          video._hls.destroy();
        }
        
        const hls = new Hls({
          enableWorker: false,
          lowLatencyMode: true
        });
        
        hls.on(Hls.Events.ERROR, function(event, data) {
          if (data.fatal) {
            hls.destroy();
            // Last resort fallback
            video.src = videoSrc;
            video.load();
            video.play().catch(e => {});
          }
        });
        
        hls.loadSource(videoSrc);
        hls.attachMedia(video);
        
        hls.on(Hls.Events.MANIFEST_PARSED, function() {
          video.play().catch(e => {});
        });
        
        // Store the hls instance
        video._hls = hls;
        return true;
      } catch(e) {
        // Continue to fallbacks
      }
    });
    
    return true;
  }

  // Try using an iframe fallback if all else fails
  function tryIframeFallback() {
    const containers = document.querySelectorAll('.vimeo-hls-container:not([data-tried-iframe])');
    
    containers.forEach(function(container) {
      container.setAttribute('data-tried-iframe', 'true');
      const video = container.querySelector('.vimeo-hls-video');
      if (!video) return;
      
      const videoSrc = video.getAttribute('data-src');
      if (!videoSrc) return;
      
      // Extract the video ID from the URL
      const urlParts = videoSrc.split('/');
      const possibleId = urlParts[urlParts.length - 1].split('.')[0];
      
      if (possibleId) {
        // Hide the video element
        video.style.display = 'none';
        
        // Create an iframe as last resort
        const iframe = document.createElement('iframe');
        iframe.src = `https://player.vimeo.com/video/${possibleId}?background=1&autoplay=1&loop=1&byline=0&title=0`;
        iframe.style.position = 'absolute';
        iframe.style.top = '0';
        iframe.style.left = '0';
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';
        iframe.allow = 'autoplay; fullscreen';
        
        container.appendChild(iframe);
      }
    });
  }
  
  // Load HLS.js with multiple CDN fallbacks
  function loadHlsScript() {
    return new Promise((resolve) => {
      if (window.Hls) {
        resolve(true);
        return;
      }
      
      // List of CDNs to try
      const cdns = [
        'https://cdnjs.cloudflare.com/ajax/libs/hls.js/1.4.0/hls.min.js',
        'https://cdn.jsdelivr.net/npm/hls.js@1.4.0/dist/hls.min.js',
        'https://unpkg.com/hls.js@1.4.0/dist/hls.min.js'
      ];
      
      let cdnIndex = 0;
      
      function tryLoadScript() {
        if (cdnIndex >= cdns.length) {
          resolve(false);
          return;
        }
        
        const cdn = cdns[cdnIndex];
        const script = document.createElement('script');
        script.src = cdn;
        
        script.onload = function() {
          setTimeout(() => {
            if (window.Hls) {
              resolve(true);
            } else {
              cdnIndex++;
              tryLoadScript();
            }
          }, 100);
        };
        
        script.onerror = function() {
          cdnIndex++;
          tryLoadScript();
        };
        
        document.head.appendChild(script);
      }
      
      tryLoadScript();
    });
  }

  // Main initialization function
  async function initializeVideos() {
    // First try native HLS support
    const hasNativeSupport = tryNativeHLS();
    
    if (!hasNativeSupport) {
      // Try to load HLS.js if needed
      const hlsLoaded = await loadHlsScript();
      
      if (hlsLoaded) {
        const hlsWorked = tryHlsJs();
        
        if (!hlsWorked) {
          tryDirectVideo();
        }
      } else {
        tryDirectVideo();
      }
    }
    
    // As a last resort, try iframe approach
    setTimeout(() => {
      const videos = document.querySelectorAll('.vimeo-hls-video');
      let allVisible = true;
      
      videos.forEach(video => {
        if (video.readyState < 3) { // HAVE_FUTURE_DATA
          allVisible = false;
        }
      });
      
      if (!allVisible) {
        tryIframeFallback();
      }
    }, 3000);
  }
  
  // Run initialization
  initializeVideos();
  
  // Also run on window load
  window.addEventListener('load', initializeVideos);
})();
</script></div></div><div class="section"><div class="container"><div class="div-block-51"><h1 class="heading">Backed by the best in the game</h1><div id="slider" class="carousel-component w-node-_13959576-f8ae-ea0f-e8a0-c9a1d5ee8ea8-2d752801"><div class="carousel-2"><div style="-webkit-transform:translate3d(0%, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0%, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0%, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0%, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)" class="logo-carousel-wrap"><ul role="list" class="logo-carousel"><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/681e0264aa18f319865b9e1c_Logo 01.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/681e026f91c8ba4fdcccc8b0_Logo 2.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/681e0277d0a702ad23dfbd0e_Logo 3.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/681e027e7407b7dfb9ebd470_Logo 4.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/681e0286ffe1851308adfb74_Logo 5.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/68222b33895972babd8e8e6f_cntrl.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/68222f8d3fdbba72924e452d_ef.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/68222b47c5b8046cd39d621f_mass.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/68247a64e8ffa1e4b63c6773_Logo 6.svg" class="logo-image"></a></li></ul><div class="logo-carousel-clone"><ul role="list" class="logo-carousel"><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/681e0264aa18f319865b9e1c_Logo 01.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/681e026f91c8ba4fdcccc8b0_Logo 2.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/681e0277d0a702ad23dfbd0e_Logo 3.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/681e027e7407b7dfb9ebd470_Logo 4.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/681e0286ffe1851308adfb74_Logo 5.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/68222b33895972babd8e8e6f_cntrl.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/68222f8d3fdbba72924e452d_ef.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/68222b47c5b8046cd39d621f_mass.svg" class="logo-image"></a></li><li class="logo-carousel-item"><a href="#" class="logo-link w-inline-block"><img width="172.5" loading="lazy" alt="" src="static/picture/68247a64e8ffa1e4b63c6773_Logo 6.svg" class="logo-image"></a></li></ul></div></div></div></div></div><div class="w-embed"><style>
/* make sure the wrapper hides overflow */
#slider {
  position: relative;
  overflow: hidden;
}

/* apply a gradient mask to fade edges */
#slider {
  /* WebKit browsers */
  -webkit-mask-image: linear-gradient(
    to right,
    transparent 0%,
    black       10%,
    black       90%,
    transparent 100%
  );
  /* Standard */
  mask-image: linear-gradient(
    to right,
    transparent 0%,
    black       10%,
    black       90%,
    transparent 100%
  );
}

</style></div></div><div class="bottom-divider-container"><div class="container"><div class="divider"></div></div></div></div><div class="section"><div class="container"><div class="container"><div class="div-block-9"><h1 data-gsap="txt.7" class="h2">Introducing the <br>Portals Explorer</h1><div data-gsap="par.9" class="text-block-8">The most advanced global DeFi dashboard: an all-in-one analytics and execution platform that combines real-time intelligence, historical data, and intent-based transactions in a single powerful interface. Powered by our <em>Zaps &amp; Data API</em>.</div></div><div id="portals-img" class="div-block-29"><img src="static/picture/681a3b73f68b77173d5dac0f_Explorer.png" loading="lazy" sizes="(max-width: 2908px) 100vw, 2908px" srcset="https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a3b73f68b77173d5dac0f_Explorer-p-500.png 500w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a3b73f68b77173d5dac0f_Explorer-p-800.png 800w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a3b73f68b77173d5dac0f_Explorer-p-1080.png 1080w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a3b73f68b77173d5dac0f_Explorer-p-1600.png 1600w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a3b73f68b77173d5dac0f_Explorer-p-2000.png 2000w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a3b73f68b77173d5dac0f_Explorer-p-2600.png 2600w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a3b73f68b77173d5dac0f_Explorer.png 2908w" alt="" class="image-13"></div><div class="w-layout-grid featuregrid top-border"><div class="w-layout-grid grid-3 alt"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6"><strong class="bold-text">Cross-Protocol Analytics</strong></div><div class="text-block-5"><em>Monitor real-time and historical data across millions of DeFi tokens and hundreds of platforms.</em></div></div><div class="w-layout-grid grid-3 alt"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div id="w-node-aa0fb6db-bd83-453a-b665-39c159ced0c4-2d752801" class="text-block-6"><strong>One-Click Zaps Execution</strong></div><div class="text-block-5"><em>Swap between any DeFi position with minimal slippage via Portals’ intent-based Zaps engine.</em></div></div><div class="w-layout-grid grid-3 alt"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6"><strong>Actionable Market Insights</strong></div><div class="text-block-5"><em>View live charts, APY, liquidity, and return trends with compact, high-signal analytics.</em></div></div><div class="w-layout-grid grid-3 alt"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6">Unified DeFi Data Layer</div><div class="text-block-5"><em>Access price, volume, and liquidity data from all major protocols through one interface.</em></div></div></div></div></div><div class="w-embed w-script"><script>
// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
  // Initialize Intersection Observer API
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      // When the target element enters the viewport
      if (entry.isIntersecting) {
        // Add animation class to start the animation
        document.getElementById('portals-img').classList.add('animate-in');
        // Stop observing once animation is triggered
        observer.unobserve(entry.target);
      }
    });
  }, {
    // Start animation when 20% of the element is visible
    threshold: 0.2,
    // Root margin to trigger slightly before element comes into view
    rootMargin: '0px 0px -100px 0px'
  });

  // Start observing the target element
  const targetElement = document.getElementById('portals-img');
  if (targetElement) {
    observer.observe(targetElement);
  }
});
</script>

<style>
#portals-img {
  /* Initial state - invisible and slightly scaled/rotated */
  opacity: 0;
  transform: perspective(1000px) rotateX(5deg) translateY(30px) scale(0.95);
  transition: all 1.2s cubic-bezier(0.23, 1, 0.32, 1); /* Smooth easing */
  will-change: transform, opacity; /* Performance optimization */
}

#portals-img.animate-in {
  /* Final state - fully visible with no transformation */
  opacity: 1;
  transform: perspective(1000px) rotateX(0deg) translateY(0) scale(1);
}

/* Add a subtle floating animation after the initial animation completes */
@keyframes subtle-float {
  0% {
    transform: perspective(1000px) translateY(0) rotateX(0deg);
  }
  50% {
    transform: perspective(1000px) translateY(-8px) rotateX(1deg);
  }
  100% {
    transform: perspective(1000px) translateY(0) rotateX(0deg);
  }
}

#portals-img.animate-in {
  animation: subtle-float 6s ease-in-out infinite;
  animation-delay: 1.2s; /* Start floating animation after entrance completes */
}
</style></div></div><div class="section block"><div class="container"><div class="w-layout-grid grid-16"><h1 data-gsap="txt.7" id="w-node-_5425096f-44c6-5ea8-31eb-89eff4bb8cb8-2d752801" class="h2">Explorer features</h1></div></div><div class="container"><div data-delay="4000" data-animation="slide" class="carousel is_contained w-slider" data-autoplay="false" data-easing="ease" data-hide-arrows="true" data-disable-swipe="false" data-autoplay-limit="0" data-nav-spacing="0" data-duration="500" data-infinite="false" id="carousel"><div data-gsap="el.1" class="carousel-mask w-slider-mask"><div class="carousel-slider w-slide"><div class="card"><div id="sidebar-blue" class="inner-card"><div class="card-content-block"><div class="text-block-7">Real Time DeFi<br></div><div class="div-block-25"><div class="text-block-5">Live access to your asset balances and token metrics, including pricing, market changes, and a host of other DeFi data.</div></div><div class="card-divider"></div></div><img src="static/picture/681a3890f5ece6d9f4a3546a_Sidebar.png" loading="lazy" sizes="(max-width: 710px) 100vw, 710px" srcset="https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a3890f5ece6d9f4a3546a_Sidebar-p-500.png 500w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a3890f5ece6d9f4a3546a_Sidebar.png 710w" alt="" class="image-9"></div></div></div><div class="carousel-slider _2 w-slide"><div class="card"><div id="trade-darkblue" class="inner-card"><div class="card-content-block"><div class="text-block-7">True Any-to-Any Swaps<br></div><div class="div-block-25"><div class="text-block-5">Swap any asset in a single, secure transaction. Powered by Intents for efficiency and security.</div></div><div class="card-divider"></div></div><img src="static/picture/681a389a8dd8756f00e21a0b_Trade.png" loading="lazy" sizes="(max-width: 710px) 100vw, 710px" srcset="https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a389a8dd8756f00e21a0b_Trade-p-500.png 500w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a389a8dd8756f00e21a0b_Trade.png 710w" alt=""></div></div></div><div class="carousel-slider _3 w-slide"><div class="card"><div id="stats-green" class="inner-card"><div class="card-content-block"><div class="text-block-7">Comprehensive Stats<br></div><div class="div-block-25"><div class="text-block-5">Live data on price, volume, liquidity, and trends to support smart, confident trading.</div></div><div class="card-divider"></div></div><img src="static/picture/681a38c7b3e05261c52b5c9c_Stats.png" loading="lazy" sizes="(max-width: 710px) 100vw, 710px" srcset="https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a38c7b3e05261c52b5c9c_Stats-p-500.png 500w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a38c7b3e05261c52b5c9c_Stats.png 710w" alt=""></div></div></div><div class="carousel-slider _4 w-slide"><div class="card"><div id="search-purple" class="inner-card"><div class="card-content-block"><div class="text-block-7">Search Tokens<br></div><div class="div-block-25"><div class="text-block-5">Quick access to your wallet balance and key token stats, including price, changes, and market data — all in one place.</div></div><div class="card-divider"></div></div><img src="static/picture/681a38ac570c294672456bcf_Search.png" loading="lazy" sizes="(max-width: 710px) 100vw, 710px" srcset="https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a38ac570c294672456bcf_Search-p-500.png 500w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681a38ac570c294672456bcf_Search.png 710w" alt=""></div></div></div></div><div class="arrow-left w-slider-arrow-left"><img src="static/picture/6810ba92404e2d8dc771d004_arrow-left.svg" loading="lazy" alt="" class="image-12"></div><div class="arrow-right w-slider-arrow-right"><img src="static/picture/6810baa1a5ac871be0a7ada6_arrow-right.svg" loading="lazy" alt="" class="image-11"></div><div class="slide-nav w-slider-nav"></div></div></div><div class="w-embed w-script"><style>
  /* Target the sidebar-blue div */
  #sidebar-blue {
    position: relative;
    overflow: hidden;
    background-color: #0d1729 !important; /* Dark blue base color */
    z-index: 1;
  }
  
  /* Create the radial gradient overlay */
  #sidebar-blue:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(
        circle at 70% 30%, 
        rgba(76, 136, 255, 0.25) 0%, 
        rgba(76, 136, 255, 0.05) 40%, 
        transparent 70%
      ),
      radial-gradient(
        circle at 30% 70%, 
        rgba(76, 136, 255, 0.2) 0%, 
        rgba(76, 136, 255, 0.05) 40%, 
        transparent 70%
      );
    z-index: -1;
    pointer-events: none;
  }
  
  /* Optional: Add a subtle noise texture for more visual interest */
  #sidebar-blue:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.05'/%3E%3C/svg%3E");
    z-index: -1;
    opacity: 0.05;
    pointer-events: none;
  }
  
  /* Target the trade-darkblue div */
  #trade-darkblue {
    position: relative;
    overflow: hidden;
    background-color: #16162e !important; /* Dark blue/purple base color */
    z-index: 1;
  }
  
  /* Create the radial gradient overlay for trade-darkblue */
  #trade-darkblue:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(
        circle at 60% 20%, 
        rgba(92, 70, 205, 0.3) 0%, 
        rgba(92, 70, 205, 0.1) 30%, 
        transparent 70%
      ),
      radial-gradient(
        circle at 20% 80%, 
        rgba(41, 60, 155, 0.25) 0%, 
        rgba(41, 60, 155, 0.05) 40%, 
        transparent 70%
      );
    z-index: -1;
    pointer-events: none;
  }
  
  /* Optional: Add a subtle noise texture for more visual interest to trade-darkblue */
  #trade-darkblue:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.05'/%3E%3C/svg%3E");
    z-index: -1;
    opacity: 0.05;
    pointer-events: none;
  }
  
  /* Target the stats-green div */
  #stats-green {
    position: relative;
    overflow: hidden;
    background-color: #0f2b1d !important; /* Dark green base color */
    z-index: 1;
  }
  
  /* Create the radial gradient overlay for stats-green */
  #stats-green:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(
        circle at 65% 25%, 
        rgba(52, 199, 89, 0.25) 0%, 
        rgba(52, 199, 89, 0.05) 40%, 
        transparent 70%
      ),
      radial-gradient(
        circle at 25% 75%, 
        rgba(38, 160, 105, 0.2) 0%, 
        rgba(38, 160, 105, 0.05) 40%, 
        transparent 70%
      );
    z-index: -1;
    pointer-events: none;
  }
  
  /* Optional: Add a subtle noise texture for more visual interest to stats-green */
  #stats-green:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.05'/%3E%3C/svg%3E");
    z-index: -1;
    opacity: 0.05;
    pointer-events: none;
  }
  
  /* Target the search-purple div */
  #search-purple {
    position: relative;
    overflow: hidden;
    background-color: #261627 !important; /* Dark purple base color */
    z-index: 1;
  }
  
  /* Create the radial gradient overlay for search-purple */
  #search-purple:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(
        circle at 70% 30%, 
        rgba(175, 82, 222, 0.25) 0%, 
        rgba(175, 82, 222, 0.05) 40%, 
        transparent 70%
      ),
      radial-gradient(
        circle at 30% 70%, 
        rgba(113, 47, 152, 0.2) 0%, 
        rgba(113, 47, 152, 0.05) 45%, 
        transparent 75%
      );
    z-index: -1;
    pointer-events: none;
  }
  
  /* Optional: Add a subtle noise texture for more visual interest to search-purple */
  #search-purple:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.05'/%3E%3C/svg%3E");
    z-index: -1;
    opacity: 0.05;
    pointer-events: none;
  }
</style>

<script>
  // This script ensures the styles are applied after the page fully loads
  document.addEventListener('DOMContentLoaded', function() {
    // You can add any additional JavaScript needed to manipulate the sidebar here
    console.log('Sidebar blue gradient initialized');
    
    // If the sidebar-blue element doesn't exist yet or loads dynamically, 
    // you might need to use a mutation observer or a timeout
    if (!document.getElementById('sidebar-blue')) {
      console.warn('sidebar-blue element not found on initial load, will watch for changes');
      
      // Optional: Set up a mutation observer to detect when sidebar-blue appears
      const observer = new MutationObserver(function(mutations) {
        if (document.getElementById('sidebar-blue')) {
          console.log('sidebar-blue element found after DOM change');
          observer.disconnect();
        }
      });
      
      observer.observe(document.body, { childList: true, subtree: true });
    }
  });
</script></div></div><div class="section block"><div class="container"><div class="div-block-26"><h2 data-gsap="txt.7" id="w-node-a3d69e11-a635-0d1a-0a83-3e136ffc144f-2d752801" class="h2">Smarter Search, Deeper Insights</h2><div data-gsap="par.8" id="w-node-a3d69e11-a635-0d1a-0a83-3e136ffc1451-2d752801" class="text-block-5">Explore tokens, pools, vaults, and more—instantly. Get key stats like fees, APY, and 7-day price charts at a glance, all in one smart search.</div></div><img class="image-10" src="static/picture/681e092c7852cfae01c9bc85_Search.png" alt="" sizes="(max-width: 2126px) 100vw, 2126px" id="search-img" loading="lazy" data-gsap="el.5" srcset="https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681e092c7852cfae01c9bc85_Search-p-500.png 500w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681e092c7852cfae01c9bc85_Search-p-800.png 800w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681e092c7852cfae01c9bc85_Search-p-1080.png 1080w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681e092c7852cfae01c9bc85_Search-p-1600.png 1600w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681e092c7852cfae01c9bc85_Search-p-2000.png 2000w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/681e092c7852cfae01c9bc85_Search.png 2126w"><div class="w-layout-grid grid-20"><div class="w-layout-grid grid-3 alt"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6">Millions of Tokens</div><div class="text-block-5">Search across tokens, pools, vaults, lending markets, yield aggregators, and other DeFi primitives. Everything is searchable, filterable, and designed for speed.</div></div><div class="w-layout-grid grid-3 alt"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6">Trending Assets</div><div class="text-block-5">Visit individual token pages to view TVL, volume, network, APY, and liquidity utilisation, and other metrics—without ever leaving the page.</div></div><div class="w-layout-grid grid-3 alt"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6">All-in-One Discovery</div><div class="text-block-5">See how a token’s been performing at a glance. Each result includes detailed info about each opportunity, showing trending tokens.</div></div></div></div></div><div class="section bg-blur"><div class="container center-down"><div class="div-block-3"><h2 data-gsap="txt.7" class="h2">Portals Simplifes DeFi</h2></div><div class="multistep-container"><div data-gsap="el.6" class="multistep-card-component"><div class="multi-step-grid _1"><div class="multi-step-grid-top"><div class="div-block-6"><div class="text-block-4">Multi Steps without Portals</div><div class="text-block-5">Without Portals, executing complex DeFi strategies means manually bridging chains, swapping assets, entering pools, and staking—each requiring separate approvals, transactions, and gas fees. It’s time-consuming, error-prone, and costly, especially when navigating sophisticated assets like Pendle PT yield tokens or Balancer GyroStabe pools.</div></div></div><div class="code-embed-3 w-embed w-iframe w-script"><div class="vimeo-hls-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; z-index: -1;">
  <video class="vimeo-hls-video" data-src="https://player.vimeo.com/external/1063643851.m3u8?s=d49de5b049e02f96013835546bff11f7abe45578&logging=false" style="width: 100%; height: 100%; object-fit: cover;" autoplay muted loop playsinline></video>
</div>

<script>
(function() {
  // Try a direct approach - set the source directly on the video
  function tryDirectVideo() {
    const videos = document.querySelectorAll('.vimeo-hls-video:not([data-tried-direct])');
    
    videos.forEach(function(video) {
      video.setAttribute('data-tried-direct', 'true');
      const videoSrc = video.getAttribute('data-src');
      if (!videoSrc) return;
      
      video.src = videoSrc;
      video.load();
      try {
        video.play();
      } catch(e) {}
    });
  }
  
  // Try native HLS playback
  function tryNativeHLS() {
    const videos = document.querySelectorAll('.vimeo-hls-video:not([data-tried-native])');
    let hasNativeSupport = false;
    
    videos.forEach(function(video) {
      video.setAttribute('data-tried-native', 'true');
      
      if (video.canPlayType('application/vnd.apple.mpegurl')) {
        hasNativeSupport = true;
        
        const videoSrc = video.getAttribute('data-src');
        if (!videoSrc) return;
        
        video.src = videoSrc;
        video.load();
        try {
          video.play();
        } catch(e) {}
      }
    });
    
    return hasNativeSupport;
  }
  
  // Try using HLS.js
  function tryHlsJs() {
    if (!window.Hls || !Hls.isSupported()) {
      return false;
    }
    
    const videos = document.querySelectorAll('.vimeo-hls-video:not([data-tried-hlsjs])');
    
    videos.forEach(function(video) {
      video.setAttribute('data-tried-hlsjs', 'true');
      const videoSrc = video.getAttribute('data-src');
      if (!videoSrc) return;
      
      try {
        // Clean up any previous instance
        if (video._hls) {
          video._hls.destroy();
        }
        
        const hls = new Hls({
          enableWorker: false,
          lowLatencyMode: true
        });
        
        hls.on(Hls.Events.ERROR, function(event, data) {
          if (data.fatal) {
            hls.destroy();
            // Last resort fallback
            video.src = videoSrc;
            video.load();
            video.play().catch(e => {});
          }
        });
        
        hls.loadSource(videoSrc);
        hls.attachMedia(video);
        
        hls.on(Hls.Events.MANIFEST_PARSED, function() {
          video.play().catch(e => {});
        });
        
        // Store the hls instance
        video._hls = hls;
        return true;
      } catch(e) {
        // Continue to fallbacks
      }
    });
    
    return true;
  }

  // Try using an iframe fallback if all else fails
  function tryIframeFallback() {
    const containers = document.querySelectorAll('.vimeo-hls-container:not([data-tried-iframe])');
    
    containers.forEach(function(container) {
      container.setAttribute('data-tried-iframe', 'true');
      const video = container.querySelector('.vimeo-hls-video');
      if (!video) return;
      
      const videoSrc = video.getAttribute('data-src');
      if (!videoSrc) return;
      
      // Extract the video ID from the URL
      const urlParts = videoSrc.split('/');
      const possibleId = urlParts[urlParts.length - 1].split('.')[0];
      
      if (possibleId) {
        // Hide the video element
        video.style.display = 'none';
        
        // Create an iframe as last resort
        const iframe = document.createElement('iframe');
        iframe.src = `https://player.vimeo.com/video/${possibleId}?background=1&autoplay=1&loop=1&byline=0&title=0`;
        iframe.style.position = 'absolute';
        iframe.style.top = '0';
        iframe.style.left = '0';
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';
        iframe.allow = 'autoplay; fullscreen';
        
        container.appendChild(iframe);
      }
    });
  }
  
  // Load HLS.js with multiple CDN fallbacks
  function loadHlsScript() {
    return new Promise((resolve) => {
      if (window.Hls) {
        resolve(true);
        return;
      }
      
      // List of CDNs to try
      const cdns = [
        'https://cdnjs.cloudflare.com/ajax/libs/hls.js/1.4.0/hls.min.js',
        'https://cdn.jsdelivr.net/npm/hls.js@1.4.0/dist/hls.min.js',
        'https://unpkg.com/hls.js@1.4.0/dist/hls.min.js'
      ];
      
      let cdnIndex = 0;
      
      function tryLoadScript() {
        if (cdnIndex >= cdns.length) {
          resolve(false);
          return;
        }
        
        const cdn = cdns[cdnIndex];
        const script = document.createElement('script');
        script.src = cdn;
        
        script.onload = function() {
          setTimeout(() => {
            if (window.Hls) {
              resolve(true);
            } else {
              cdnIndex++;
              tryLoadScript();
            }
          }, 100);
        };
        
        script.onerror = function() {
          cdnIndex++;
          tryLoadScript();
        };
        
        document.head.appendChild(script);
      }
      
      tryLoadScript();
    });
  }

  // Main initialization function
  async function initializeVideos() {
    // First try native HLS support
    const hasNativeSupport = tryNativeHLS();
    
    if (!hasNativeSupport) {
      // Try to load HLS.js if needed
      const hlsLoaded = await loadHlsScript();
      
      if (hlsLoaded) {
        const hlsWorked = tryHlsJs();
        
        if (!hlsWorked) {
          tryDirectVideo();
        }
      } else {
        tryDirectVideo();
      }
    }
    
    // As a last resort, try iframe approach
    setTimeout(() => {
      const videos = document.querySelectorAll('.vimeo-hls-video');
      let allVisible = true;
      
      videos.forEach(video => {
        if (video.readyState < 3) { // HAVE_FUTURE_DATA
          allVisible = false;
        }
      });
      
      if (!allVisible) {
        tryIframeFallback();
      }
    }, 3000);
  }
  
  // Run initialization
  initializeVideos();
  
  // Also run on window load
  window.addEventListener('load', initializeVideos);
})();
</script></div></div><div class="multi-step-grid"><div class="multi-step-grid-top"><div class="div-block-6"><div class="text-block-4">Multi Steps with Portals</div><div class="text-block-5">With Portals, all those actions are bundled into a single, optimized transaction. Swap, bridge, zap into liquidity pools, stake vault tokens, and move between complex DeFi positions instantly—saving time, reducing gas costs, and minimizing slippage and approval risks. One click. Maximum efficiency.</div></div></div><div class="code-embed-3 w-embed w-iframe w-script"><div class="vimeo-hls-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; z-index: -1;">
  <video class="vimeo-hls-video" data-src="https://player.vimeo.com/external/1063642089.m3u8?s=0ae0ef15fa45793bafd41aed32fef1f648611dd3&logging=false" style="width: 100%; height: 100%; object-fit: cover;" autoplay muted loop playsinline></video>
</div>

<script>
(function() {
  // Try a direct approach - set the source directly on the video
  function tryDirectVideo() {
    const videos = document.querySelectorAll('.vimeo-hls-video:not([data-tried-direct])');
    
    videos.forEach(function(video) {
      video.setAttribute('data-tried-direct', 'true');
      const videoSrc = video.getAttribute('data-src');
      if (!videoSrc) return;
      
      video.src = videoSrc;
      video.load();
      try {
        video.play();
      } catch(e) {}
    });
  }
  
  // Try native HLS playback
  function tryNativeHLS() {
    const videos = document.querySelectorAll('.vimeo-hls-video:not([data-tried-native])');
    let hasNativeSupport = false;
    
    videos.forEach(function(video) {
      video.setAttribute('data-tried-native', 'true');
      
      if (video.canPlayType('application/vnd.apple.mpegurl')) {
        hasNativeSupport = true;
        
        const videoSrc = video.getAttribute('data-src');
        if (!videoSrc) return;
        
        video.src = videoSrc;
        video.load();
        try {
          video.play();
        } catch(e) {}
      }
    });
    
    return hasNativeSupport;
  }
  
  // Try using HLS.js
  function tryHlsJs() {
    if (!window.Hls || !Hls.isSupported()) {
      return false;
    }
    
    const videos = document.querySelectorAll('.vimeo-hls-video:not([data-tried-hlsjs])');
    
    videos.forEach(function(video) {
      video.setAttribute('data-tried-hlsjs', 'true');
      const videoSrc = video.getAttribute('data-src');
      if (!videoSrc) return;
      
      try {
        // Clean up any previous instance
        if (video._hls) {
          video._hls.destroy();
        }
        
        const hls = new Hls({
          enableWorker: false,
          lowLatencyMode: true
        });
        
        hls.on(Hls.Events.ERROR, function(event, data) {
          if (data.fatal) {
            hls.destroy();
            // Last resort fallback
            video.src = videoSrc;
            video.load();
            video.play().catch(e => {});
          }
        });
        
        hls.loadSource(videoSrc);
        hls.attachMedia(video);
        
        hls.on(Hls.Events.MANIFEST_PARSED, function() {
          video.play().catch(e => {});
        });
        
        // Store the hls instance
        video._hls = hls;
        return true;
      } catch(e) {
        // Continue to fallbacks
      }
    });
    
    return true;
  }

  // Try using an iframe fallback if all else fails
  function tryIframeFallback() {
    const containers = document.querySelectorAll('.vimeo-hls-container:not([data-tried-iframe])');
    
    containers.forEach(function(container) {
      container.setAttribute('data-tried-iframe', 'true');
      const video = container.querySelector('.vimeo-hls-video');
      if (!video) return;
      
      const videoSrc = video.getAttribute('data-src');
      if (!videoSrc) return;
      
      // Extract the video ID from the URL
      const urlParts = videoSrc.split('/');
      const possibleId = urlParts[urlParts.length - 1].split('.')[0];
      
      if (possibleId) {
        // Hide the video element
        video.style.display = 'none';
        
        // Create an iframe as last resort
        const iframe = document.createElement('iframe');
        iframe.src = `https://player.vimeo.com/video/${possibleId}?background=1&autoplay=1&loop=1&byline=0&title=0`;
        iframe.style.position = 'absolute';
        iframe.style.top = '0';
        iframe.style.left = '0';
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';
        iframe.allow = 'autoplay; fullscreen';
        
        container.appendChild(iframe);
      }
    });
  }
  
  // Load HLS.js with multiple CDN fallbacks
  function loadHlsScript() {
    return new Promise((resolve) => {
      if (window.Hls) {
        resolve(true);
        return;
      }
      
      // List of CDNs to try
      const cdns = [
        'https://cdnjs.cloudflare.com/ajax/libs/hls.js/1.4.0/hls.min.js',
        'https://cdn.jsdelivr.net/npm/hls.js@1.4.0/dist/hls.min.js',
        'https://unpkg.com/hls.js@1.4.0/dist/hls.min.js'
      ];
      
      let cdnIndex = 0;
      
      function tryLoadScript() {
        if (cdnIndex >= cdns.length) {
          resolve(false);
          return;
        }
        
        const cdn = cdns[cdnIndex];
        const script = document.createElement('script');
        script.src = cdn;
        
        script.onload = function() {
          setTimeout(() => {
            if (window.Hls) {
              resolve(true);
            } else {
              cdnIndex++;
              tryLoadScript();
            }
          }, 100);
        };
        
        script.onerror = function() {
          cdnIndex++;
          tryLoadScript();
        };
        
        document.head.appendChild(script);
      }
      
      tryLoadScript();
    });
  }

  // Main initialization function
  async function initializeVideos() {
    // First try native HLS support
    const hasNativeSupport = tryNativeHLS();
    
    if (!hasNativeSupport) {
      // Try to load HLS.js if needed
      const hlsLoaded = await loadHlsScript();
      
      if (hlsLoaded) {
        const hlsWorked = tryHlsJs();
        
        if (!hlsWorked) {
          tryDirectVideo();
        }
      } else {
        tryDirectVideo();
      }
    }
    
    // As a last resort, try iframe approach
    setTimeout(() => {
      const videos = document.querySelectorAll('.vimeo-hls-video');
      let allVisible = true;
      
      videos.forEach(video => {
        if (video.readyState < 3) { // HAVE_FUTURE_DATA
          allVisible = false;
        }
      });
      
      if (!allVisible) {
        tryIframeFallback();
      }
    }, 3000);
  }
  
  // Run initialization
  initializeVideos();
  
  // Also run on window load
  window.addEventListener('load', initializeVideos);
})();
</script></div></div></div></div><div class="w-layout-grid featuregrid bento"><div class="w-layout-grid grid-3"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6"><strong class="bold-text">Handle Complexity</strong></div><div class="text-block-5">The most advanced DeFi moves are simplified, enabling seamless swaps, liquidity provisioning, and staking across complex assets like Pendle synthetic yield tokens and Balancer pools.</div></div><div class="w-layout-grid grid-3"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div id="w-node-_4c3a6064-78eb-208a-7467-a7ba6c6d4a6a-2d752801" class="text-block-6"><strong>Cross-Chain Zaps</strong></div><div class="text-block-5">Move liquidity and DeFi positions across multiple chains effortlessly, with optimized routes that abstract away the need for manual bridging or token swapping.</div></div><div class="w-layout-grid grid-3"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6"><strong>AI-Ready Solutions</strong></div><div class="text-block-5">Portals powers AI-driven DeFi automation by providing current data, historical insights, and execution APIs that enable smarter, Intent-based asset management.</div></div><div class="w-layout-grid grid-3"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6">Ultimate Efficiency</div><div class="text-block-5">Save time, reduce gas costs, and minimize slippage by bundling multi-step DeFi operations into a single, optimized transaction powered by our Warpdrive routing engine.</div></div></div></div><div class="bottom-divider-container"><div class="container"><div class="divider"></div></div></div></div><div class="section"><div class="container center-down"><div class="w-layout-grid grid-19"><div class="div-block-21"><h1 class="h1 light"><strong data-gsap="txt.7" class="h2">Launch Smarter. Scale Faster.</strong></h1><div data-gsap="par.8" class="title-subtext">Portals is the universal DeFi solution combining real-time and historical data, swap and zap execution, and intent-based transactions into one powerful platform.</div><div data-current="Tab 1" data-easing="ease" data-duration-in="300" data-duration-out="100" class="tabs w-tabs"><div data-gsap="el.6" class="tabs-menu w-tab-menu"><a data-w-tab="Tab 1" class="tab-link w-inline-block w-tab-link w--current"><div>Developers</div></a><a data-w-tab="Tab 2" class="tab-link w-inline-block w-tab-link"><div>Protocols</div></a><a data-w-tab="Tab 3" class="tab-link w-inline-block w-tab-link"><div>Traders</div></a></div><div class="tabs-content w-tab-content"><div data-w-tab="Tab 1" class="w-tab-pane w--tab-active"><div data-gsap="el.6" class="w-layout-grid benefits-grid"><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div class="text-block-6"><strong class="bold-text">Ship in 30 Minutes</strong></div><div class="text-block-18"><span class="text-span-5">Plug-and-play APIs with full docs and low-code integration.</span></div></div><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div class="text-block-6"><strong class="bold-text">Most Complete DeFi API</strong></div><div class="text-block-18"><span class="text-span-5">Real-time &amp; historical data for thousands of tokens, vaults, pools, and other DeFi primitives.</span></div></div><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div class="text-block-6"><strong class="bold-text">No Solidity Needed</strong></div><div class="text-block-18"><span class="text-span-5">Portals API handles the orchestration and bundling of co.</span></div></div><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div class="text-block-6"><strong class="bold-text">Built for DeFi AI</strong></div><div class="text-block-18"><span class="text-span-5">Power AI agents with structured endpoints and real-time context.</span></div></div></div></div><div data-w-tab="Tab 2" class="w-tab-pane"><div class="w-layout-grid benefits-grid"><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div id="w-node-_7168f71b-8641-a07b-601f-4bd9aaea5d7f-2d752801" class="text-block-6"><strong class="bold-text">Zap In, Zero Friction</strong></div><div class="text-block-18"><span class="text-span-5">Let traders seamlessly enter your protocol with any asset in one transaction.</span></div></div><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div class="text-block-6"><strong class="bold-text">Universal Routing</strong></div><div class="text-block-18"><span class="text-span-5">Swap any asset for any other, even highly complex, compsed DeFi assets, all in one-click</span></div></div><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div class="text-block-6"><strong class="bold-text">White-Label Widget</strong></div><div class="text-block-18"><span class="text-span-5">Fully customizable Swap/Zap UI, no-coding required.</span></div></div><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div class="text-block-6"><strong class="bold-text">Data That Drives TVL</strong></div><div class="text-block-18"><span class="text-span-5">Show off your protocol&#x27;s growth trends, holders distribution, and other trader friendly metrics</span></div></div></div></div><div data-w-tab="Tab 3" class="w-tab-pane"><div class="w-layout-grid benefits-grid"><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div id="w-node-_3f777b97-32e0-734a-7855-23e85d6812e1-2d752801" class="text-block-6"><strong class="bold-text">Swap Anything Instantly</strong></div><div class="text-block-18"><span class="text-span-5">Move from any tokenized position to another in one click.</span></div></div><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div class="text-block-6"><strong class="bold-text">One Dashboard for All</strong></div><div class="text-block-18"><span class="text-span-5">View live prices, APYs, TVL, and act without leaving the page.</span></div></div><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div class="text-block-6"><strong class="bold-text">No More Multi-Step Zaps</strong></div><div class="text-block-18"><span class="text-span-5">Enter pools or vaults in one optimized, pre-simulated action.</span></div></div><div class="w-layout-grid grid-3 _2row"><img loading="lazy" src="static/picture/67e2b272d55a5b472d752835_check.svg" alt="" class="image"><div class="text-block-6"><strong class="bold-text">Pre-Flight Checks &amp; Simulation</strong></div><div class="text-block-18"><span class="text-span-5">See exactly what you’ll get before committing. No surprises, no failed swaps.<br></span></div></div></div></div></div></div></div><img src="static/picture/6813b05d3e366dec1889a4ee_Rocket.jpg" loading="lazy" width="376" id="w-node-_1bbcb9c7-7be5-bab6-13f8-760a33a8e793-2d752801" alt="" srcset="https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/6813b05d3e366dec1889a4ee_Rocket-p-500.jpg 500w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/6813b05d3e366dec1889a4ee_Rocket-p-800.jpg 800w, https://cdn.prod.website-files.com/67e2b272d55a5b472d752803/6813b05d3e366dec1889a4ee_Rocket.jpg 941w" sizes="(max-width: 479px) 100vw, 376px" class="image-7"></div></div><div class="bottom-divider-container"><div class="container"><div class="divider"></div></div></div></div><div class="section"><div class="container"><div class="div-block-9"><h1 data-gsap="txt.7" class="h2">The technology behind Portals.</h1></div><div data-gsap="el.6" data-w-id="2c029168-5430-ccf3-c7c1-3c956fe765aa" class="bento-box"><div class="code-embed-3 w-embed w-script"><style>
#portals-warpdrive-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 400px;
    overflow: hidden;
    background: #0c0926;
}

#portals-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.warpdrive-token-container {
    position: absolute;
    pointer-events: none;
    transform: translate(-50%, -50%);
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.warpdrive-token {
    width: 60px;
    height: 60px;
    position: relative;
    filter: drop-shadow(0 0 10px rgba(131, 91, 255, 0.6));
    transition: transform 0.3s ease, filter 0.3s ease;
}

.token-inner {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(131, 91, 255, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.2);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.token-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(131, 91, 255, 0.4);
    opacity: 0.7;
    pointer-events: none;
}

.warpdrive-token-label {
    background: rgba(9, 9, 38, 0.85);
    color: white;
    font-family: 'Inter', -apple-system, sans-serif;
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
    margin-top: 8px;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(131, 91, 255, 0.3);
    box-shadow: 0 0 10px rgba(131, 91, 255, 0.3);
}

.transaction-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 5;
}

.transaction-indicator {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(9, 9, 38, 0.85);
    color: white;
    font-family: 'Inter', -apple-system, sans-serif;
    font-size: 12px;
    padding: 10px 14px;
    border-radius: 6px;
    transform: translate(-50%, -50%);
    z-index: 15;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(131, 91, 255, 0.3);
    box-shadow: 0 0 15px rgba(131, 91, 255, 0.3);
    white-space: nowrap;
    letter-spacing: 0.3px;
    line-height: 1.5;
}

.transaction-indicator span {
    color: #a36eff;
    font-weight: 600;
    margin: 0 3px;
}

.progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0c0926 0%, #170e40 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.progress-bar-container {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #674cff, #a36eff);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.progress-text {
    margin-top: 15px;
    color: white;
    font-family: 'Inter', -apple-system, sans-serif;
    font-size: 14px;
    font-weight: 500;
}

.warpdrive-stats {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: row;
    color: white;
    font-family: 'Inter', -apple-system, sans-serif;
    font-size: 12px;
    z-index: 20;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.warpdrive-stat {
    margin-left: 10px;
    background: rgba(9, 9, 38, 0.7);
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid rgba(131, 91, 255, 0.3);
    box-shadow: 0 0 15px rgba(131, 91, 255, 0.2);
    backdrop-filter: blur(4px);
}

.warpdrive-value {
    color: #a36eff;
    font-weight: 600;
    margin-left: 5px;
}

.token-pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    border: 2px solid rgba(131, 91, 255, 0.8);
    pointer-events: none;
    z-index: 1;
}

.star {
    position: absolute;
    background-color: white;
    border-radius: 50%;
    opacity: 0;
    animation: twinkle var(--duration) ease-in-out infinite;
    animation-delay: var(--delay);
}

@keyframes pulse-ring {
    0% {
        transform: translate(-50%, -50%) scale(0.95);
        opacity: 0.8;
        border-color: rgba(255, 255, 255, 0.9);
    }
    100% {
        transform: translate(-50%, -50%) scale(1.8);
        opacity: 0;
        border-color: rgba(131, 91, 255, 0);
    }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
    100% { transform: translateY(0px); }
}

@keyframes glow-pulse {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

@keyframes twinkle {
    0% { opacity: 0; }
    50% { opacity: var(--max-opacity); }
    100% { opacity: 0; }
}

/* Particle animations */
.particle {
    position: absolute;
    background: white;
    border-radius: 50%;
    pointer-events: none;
    opacity: 0;
}

@keyframes particle-float {
    0% { 
        transform: translate(0, 0) scale(1);
        opacity: 0.7;
    }
    100% { 
        transform: translate(var(--x), var(--y)) scale(0);
        opacity: 0;
    }
}
</style>

<div id="portals-warpdrive-container">
    <canvas id="portals-canvas"></canvas>
    
    <div class="warpdrive-stats">
        <div class="warpdrive-stat">Gas Savings:<span class="warpdrive-value">42%</span></div>
        <div class="warpdrive-stat">Slippage Reduction:<span class="warpdrive-value">3.5x</span></div>
        <div class="warpdrive-stat">Protocols:<span class="warpdrive-value">37</span></div>
    </div>
    
    <div class="progress">
        <div class="progress-bar-container">
            <div class="progress-bar"></div>
        </div>
        <div class="progress-text">Initializing Warpdrive</div>
    </div>
</div>

<script>
(function() {
    // Configuration
    const CONFIG = {
        tokens: [
            { symbol: 'ETH', value: '$3,482.60', img: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png' },
            { symbol: 'USDC', value: '$1.00', img: 'https://assets.coingecko.com/coins/images/6319/large/usdc.png' },
            { symbol: 'WBTC', value: '$65,221.05', img: 'https://assets.coingecko.com/coins/images/7598/large/wrapped_bitcoin_wbtc.png' },
            { symbol: 'DAI', value: '$1.00', img: 'https://assets.coingecko.com/coins/images/9956/large/Badge_Dai.png' },
            { symbol: 'AAVE', value: '$92.48', img: 'https://assets.coingecko.com/coins/images/12645/large/AAVE.png' },
            { symbol: 'CRV', value: '$0.52', img: 'https://assets.coingecko.com/coins/images/12124/large/Curve.png' },
            { symbol: 'YFI', value: '$8,341.20', img: 'https://assets.coingecko.com/coins/images/11849/large/yfi-192x192.png' },
            { symbol: 'UNI', value: '$8.27', img: 'https://assets.coingecko.com/coins/images/12504/large/uni.jpg' }
        ],
        protocols: [
            "Uniswap V3", "Curve", "Balancer", "Aave V3", "Compound V3", 
            "Convex", "Yearn", "Beefy", "Pendle", "Lido", "PancakeSwap", "Maverick"
        ],
        transactionTypes: [
            { text: "Token → LP Position", detail: "Providing liquidity to Uniswap V3" },
            { text: "Token → Staking", detail: "Staking in the protocol for rewards" },
            { text: "LP → Vault", detail: "Depositing LP position into yield vault" },
            { text: "Vault → Vault", detail: "Moving between yield strategies" },
            { text: "Token → Loan Position", detail: "Opening a leveraged position" }
        ],
        stars: {
            count: 150,  // Number of stars
            minSize: 1,  // Minimum star size in pixels
            maxSize: 3,  // Maximum star size in pixels
            minOpacity: 0.2,  // Minimum star opacity
            maxOpacity: 0.7   // Maximum star opacity
        }
    };
    
    // Canvas and context
    let canvas, ctx;
    // Token elements
    let tokenElements = [];
    // Animation variables
    let animationRunning = true;
    let animationFrame;
    // Transaction lines
    let transactionLines = [];
    // Transaction indicators
    let transactionIndicators = [];
    // Stars
    let stars = [];
    
    // Initialize
    function initialize() {
        // Get container and create canvas
        const container = document.getElementById('portals-warpdrive-container');
        canvas = document.getElementById('portals-canvas');
        
        // Set canvas size
        resizeCanvas();
        
        // Get context
        ctx = canvas.getContext('2d');
        
        // Create stars
        createStars();
        
        // Create token elements
        createTokenElements();
        
        // Start loading animation
        startLoading();
        
        // Listen for resize events
        window.addEventListener('resize', resizeCanvas);
    }
    
    function resizeCanvas() {
        const container = document.getElementById('portals-warpdrive-container');
        canvas.width = container.clientWidth;
        canvas.height = container.clientHeight;
        
        // Reposition tokens if they exist
        if (tokenElements.length > 0) {
            positionTokens();
        }
        
        // Reposition stars
        if (stars.length > 0) {
            repositionStars();
        }
    }
    
    function startLoading() {
        const progressBar = document.querySelector('.progress-bar');
        let progress = 0;
        
        const interval = setInterval(() => {
            progress += 2;
            progressBar.style.width = `${progress}%`;
            
            if (progress >= 100) {
                clearInterval(interval);
                
                setTimeout(() => {
                    // Hide loading screen
                    document.querySelector('.progress').style.opacity = '0';
                    
                    setTimeout(() => {
                        document.querySelector('.progress').style.display = 'none';
                        
                        // Show tokens
                        showTokens();
                        
                        // Show UI elements
                        document.querySelector('.warpdrive-stats').style.opacity = '1';
                        document.querySelector('.warpdrive-stats').style.transform = 'translateY(0)';
                        
                        // Start transaction simulations
                        setTimeout(startTransactions, 1000);
                        
                        // Start background animation
                        animateBackground();
                    }, 500);
                }, 500);
            }
        }, 30);
    }
    
    function createStars() {
        const container = document.getElementById('portals-warpdrive-container');
        
        for (let i = 0; i < CONFIG.stars.count; i++) {
            // Create a twinkling star
            const star = document.createElement('div');
            star.className = 'star';
            
            // Random size
            const size = CONFIG.stars.minSize + Math.random() * (CONFIG.stars.maxSize - CONFIG.stars.minSize);
            star.style.width = `${size}px`;
            star.style.height = `${size}px`;
            
            // Random position
            const x = Math.random() * container.clientWidth;
            const y = Math.random() * container.clientHeight;
            star.style.left = `${x}px`;
            star.style.top = `${y}px`;
            
            // Random twinkle animation properties
            const maxOpacity = CONFIG.stars.minOpacity + Math.random() * (CONFIG.stars.maxOpacity - CONFIG.stars.minOpacity);
            const duration = 3 + Math.random() * 5 + 's';
            const delay = Math.random() * 5 + 's';
            
            star.style.setProperty('--max-opacity', maxOpacity);
            star.style.setProperty('--duration', duration);
            star.style.setProperty('--delay', delay);
            
            // Add to container
            container.appendChild(star);
            
            // Store for reference
            stars.push({
                element: star,
                x: x,
                y: y,
                size: size
            });
        }
    }
    
    function repositionStars() {
        const container = document.getElementById('portals-warpdrive-container');
        
        stars.forEach(star => {
            // Recalculate position to maintain relative position
            const x = Math.random() * container.clientWidth;
            const y = Math.random() * container.clientHeight;
            
            star.element.style.left = `${x}px`;
            star.element.style.top = `${y}px`;
            
            // Update stored position
            star.x = x;
            star.y = y;
        });
    }
    
    function createTokenElements() {
        const container = document.getElementById('portals-warpdrive-container');
        
        CONFIG.tokens.forEach((token, index) => {
            // Create token container
            const tokenContainer = document.createElement('div');
            tokenContainer.className = 'warpdrive-token-container';
            
            // Create token wrapper
            const tokenElement = document.createElement('div');
            tokenElement.className = 'warpdrive-token';
            tokenElement.style.animation = `float ${3 + Math.random()}s ease-in-out infinite`;
            
            // Create inner token (the actual circle)
            const tokenInner = document.createElement('div');
            tokenInner.className = 'token-inner';
            tokenInner.style.backgroundImage = `url(${token.img})`;
            
            // Create glow effect
            const tokenGlow = document.createElement('div');
            tokenGlow.className = 'token-glow';
            tokenGlow.style.animation = `glow-pulse ${2 + Math.random()}s ease-in-out infinite`;
            
            // Add inner elements to token
            tokenElement.appendChild(tokenInner);
            tokenInner.appendChild(tokenGlow);
            
            // Create token label
            const tokenLabel = document.createElement('div');
            tokenLabel.className = 'warpdrive-token-label';
            tokenLabel.textContent = token.symbol;
            
            // Add to container
            tokenContainer.appendChild(tokenElement);
            tokenContainer.appendChild(tokenLabel);
            
            // Add to document
            container.appendChild(tokenContainer);
            
            // Store element
            tokenElements.push({
                container: tokenContainer,
                element: tokenElement,
                inner: tokenInner,
                glow: tokenGlow,
                label: tokenLabel,
                symbol: token.symbol,
                value: token.value,
                position: { x: 0, y: 0 },
                index: index
            });
        });
        
        // Position tokens
        positionTokens();
    }
    
    function positionTokens() {
        const container = document.getElementById('portals-warpdrive-container');
        const centerX = container.clientWidth / 2;
        const centerY = container.clientHeight / 2;
        const radius = Math.min(centerX, centerY) * 0.6;
        
        tokenElements.forEach((token, index) => {
            const angle = (index / tokenElements.length) * Math.PI * 2;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            
            token.container.style.left = `${x}px`;
            token.container.style.top = `${y}px`;
            
            // Store position for animation
            token.position = { x, y };
        });
    }
    
    function showTokens() {
        tokenElements.forEach((token, index) => {
            setTimeout(() => {
                token.container.style.opacity = '1';
                token.element.style.transform = 'scale(1)';
            }, index * 100);
        });
    }
    
    function startTransactions() {
        // Create some initial transaction indicators
        createTransactionIndicator();
        
        // Schedule next transaction
        setTimeout(createTransactionIndicator, 2000);
    }
    
    function createTransactionIndicator() {
        // Pick random start and end tokens
        const startIndex = Math.floor(Math.random() * tokenElements.length);
        let endIndex;
        do {
            endIndex = Math.floor(Math.random() * tokenElements.length);
        } while (endIndex === startIndex);
        
        const startToken = tokenElements[startIndex];
        const endToken = tokenElements[endIndex];
        
        // Maybe add an intermediate token
        let intermediateToken = null;
        let hasIntermediate = Math.random() > 0.5;
        
        if (hasIntermediate) {
            let intermediateIndex;
            do {
                intermediateIndex = Math.floor(Math.random() * tokenElements.length);
            } while (intermediateIndex === startIndex || intermediateIndex === endIndex);
            
            intermediateToken = tokenElements[intermediateIndex];
        }
        
        // Get transaction type
        const transactionType = CONFIG.transactionTypes[Math.floor(Math.random() * CONFIG.transactionTypes.length)];
        const protocol = CONFIG.protocols[Math.floor(Math.random() * CONFIG.protocols.length)];
        
        // Create path and animation
        if (hasIntermediate) {
            // Two-segment path
            createTransactionPath(startToken, intermediateToken, endToken, transactionType, protocol);
        } else {
            // Direct path
            createTransactionPath(startToken, null, endToken, transactionType, protocol);
        }
        
        // Schedule next transaction
        setTimeout(createTransactionIndicator, 4000 + Math.random() * 2000);
    }
    
    function createTransactionPath(startToken, intermediateToken, endToken, transactionType, protocol) {
        const container = document.getElementById('portals-warpdrive-container');
        
        // Generate random transaction amounts
        const startAmount = (1 + Math.random() * 999).toFixed(2);
        const endAmount = (1 + Math.random() * 999).toFixed(2);
        
        // Flash effect for start token
        flashToken(startToken);
        
        // Create transaction lines
        if (intermediateToken) {
            // First segment
            createCurvedLine(
                startToken.position.x, 
                startToken.position.y, 
                intermediateToken.position.x, 
                intermediateToken.position.y,
                '#674cff'
            );
            
            // Second segment
            setTimeout(() => {
                createCurvedLine(
                    intermediateToken.position.x, 
                    intermediateToken.position.y, 
                    endToken.position.x, 
                    endToken.position.y,
                    '#a36eff'
                );
                
                // Flash intermediate token
                flashToken(intermediateToken);
            }, 800);
            
            // Flash end token after both segments
            setTimeout(() => {
                flashToken(endToken);
            }, 1600);
        } else {
            // Direct path
            createCurvedLine(
                startToken.position.x, 
                startToken.position.y, 
                endToken.position.x, 
                endToken.position.y,
                '#8a4fff'
            );
            
            // Flash end token
            setTimeout(() => {
                flashToken(endToken);
            }, 800);
        }
        
        // Create transaction indicator
        const transactionIndicator = document.createElement('div');
        transactionIndicator.className = 'transaction-indicator';
        
        // Set indicator content with better spacing
        transactionIndicator.innerHTML = `
            <div style="margin-bottom: 4px">${startAmount} <span>${startToken.symbol}</span> → ${endAmount} <span>${endToken.symbol}</span></div>
            <div style="opacity: 0.8; font-size: 10px">${transactionType.text} via ${protocol}</div>
        `;
        
        // Position indicator
        let indicatorX, indicatorY;
        
        if (intermediateToken) {
            // Position at intermediate token
            indicatorX = intermediateToken.position.x;
            indicatorY = intermediateToken.position.y - 40;
        } else {
            // Position midway between tokens
            indicatorX = (startToken.position.x + endToken.position.x) / 2;
            indicatorY = (startToken.position.y + endToken.position.y) / 2 - 40;
        }
        
        transactionIndicator.style.left = `${indicatorX}px`;
        transactionIndicator.style.top = `${indicatorY}px`;
        
        // Add to container
        container.appendChild(transactionIndicator);
        
        // Show indicator
        setTimeout(() => {
            transactionIndicator.style.opacity = '1';
            
            // Hide and remove after display
            setTimeout(() => {
                transactionIndicator.style.opacity = '0';
                
                setTimeout(() => {
                    if (transactionIndicator.parentNode) {
                        transactionIndicator.parentNode.removeChild(transactionIndicator);
                    }
                }, 500);
            }, 2500);
        }, intermediateToken ? 1000 : 400);
        
        // Store indicator for cleanup
        transactionIndicators.push(transactionIndicator);
    }
    
    function createCurvedLine(x1, y1, x2, y2, color) {
        const container = document.getElementById('portals-warpdrive-container');
        
        // Create SVG element for the line
        const svgNS = "http://www.w3.org/2000/svg";
        const svg = document.createElementNS(svgNS, "svg");
        svg.setAttribute("class", "transaction-line");
        svg.setAttribute("width", "100%");
        svg.setAttribute("height", "100%");
        svg.style.position = "absolute";
        svg.style.top = "0";
        svg.style.left = "0";
        svg.style.pointerEvents = "none";
        
        // Calculate control point for curve
        const midX = (x1 + x2) / 2;
        const midY = (y1 + y2) / 2;
        
        // Add some randomness and curve upward
        const controlX = midX;
        const controlY = midY - Math.random() * 70 - 30;
        
        // Create defs for filters and gradients
        const defs = document.createElementNS(svgNS, "defs");
        
        // Create gradient
        const gradientId = `gradient-${Date.now()}`;
        const gradient = document.createElementNS(svgNS, "linearGradient");
        gradient.setAttribute("id", gradientId);
        gradient.setAttribute("x1", "0%");
        gradient.setAttribute("y1", "0%");
        gradient.setAttribute("x2", "100%");
        gradient.setAttribute("y2", "0%");
        gradient.setAttribute("gradientUnits", "userSpaceOnUse");
        gradient.setAttribute("gradientTransform", `rotate(${Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI}, ${(x1 + x2) / 2}, ${(y1 + y2) / 2})`);
        
        const stop1 = document.createElementNS(svgNS, "stop");
        stop1.setAttribute("offset", "0%");
        stop1.setAttribute("stop-color", "#674cff");
        
        const stop2 = document.createElementNS(svgNS, "stop");
        stop2.setAttribute("offset", "100%");
        stop2.setAttribute("stop-color", "#a36eff");
        
        gradient.appendChild(stop1);
        gradient.appendChild(stop2);
        defs.appendChild(gradient);
        
        // Create filter for glow effect
        const filterId = `glow-${Date.now()}`;
        const filter = document.createElementNS(svgNS, "filter");
        filter.setAttribute("id", filterId);
        filter.setAttribute("x", "-50%");
        filter.setAttribute("y", "-50%");
        filter.setAttribute("width", "200%");
        filter.setAttribute("height", "200%");
        
        const feGaussianBlur = document.createElementNS(svgNS, "feGaussianBlur");
        feGaussianBlur.setAttribute("stdDeviation", "3");
        feGaussianBlur.setAttribute("result", "blur");
        
        filter.appendChild(feGaussianBlur);
        defs.appendChild(filter);
        
        svg.appendChild(defs);
        
        // Create path for curve
        const path = document.createElementNS(svgNS, "path");
        path.setAttribute("d", `M ${x1} ${y1} Q ${controlX} ${controlY} ${x2} ${y2}`);
        path.setAttribute("fill", "none");
        path.setAttribute("stroke", `url(#${gradientId})`);
        path.setAttribute("stroke-width", "3");
        path.setAttribute("stroke-linecap", "round");
        path.setAttribute("filter", `url(#${filterId})`);
        path.setAttribute("opacity", "0");
        
        // Add animation for path
        path.style.transition = "opacity 0.5s ease";
        setTimeout(() => {
            path.setAttribute("opacity", "0.9");
        }, 10);
        
        svg.appendChild(path);
        
        // Create moving particles along the path
        const particlesGroup = document.createElementNS(svgNS, "g");
        
        // Add multiple particles with different sizes and speeds
        for (let i = 0; i < 12; i++) {
            const particleSize = 1.5 + Math.random() * 2.5;
            const particle = document.createElementNS(svgNS, "circle");
            particle.setAttribute("r", particleSize);
            particle.setAttribute("fill", "white");
            particle.setAttribute("filter", `url(#${filterId})`);
            
            // Add particle animation with varying speed
            const duration = 0.8 + Math.random() * 0.6;
            const animateMotion = document.createElementNS(svgNS, "animateMotion");
            animateMotion.setAttribute("dur", `${duration}s`);
            animateMotion.setAttribute("repeatCount", "3");
            animateMotion.setAttribute("path", `M ${x1} ${y1} Q ${controlX} ${controlY} ${x2} ${y2}`);
            animateMotion.setAttribute("begin", `${i * 0.1}s`);
            
            particle.appendChild(animateMotion);
            particlesGroup.appendChild(particle);
        }
        
        svg.appendChild(particlesGroup);
        container.appendChild(svg);
        
        // Create burst particles at destination
        setTimeout(() => {
            createParticleBurst(x2, y2);
        }, 800);
        
        // Store for cleanup
        transactionLines.push(svg);
        
        // Remove after animation completes
        setTimeout(() => {
            if (svg.parentNode) {
                // Fade out
                path.setAttribute("opacity", "0");
                
                setTimeout(() => {
                    if (svg.parentNode) {
                        svg.parentNode.removeChild(svg);
                        
                        // Remove from array
                        const index = transactionLines.indexOf(svg);
                        if (index > -1) {
                            transactionLines.splice(index, 1);
                        }
                    }
                }, 500);
            }
        }, 3500);
    }
    
    function flashToken(token) {
        // Check if already flashing
        if (token.element.dataset.isFlashing === 'true') {
            return;
        }
        
        token.element.dataset.isFlashing = 'true';
        
        // Add a glow/pulse effect to the token
        token.inner.style.filter = "brightness(1.5)";
        token.element.style.filter = "drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))";
        
        // Create a ring effect that's centered on the token (not the container)
        const ring = document.createElement('div');
        ring.className = 'token-pulse-ring';
        ring.style.width = '60px';
        ring.style.height = '60px';
        ring.style.animation = 'pulse-ring 1s cubic-bezier(0.1, 0.9, 0.2, 1) forwards';
        
        // Append ring to the inner token element (not the container)
        token.element.appendChild(ring);
        
        // Reset after animation
        setTimeout(() => {
            token.inner.style.filter = "";
            token.element.style.filter = "drop-shadow(0 0 10px rgba(131, 91, 255, 0.6))";
            
            // Remove ring after animation completes
            setTimeout(() => {
                if (ring.parentNode === token.element) {
                    token.element.removeChild(ring);
                }
                token.element.dataset.isFlashing = 'false';
            }, 900);
        }, 500);
    }
    
    function createParticleBurst(x, y) {
        const container = document.getElementById('portals-warpdrive-container');
        
        // Create multiple particles
        for (let i = 0; i < 12; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Random size between 3 and 6px
            const size = 3 + Math.random() * 3;
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            
            // Position at the burst center
            particle.style.left = `${x}px`;
            particle.style.top = `${y}px`;
            
            // Random direction
            const angle = Math.random() * Math.PI * 2;
            const distance = 30 + Math.random() * 30;
            const xMove = Math.cos(angle) * distance;
            const yMove = Math.sin(angle) * distance;
            
            // Set CSS variables for the animation
            particle.style.setProperty('--x', `${xMove}px`);
            particle.style.setProperty('--y', `${yMove}px`);
            
            // Apply animation
            particle.style.animation = `particle-float ${0.5 + Math.random() * 0.5}s ease-out forwards`;
            
            // Add to container
            container.appendChild(particle);
            
            // Remove after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 1500);
        }
    }
    
    function animateBackground() {
        // Clear previous frame
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw grid
        drawGrid();
        
        // Continue animation
        if (animationRunning) {
            animationFrame = requestAnimationFrame(animateBackground);
        }
    }
    
    function drawGrid() {
        const time = Date.now() * 0.0001; // Slower animation
        const gridSize = 60; // Larger grid
        const gridWidth = Math.ceil(canvas.width / gridSize) + 1;
        const gridHeight = Math.ceil(canvas.height / gridSize) + 1;
        
        // Draw subtle glow at center
        const gradient = ctx.createRadialGradient(
            canvas.width / 2, canvas.height / 2, 0,
            canvas.width / 2, canvas.height / 2, canvas.width * 0.7
        );
        gradient.addColorStop(0, 'rgba(131, 91, 255, 0.15)');
        gradient.addColorStop(1, 'rgba(131, 91, 255, 0)');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        ctx.strokeStyle = 'rgba(131, 91, 255, 0.15)';
        ctx.lineWidth = 1;
        
        // Horizontal lines with wave
        for (let y = 0; y < gridHeight; y++) {
            ctx.beginPath();
            for (let x = 0; x < gridWidth; x++) {
                const xPos = x * gridSize;
                const yPos = y * gridSize + Math.sin(x * 0.1 + time) * 8;
                
                if (x === 0) {
                    ctx.moveTo(xPos, yPos);
                } else {
                    ctx.lineTo(xPos, yPos);
                }
            }
            ctx.stroke();
        }
        
        // Vertical lines with wave
        for (let x = 0; x < gridWidth; x++) {
            ctx.beginPath();
            for (let y = 0; y < gridHeight; y++) {
                const xPos = x * gridSize + Math.sin(y * 0.1 + time) * 8;
                const yPos = y * gridSize;
                
                if (y === 0) {
                    ctx.moveTo(xPos, yPos);
                } else {
                    ctx.lineTo(xPos, yPos);
                }
            }
            ctx.stroke();
        }
    }
    
    function cleanup() {
        // Stop animation
        animationRunning = false;
        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }
        
        // Remove event listeners
        window.removeEventListener('resize', resizeCanvas);
        
        // Remove token elements
        tokenElements.forEach(token => {
            if (token.container.parentNode) {
                token.container.parentNode.removeChild(token.container);
            }
        });
        
        // Remove transaction lines
        transactionLines.forEach(line => {
            if (line.parentNode) {
                line.parentNode.removeChild(line);
            }
        });
        
        // Remove transaction indicators
        transactionIndicators.forEach(indicator => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        });
        
        // Remove stars
        stars.forEach(star => {
            if (star.element.parentNode) {
                star.element.parentNode.removeChild(star.element);
            }
        });
        
        // Remove all particle elements
        const particles = document.querySelectorAll('.particle');
        particles.forEach(particle => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        });
    }
    
    // Initialize when DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // Handle cleanup when leaving page
    window.addEventListener('beforeunload', cleanup);
})();
</script></div><div class="multi-step-grid-top flip"><div class="text-block-7">Warpdrive Aggregator<br></div><div class="text-block-18"><span class="text-span-5">Our in-house DeFi aggregator, built to tackle even the most complex scenarios. Powered by our <em>Intents &amp; Pricing engines</em>, Warpdrive prioritizes outcomes over routes, delivering unmatched efficiency in liquidity management and token swaps, with built-in slippage and price impact protection.</span></div></div></div><div data-gsap="el.6" class="w-layout-grid grid-4"><div class="bento-box zaps"><div class="multi-step-grid-top flip"><div class="text-block-7">Zaps API<br></div><div class="text-block-18"><span class="text-span-5">Leverage DeFi’s composability with the Zaps API. Bundle multiple actions, including asset swaps &amp; yield opportunity zaps, into a single transaction using <em>Intents</em>.</span></div></div></div><div class="bento-box data"><div class="multi-step-grid-top flip"><div class="text-block-7">Data API<br></div><div class="text-block-18"><span class="text-span-5">Get real-time and historical data on DeFi assets across multiple blockchains and protocols. Access APYs, TVL, prices, balances, contract addresses, and more.</span></div></div></div></div></div></div><div class="section"><div class="container"></div><div class="bottom-divider-container"><div class="container"><div class="divider"></div></div></div></div><div class="section"><div class="container"><div class="container"><div class="w-layout-grid grid-16"><h1 data-gsap="txt.7" id="w-node-_48e0f62d-e133-41cf-5a2b-86bc379b8c43-2d752801" class="h2">Portals API for Product Teams</h1><div data-gsap="par.8" id="w-node-_796c8767-145e-a940-f0c7-66bf2a996507-2d752801" class="text-block-5">‍Whether you&#x27;re building a swap interface, a DeFi analytics dashboard, or a next-generation DEX, Portals provides the APIs and infrastructure to launch faster and scale smarter</div></div><div data-gsap="el.6" class="w-layout-grid grid-17"><div data-w-id="a12abbe3-ee5e-758c-e691-317d6820725e" style="-webkit-transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="bento-box use-cases _1"><div class="multi-step-grid-top flip"><div class="text-block-7">Swap Platforms<br></div></div></div><div data-w-id="2e16c4c3-e8fe-1ee3-6bd5-e84bebd31f91" style="-webkit-transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="bento-box use-cases _2"><div class="multi-step-grid-top flip"><div class="text-block-7">Data Aggregators<br></div></div></div><div data-w-id="ba64a6b5-36dc-ccbe-1a8d-5760c419a896" style="-webkit-transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 0, 0) scale3d(1.1, 1.1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="bento-box use-cases _3"><div class="multi-step-grid-top flip"><div class="text-block-7">Decentralised Exchanges<br></div></div></div></div></div></div><div class="bottom-divider-container"><div class="container"><div class="divider"></div></div></div></div><div class="section"><div class="container center-down"><div class="div-block-11"><h1 data-gsap="txt.7" class="h1 light">Universal Intents Engine</h1></div><div class="code-embed-5 w-embed w-script"><style>
#animation-container {
    position: relative;
    width: 100%;
    height: 30vh;
    background-color: black;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.pill {
    position: relative;
    background-color: white;
    color: black;
    padding: 15px 30px;
    border-radius: 30px;
    font-weight: bold;
    font-size: 18px;
    z-index: 10;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}
</style>

<div id="animation-container">
    <canvas id="animation"></canvas>
    <div class="pill">Intents Engine</div>
</div>

<script>
    // Initialize canvas
    const canvas = document.getElementById('animation');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size
    function setCanvasSize() {
        canvas.width = window.innerWidth;
        canvas.height = canvas.parentElement.offsetHeight;
    }
    
    setCanvasSize();
    window.addEventListener('resize', setCanvasSize);
    
    // Get pill dimensions for calculation
    const pill = document.querySelector('.pill');
    let pillRect;
    
    // Make sure everything is properly initialized
    function initializeAnimation() {
        pillRect = pill.getBoundingClientRect();
        const containerRect = canvas.parentElement.getBoundingClientRect();
        
        // Ensure the pill position is calculated relative to the container
        const pillLeft = pillRect.left - containerRect.left;
        const pillRight = pillRect.right - containerRect.left;
        const pillTop = pillRect.top - containerRect.top;
        const pillBottom = pillRect.bottom - containerRect.top;
        
        // Update pillRect with container-relative coordinates
        pillRect = {
            left: pillLeft,
            right: pillRight,
            top: pillTop,
            bottom: pillBottom,
            width: pillRect.width,
            height: pillRect.height
        };
        
        generateLines();
    }
    
    // Left side colors (blues)
    const leftColors = [
        { r: 0, g: 120, b: 255 },   // Light blue
        { r: 30, g: 90, b: 200 },   // Medium blue
        { r: 20, g: 60, b: 150 }    // Dark blue
    ];
    
    // Right side colors (pinks to orange)
    const rightColors = [
        { r: 255, g: 100, b: 150 }, // Pink
        { r: 255, g: 130, b: 100 }, // Coral
        { r: 255, g: 180, b: 100 }  // Orange
    ];
    
    // Flowing line class
    class FlowLine {
        constructor(startX, startY, endX, endY, color, thickness, indexY, totalY) {
            this.startX = startX;
            this.startY = startY;
            this.endX = endX;
            this.endY = endY;
            this.color = color;
            this.thickness = thickness;
            this.indexY = indexY;   // Vertical index of this line
            this.totalY = totalY;   // Total number of lines vertically
            
            // Create curve points for the line
            this.setupCurvePoints();
            
            // Animation properties
            this.pulsePhase = Math.random() * Math.PI * 2;
            this.pulseSpeed = 0.015 + Math.random() * 0.01;
            
            // Flowing particles
            this.particles = [];
            this.lastParticleTime = 0;
            this.generateParticles();
        }
        
        setupCurvePoints() {
            // Total horizontal distance
            const totalDistance = Math.abs(this.endX - this.startX);
            
            // Calculate normalized vertical position (-1 to 1, with 0 at center)
            const normalizedY = (this.indexY / (this.totalY - 1) - 0.5) * 2;
            
            // Calculate how much the line should curve
            // Middle lines curve less, top and bottom lines curve more
            const curveFactor = Math.abs(normalizedY);
            
            // Direction of the curve (up or down)
            const curveDirection = normalizedY > 0 ? 1 : -1;
            
            // Create the curve points
            if (this.startX < this.endX) {
                // Left-to-right line
                
                // Straight segment is now 25% (increased from 10%)
                const straightLength = totalDistance * 0.25;
                
                // Position where curve starts
                const curveStartX = this.startX + straightLength;
                
                // Control points for the curve
                // First control point - slightly after start of curve
                const cp1x = curveStartX + totalDistance * 0.1;
                const cp1y = this.startY;
                
                // Second control point - reduced vertical offset
                const cp2x = this.startX + totalDistance * 0.5;
                const cp2y = this.startY + (curveDirection * curveFactor * totalDistance * 0.08);
                
                // End point of curve - reduced vertical offset
                const endX = this.endX;
                const endY = this.startY + (curveDirection * curveFactor * totalDistance * 0.12);
                
                this.curvePoints = {
                    start: { x: this.startX, y: this.startY },
                    straightEnd: { x: curveStartX, y: this.startY },
                    cp1: { x: cp1x, y: cp1y },
                    cp2: { x: cp2x, y: cp2y },
                    end: { x: endX, y: endY }
                };
            } else {
                // Right-to-left line
                
                // Straight segment is now 25% (increased from 10%)
                const straightLength = totalDistance * 0.25;
                
                // Position where curve starts
                const curveStartX = this.startX - straightLength;
                
                // Control points for the curve
                // First control point - slightly after start of curve
                const cp1x = curveStartX - totalDistance * 0.1;
                const cp1y = this.startY;
                
                // Second control point - reduced vertical offset
                const cp2x = this.startX - totalDistance * 0.5;
                const cp2y = this.startY + (curveDirection * curveFactor * totalDistance * 0.08);
                
                // End point of curve - reduced vertical offset
                const endX = this.endX;
                const endY = this.startY + (curveDirection * curveFactor * totalDistance * 0.12);
                
                this.curvePoints = {
                    start: { x: this.startX, y: this.startY },
                    straightEnd: { x: curveStartX, y: this.startY },
                    cp1: { x: cp1x, y: cp1y },
                    cp2: { x: cp2x, y: cp2y },
                    end: { x: endX, y: endY }
                };
            }
        }
        
        generateParticles() {
            this.particles = [];
            const count = 8 + Math.floor(Math.random() * 4);
            
            for (let i = 0; i < count; i++) {
                this.particles.push({
                    position: Math.random(),
                    speed: 0.001 + Math.random() * 0.002,
                    size: this.thickness * (0.7 + Math.random() * 0.6),
                    alpha: 0.6 + Math.random() * 0.4
                });
            }
        }
        
        update() {
            // Update pulse effect
            this.pulsePhase += this.pulseSpeed;
            if (this.pulsePhase > Math.PI * 2) this.pulsePhase -= Math.PI * 2;
            
            // Update particles
            this.particles.forEach(particle => {
                // Move particle along the curve
                particle.position += particle.speed;
                
                // Reset particle if it reached the end
                if (particle.position > 1) {
                    particle.position = 0;
                    particle.size = this.thickness * (0.7 + Math.random() * 0.6);
                    particle.alpha = 0.6 + Math.random() * 0.4;
                }
            });
        }
        
        getPointOnPath(t) {
            const points = this.curvePoints;
            
            // First straight segment (0 to 0.1)
            if (t < 0.1) {
                const segmentT = t / 0.1;
                return {
                    x: points.start.x + (points.straightEnd.x - points.start.x) * segmentT,
                    y: points.start.y
                };
            }
            // Curve segment (0.1 to 1.0)
            else {
                const curveT = (t - 0.1) / 0.9;
                return this.getBezierPoint(
                    points.straightEnd,
                    points.cp1,
                    points.cp2,
                    points.end,
                    curveT
                );
            }
        }
        
        getBezierPoint(p0, p1, p2, p3, t) {
            const mt = 1 - t;
            const mt2 = mt * mt;
            const mt3 = mt2 * mt;
            const t2 = t * t;
            const t3 = t2 * t;
            
            return {
                x: mt3 * p0.x + 3 * mt2 * t * p1.x + 3 * mt * t2 * p2.x + t3 * p3.x,
                y: mt3 * p0.y + 3 * mt2 * t * p1.y + 3 * mt * t2 * p2.y + t3 * p3.y
            };
        }
        
        draw(ctx) {
            const pulse = 0.85 + 0.15 * Math.sin(this.pulsePhase);
            const currentThickness = this.thickness * pulse;
            
            // Create gradient
            const { r, g, b } = this.color;
            const alpha = 0.9 * pulse;
            
            // Draw the path
            ctx.beginPath();
            
            // Start point
            const points = this.curvePoints;
            ctx.moveTo(points.start.x, points.start.y);
            
            // Straight segment
            ctx.lineTo(points.straightEnd.x, points.straightEnd.y);
            
            // Curve segment
            ctx.bezierCurveTo(
                points.cp1.x, points.cp1.y,
                points.cp2.x, points.cp2.y,
                points.end.x, points.end.y
            );
            
            // Create gradient based on direction
            const gradient = ctx.createLinearGradient(this.startX, this.startY, this.endX, this.endY);
            
            // For left-to-right lines
            if (this.startX < this.endX) {
                gradient.addColorStop(0, `rgba(${r}, ${g}, ${b}, 0)`);
                gradient.addColorStop(0.1, `rgba(${r}, ${g}, ${b}, ${alpha * 0.8})`);
                gradient.addColorStop(0.9, `rgba(${r}, ${g}, ${b}, ${alpha})`);
                gradient.addColorStop(1, `rgba(${r}, ${g}, ${b}, 0)`);
            } 
            // For right-to-left lines
            else {
                gradient.addColorStop(0, `rgba(${r}, ${g}, ${b}, 0)`);
                gradient.addColorStop(0.1, `rgba(${r}, ${g}, ${b}, ${alpha})`);
                gradient.addColorStop(0.9, `rgba(${r}, ${g}, ${b}, ${alpha * 0.8})`);
                gradient.addColorStop(1, `rgba(${r}, ${g}, ${b}, 0)`);
            }
            
            ctx.strokeStyle = gradient;
            ctx.lineWidth = currentThickness;
            ctx.stroke();
            
            // Draw glow effect
            ctx.beginPath();
            ctx.moveTo(points.start.x, points.start.y);
            ctx.lineTo(points.straightEnd.x, points.straightEnd.y);
            ctx.bezierCurveTo(
                points.cp1.x, points.cp1.y,
                points.cp2.x, points.cp2.y,
                points.end.x, points.end.y
            );
            
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${alpha * 0.3})`;
            ctx.lineWidth = currentThickness * 2.5;
            ctx.stroke();
            
            // Draw particles
            this.particles.forEach(particle => {
                const point = this.getPointOnPath(particle.position);
                
                ctx.beginPath();
                ctx.arc(point.x, point.y, particle.size * pulse, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${particle.alpha * pulse})`;
                ctx.fill();
            });
        }
    }
    
    // Create flow lines
    let flowLines = [];
    
    function generateLines() {
        flowLines = [];
        
        // Get center and dimensions of the pill
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const pillWidth = pillRect.width;
        const pillHeight = pillRect.height;
        
        // Number of lines on each side
        const linesPerSide = 6; // Reduced for more spacing between lines
        
        // Vertical constraint - even smaller vertical space
        const verticalSpread = Math.min(pillHeight * 0.4, canvas.height * 0.15);
        
        // Left side lines
        for (let i = 0; i < linesPerSide; i++) {
            // Calculate vertical position with smaller spread
            const t = i / (linesPerSide - 1);
            const verticalOffset = (t - 0.5) * verticalSpread;
            const lineY = centerY + verticalOffset;
            
            // Starting point on the pill (left side)
            const startX = centerX - (pillWidth / 2);
            const startY = lineY;
            
            // End point at the screen edge
            const endX = 0;
            const endY = lineY;
            
            // Pick a blue color
            const colorIndex = i % leftColors.length;
            const color = leftColors[colorIndex];
            
            // Create line with random thickness
            const thickness = 1.3 + Math.random() * 0.7;
            
            flowLines.push(new FlowLine(
                startX, startY,
                endX, endY,
                color, thickness,
                i, linesPerSide
            ));
        }
        
        // Right side lines
        for (let i = 0; i < linesPerSide; i++) {
            // Calculate vertical position with smaller spread
            const t = i / (linesPerSide - 1);
            const verticalOffset = (t - 0.5) * verticalSpread;
            const lineY = centerY + verticalOffset;
            
            // Starting point on the pill (right side)
            const startX = centerX + (pillWidth / 2);
            const startY = lineY;
            
            // End point at the screen edge
            const endX = canvas.width;
            const endY = lineY;
            
            // Pick a pink/orange color
            const colorIndex = i % rightColors.length;
            const color = rightColors[colorIndex];
            
            // Create line with random thickness
            const thickness = 1.3 + Math.random() * 0.7;
            
            flowLines.push(new FlowLine(
                startX, startY,
                endX, endY,
                color, thickness,
                i, linesPerSide
            ));
        }
    }
    
    // Animation loop
    function animate() {
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Update and draw all flow lines
        flowLines.forEach(line => {
            line.update();
            line.draw(ctx);
        });
        
        requestAnimationFrame(animate);
    }
    
    // Ensure proper initialization after everything is loaded
    window.addEventListener('load', () => {
        setTimeout(initializeAnimation, 100); // Small delay to ensure pill is rendered
    });
    
    // Initialize immediately as well, in case load event already fired
    setTimeout(initializeAnimation, 100);
    
    // Start animation loop
    animate();
    
    // Handle window resize
    window.addEventListener('resize', () => {
        setCanvasSize();
        setTimeout(initializeAnimation, 100); // Re-initialize after resize with slight delay
    });
</script></div><div class="div-block-12"><div data-gsap="par.8" class="text-block-8">The Universal Intents Engine powers any-to-any DeFi transactions by turning user outcomes into optimized execution paths. It bundles complex steps like swaps, liquidity provisioning, and staking into a single transaction—no matter the protocol or complexity. <br><br><strong>No smart contract knowledge needed.</strong><br></div></div><div class="w-layout-grid featuregrid bento"><div class="w-layout-grid grid-3"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6">Any-to-Any Asset Swaps</div><div class="text-block-5">Move between vaults, pools, LPs, or yield tokens with a single input; no manual steps or bridging needed.</div></div><div class="w-layout-grid grid-3"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6">One-Token Liquidity Provisioning</div><div class="text-block-5">Enter Curve, Balancer, and Gyroscope pools using just one asset; automatically split, routed, and staked.</div></div><div class="w-layout-grid grid-3"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6">Warpdrive Optimization</div><div class="text-block-5">Our in-house routing algorithm finds the most efficient path with built-in slippage and price impact protection.</div></div><div class="w-layout-grid grid-3"><img src="static/picture/67e2b272d55a5b472d75282e_code.svg" loading="lazy" alt=""><div class="text-block-6">Zero Code Required</div><div class="text-block-5">Execute complex strategies without smart contract knowledge—off-chain planning handles it all.</div></div></div></div><div class="bottom-divider-container"><div class="container"><div class="divider"></div></div></div></div><div class="section"><div class="container center-down"><div class="div-block-18"><h1 data-gsap="txt.7" class="h1 light">Builders love us</h1></div><div data-gsap="el.6" class="w-layout-grid grid-12"><div class="w-layout-grid grid-14"><div class="testimonial-block"><div class="text-block-16">The future of UX is in meeting users where they’re at, and that means zapping into DeFi from assets they already hold. Portals made this easy for us to do, and we will happily use them again for this in the future<br></div><div class="w-layout-grid grid-13"><img src="static/picture/681a3db90061fca18f3c237e_alchemix.svg" loading="lazy" alt="" class="image-3"><div id="w-node-_4b69354d-e73f-c59d-a49b-24ea597b719f-2d752801"><div class="text-block-17">Alchemix</div><img src="static/picture/681a3c9b928793731ecbc299_Stars.svg" loading="lazy" alt=""></div></div><img src="static/picture/67e2b272d55a5b472d75283d_Icon.svg" loading="lazy" alt="" class="image-4"></div><div class="testimonial-block"><div class="text-block-16">Portals has been a blessing for us. We haven&#x27;t seen growth like this since DeFi summer.</div><div class="w-layout-grid grid-13"><img src="static/picture/681a3ddadd3ce12e43009558_vault.svg" loading="lazy" alt="" class="image-3"><div id="w-node-_8e36ebc9-901f-0f40-b3c3-8a088b927b55-2d752801"><div class="text-block-17">Vault Protocol</div><img src="static/picture/681a3c9b928793731ecbc299_Stars.svg" loading="lazy" alt=""></div></div><img src="static/picture/67e2b272d55a5b472d75283d_Icon.svg" loading="lazy" alt="" class="image-4"></div><div class="testimonial-block"><div class="text-block-16">Portals provides real-time data even on exotic DeFi protocols, which helps to make sure Velvet users always have the most accurate information</div><div class="w-layout-grid grid-13"><img src="static/picture/682cea65d34e1c33691f4716_images (1).png" loading="lazy" alt="" class="image-3"><div id="w-node-_1ac83d59-5190-4528-83a3-4c96eaa4dae3-2d752801"><div class="text-block-17">Velvet Capital</div><img src="static/picture/681a3c9b928793731ecbc299_Stars.svg" loading="lazy" alt=""></div></div><img src="static/picture/67e2b272d55a5b472d75283d_Icon.svg" loading="lazy" alt="" class="image-4"></div></div><div class="w-layout-grid grid-14"><div class="testimonial-block"><div class="text-block-16">Contango queries Portals via Balmy&#x27;s SDK. As always, the goal is to provide traders with the best price in DeFi</div><div class="w-layout-grid grid-13"><img src="static/picture/681a3dc2b6f408ac7797ebea_contango.svg" loading="lazy" alt="" class="image-3"><div id="w-node-_1a5a0993-0c40-43f2-9ded-9847d7d78006-2d752801"><div class="text-block-17">Contango</div><img src="static/picture/681a3c9b928793731ecbc299_Stars.svg" loading="lazy" alt=""></div></div><img src="static/picture/67e2b272d55a5b472d75283d_Icon.svg" loading="lazy" alt="" class="image-4"></div><div id="w-node-_26bdedef-732d-89bb-4cbe-cb27dff0287b-2d752801" class="testimonial-block"><div class="text-block-16">Currently in the process of implementing Portals as full-fleged market-data provider in our app. Thanks Suhail and team for the amazing work, not only are you providing spot and historical market-data for all LP tokens, but we accidentally discovered while testing, that many lP tokens we already had in the app had totally off market-data!</div><div class="w-layout-grid grid-13"><img src="static/picture/681a3de297226bd107543a06_defi.svg" loading="lazy" alt="" class="image-3"><div id="w-node-_26bdedef-732d-89bb-4cbe-cb27dff02880-2d752801"><div class="text-block-17">DeFi App</div><img src="static/picture/681a3c9b928793731ecbc299_Stars.svg" loading="lazy" alt=""></div></div><img src="static/picture/67e2b272d55a5b472d75283d_Icon.svg" loading="lazy" alt="" class="image-4"></div></div><div class="w-layout-grid grid-14"><div class="testimonial-block"><div class="text-block-16">Portals has been an great integration partner, providing us with a performant API that enables swaps across a wide range of asset pairs. This ultimately improved swap experience for our users.<br></div><div class="w-layout-grid grid-13"><img src="static/picture/681a3dce67189f35218fe4b8_shapeshift.svg" loading="lazy" alt="" class="image-3"><div id="w-node-ec14f5ed-5521-1602-4e56-5a6380543955-2d752801"><div class="text-block-17">Shapeshift</div><img src="static/picture/681a3c9b928793731ecbc299_Stars.svg" loading="lazy" alt=""></div></div><img src="static/picture/67e2b272d55a5b472d75283d_Icon.svg" loading="lazy" alt="" class="image-4"></div><div class="testimonial-block"><div class="text-block-16">Portals Finance&#x27;s API is the quiet engine behind many of Barter&#x27;s best routes. Incredibly fast, reliably accurate and deep enough to cover even the long-tail pairs. It&#x27;s the kind of partner that lets us focus on DeFi innovation instead of infrastructure.<br></div><div class="w-layout-grid grid-13"><img src="static/picture/682330f3b7f6468de1e21293_NpphWcVt_400x400.jpg" loading="lazy" alt="" class="image-3"><div id="w-node-_038afe4f-892a-1b9d-65db-63ef69114f2f-2d752801"><div class="text-block-17">Barter</div><img src="static/picture/681a3c9b928793731ecbc299_Stars.svg" loading="lazy" alt=""></div></div><img src="static/picture/67e2b272d55a5b472d75283d_Icon.svg" loading="lazy" alt="" class="image-4"></div></div></div><div class="bottom-divider-container"><div class="container"><div class="divider"></div></div></div></div><section class="section"><div class="container"><div class="div-block-30"><div id="w-node-_9d9bd8ef-4ad1-aeba-b700-c90562999d51-2d752801"><div class="w-layout-grid menu-logo-grid"><img src="static/picture/67e2b272d55a5b472d75282b_Portals.svg" loading="lazy" alt=""><div class="text-block-2">Portals</div></div></div><div class="text-block-17 center">© 2025 Portals.fi<br></div><div id="w-node-_0b824103-edf3-1925-060f-80374296ab73-2d752801" class="div-block-49"><a id="w-node-_0b824103-edf3-1925-060f-80374296ab74-2d752801" href="https://iq.wiki/wiki/portals" target="_blank" class="footer-social w-inline-block"><img loading="lazy" src="static/picture/681a36cad2d2735704206730_brain.svg" alt="" class="image-15"></a><a id="w-node-_0b824103-edf3-1925-060f-80374296ab76-2d752801" href="https://discord.com/invite/YD7RRMu76s" target="_blank" class="footer-social w-inline-block"><img loading="lazy" src="static/picture/681a36cad2d273570420672d_Socials-2.svg" alt="" class="image-15"></a><a id="w-node-_0b824103-edf3-1925-060f-80374296ab78-2d752801" href="https://twitter.com/portals_fi" target="_blank" class="footer-social w-inline-block"><img loading="lazy" src="static/picture/681a36cad2d273570420672e_Socials-1.svg" alt="" class="image-16"></a><a id="w-node-_0b824103-edf3-1925-060f-80374296ab7a-2d752801" href="https://github.com/portals-fi" target="_blank" class="footer-social w-inline-block"><img loading="lazy" src="static/picture/681a36cad2d273570420672f_GitHub.svg" alt="" class="image-17"></a></div></div></div></section></div><div class="section faq"><div class="container center-down"><div class="div-block-18"><h1 class="h1 light">FAQs</h1></div><div class="fs_accordion-1_component"><div class="fs_accordion-1_embed w-embed w-script"><!-- [Finsweet Attributes] A11Y -->
<script>(()=>{var t="https://cdn.jsdelivr.net/npm/@finsweet/attributes-a11y@1/a11y.js",e=document.querySelector(`script[src="${t}"]`);e||(e=document.createElement("script"),e.async=!0,e.src=t,document.head.append(e));})();</script></div><div class="fs_accordion-1_item"><div id="accordion-1-header-1" tabindex="0" role="button" aria-controls="accordion-1-content-1" aria-expanded="false" class="fs_accordion-1_header"><div class="fs_accordion-1_label"><strong>What are Zaps?</strong></div><div class="fs_accordion-1_arrow-wrapper"><div class="fs_accordion-1_icon w-icon-dropdown-toggle"></div></div></div><div id="accordion-1-content-1" aria-labelledby="accordion-header-1" class="fs_accordion-1_content"><div class="fs_accordion-1_body"><p class="fs_accordion-1_paragraph">Zaps are smart contract interactions that enable users to enter or exit complex DeFi positions using a single token. They streamline participation in multi-asset opportunities such as:<br><span class="text-span-3">Liquidity pools<br>Yield-generating vaults<br>Liquid Staking Derivatives (LSDs)<br>Real-world assets</span></p></div></div></div><div class="fs_accordion-1_item"><div id="accordion-1-header-1" tabindex="0" role="button" aria-controls="accordion-1-content-1" aria-expanded="false" class="fs_accordion-1_header"><div class="fs_accordion-1_label"><strong>What are Intents?</strong></div><div class="fs_accordion-1_arrow-wrapper"><div class="fs_accordion-1_icon w-icon-dropdown-toggle"></div></div></div><div id="accordion-1-content-1" aria-labelledby="accordion-header-1" class="fs_accordion-1_content"><div class="fs_accordion-1_body"><p class="fs_accordion-1_paragraph">Intents offer a user-centric approach to DeFi interactions, focusing on desired outcomes rather than specific steps. The Portals system interprets these intents and executes the optimal series of actions to fulfill them.<br><br>Key features:<br><span class="text-span-3">Abstraction: Specify outcomes, not implementation details<br>Optimization: Efficient path determination considering gas costs, slippage, and market conditions<br>Adaptability: Execution evolves with the DeFi landscape while intents remain constant</span></p></div></div></div><div class="fs_accordion-1_item"><div id="accordion-1-header-1" tabindex="0" role="button" aria-controls="accordion-1-content-1" aria-expanded="false" class="fs_accordion-1_header"><div class="fs_accordion-1_label"><strong>So, what kind of features does Portals offer?</strong></div><div class="fs_accordion-1_arrow-wrapper"><div class="fs_accordion-1_icon w-icon-dropdown-toggle"></div></div></div><div id="accordion-1-content-1" aria-labelledby="accordion-header-1" class="fs_accordion-1_content"><div class="fs_accordion-1_body"><p class="fs_accordion-1_paragraph">Portals provide several core features. For one, our <strong>Swap &amp; Zap API</strong> simplifies sophisticated DeFi strategies and allows performing complex token swaps in a single transaction. For example, you can enter multi-token pools like Curve&#x27;s 3pool or Balancer&#x27;s weighted pools using any token as input. It’s great for reducing the usual hassle of multiple steps and approvals. Slippage and price impact protection is automatically managed in the best way possible.</p></div></div></div></div></div></div><div class="mobile-nav"><div class="m-nav-toggle"><a data-w-id="a4d28ae5-23b9-ca80-45df-9682d5eb110a" style="opacity:0;-webkit-transform:translate3d(0, 100PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 100PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 100PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 100PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);display:none" href="#" class="m-nav-toggle-close w-inline-block"><div class="m-nav-toggle-inner close"><div class="m-nav-close-icon-wrapper"><img alt="close icon" src="static/picture/68233a2fe8c818e2fc7f757a_nav-close-icon.svg" class="m-nav-close-icon"></div></div></a><a data-w-id="a4d28ae5-23b9-ca80-45df-9682d5eb110e" style="display:inline-block;-webkit-transform:translate3d(0, 0PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 0PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 0PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 0PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:1" href="#" class="m-nav-toggle-open w-inline-block"><div class="m-nav-toggle-inner"><p class="m-nav-toggle-description">Menu</p></div></a></div><nav style="display:none;width:100%;height:0%" class="m-nav-overlay"><div class="nav-overlay-gradient-top"></div><div class="nav-overlay-gradient-bottom"></div><div style="-webkit-transform:translate3d(0, 110PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 110PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 110PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 110PX, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="m-nav-content"><ul role="list" class="m-nav-list w-list-unstyled"><li class="m-nav-link-item first-item"><a href="/blog" class="m-nav-link">Blog</a></li><li class="m-nav-link-item"><a href="https://build.portals.fi/auth/signin?callbackUrl=https%3A%2F%2Fbuild.portals.fi%2Fdocs" class="m-nav-link">Developers</a></li><li class="m-nav-link-item"><a href="http://contact.portals.fi/" class="m-nav-link">Contact</a></li><li class="m-nav-link-item last-item"><a href="https://explorer.portals.fi/" target="_blank" class="m-nav-link">Launch App</a></li></ul></div></nav><div class="m-nav-toggle-bg"></div></div><script src="static/js/jquery-3.5.1.min.dc5e7f18c8.js" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script><script src="static/js/webflow.schunk.36b8fb49256177c8.js" type="text/javascript"></script><script src="static/js/webflow.schunk.3b58b0126ec020df.js" type="text/javascript"></script><script src="static/js/webflow.49f730cb.ff4837cca6b02acf.js" type="text/javascript"></script><script async src="static/js/split-type.js"></script><script async src="static/js/gsap.min.js"></script><script async src="static/js/ScrollTrigger.min.js"></script><script async src="static/js/gfluo-free.min.js"></script> <!-- Remove free code link if your using only PRO --><script async src="static/js/gfluo-pro.min.js"></script>       </body></html>