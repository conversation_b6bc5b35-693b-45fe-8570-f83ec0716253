{"code": "(window.webpackJsonp=window.webpackJsonp||[]).push([[24],{616:function(t,s,a){\"use strict\";a.r(s);var c=function(){var t=this,s=t.$createElement,c=t._self._c||s;return c(\"div\",{staticClass:\"share\",attrs:{\"data-v-715b5f9c\":\"\"}},[c(\"div\",{staticClass:\"header\",attrs:{\"data-v-715b5f9c\":\"\"}},[c(\"img\",{attrs:{src:a(737),\"data-v-715b5f9c\":\"\"},on:{click:t.back}})]),t._v(\" \"),t._m(0),t._v(\" \"),c(\"div\",{staticClass:\"share_info_content\",attrs:{\"data-v-715b5f9c\":\"\"}},[c(\"div\",{staticClass:\"share_title fc-0A0B0D fs-40 ff_NunitoBold\",attrs:{\"data-v-715b5f9c\":\"\"}},[t._v(t._s(t.$t(\"key107\")))]),t._v(\" \"),c(\"div\",{staticClass:\"share_subtitle fc-5B616E ff_NunitoRegular\",attrs:{\"data-v-715b5f9c\":\"\"}},[t._v(t._s(t.$t(\"key108\")))]),t._v(\" \"),c(\"div\",{staticClass:\"share_subtitle fc-5B616E ff_NunitoRegular\",attrs:{\"data-v-715b5f9c\":\"\"}},[t._v(t._s(t.$t(\"key109\"))+\"!\")])]),t._v(\" \"),c(\"div\",{staticClass:\"address_container\",attrs:{\"data-v-715b5f9c\":\"\"}},[c(\"div\",{staticClass:\"address_content\",attrs:{\"data-v-715b5f9c\":\"\"}},[c(\"div\",{staticClass:\"address_value fc-353F52 fs-26 ff_NunitoRegular\",attrs:{\"data-v-715b5f9c\":\"\"}},[t._v(\"\\n                  \"+t._s(t.url))]),t._v(\" \"),c(\"span\",{staticClass:\"copy fc-353F52 ff_NunitoSemiBold copyBtn btn-copy\",attrs:{\"data-clipboard-text\":t.url,\"data-v-715b5f9c\":\"\"},on:{click:t.copy}},[t._v(t._s(t.$t(\"key110\")))])])]),t._v(\" \"),c(\"div\",{staticClass:\"submit_container\",attrs:{\"data-v-715b5f9c\":\"\"}},[c(\"div\",{staticClass:\"submit_btn ff_NunitoSemiBold copyBtn btn-copy\",attrs:{\"data-clipboard-text\":t.url,\"data-v-715b5f9c\":\"\"},on:{click:t.copy}},[t._v(\"\\n              \"+t._s(t.$t(\"key111\")))])])])},e=[function(){var t=this.$createElement,s=this._self._c||t;return s(\"div\",{staticClass:\"share_img_content\",attrs:{\"data-v-715b5f9c\":\"\"}},[s(\"img\",{staticClass:\"img_share\",attrs:{src:a(761),\"data-v-715b5f9c\":\"\"}})])}];c._withStripped=!0;var i=a(716),r=a.n(i),n={name:\"share\",props:{},components:{},data:()=>({url:\"\"}),computed:{userinfo(){return this.$store.state.user.userinfo}},mounted(){this.setshareUrl()},methods:{setshareUrl(){const t=document.location.hostname,s=window.location.protocol;this.url=`${s}//${t}?invite=${this.userinfo.invite}`},copy(){var t=new r.a(\".btn-copy\");t.on(\"success\",s=>{this.$toast({message:this.$t(\"key96\"),icon:\"success\"}),t.destroy()}),t.on(\"error\",s=>{this.$toast({message:this.$t(\"key97\"),icon:\"cross\"}),t.destroy()})},back(){this.$router.back()}}},o=(a(861),a(52)),d=Object(o.a)(n,c,e,!1,null,null,null);d.options.__file=\"src/view/home/<USER>\";s.default=d.exports},702:function(t,s,a){},737:function(t,s){t.exports=\"/img/icon_close.7ba8065d_6d2f199eb58915f8f7038237bbc7c153.svg\"},761:function(t,s){t.exports=\"/img/img_share.c6632a1c_4d8849653d3bc68f85e74d25c1d12218.png\"},861:function(t,s,a){\"use strict\";var c=a(702);a.n(c).a}}]);", "extractedComments": []}