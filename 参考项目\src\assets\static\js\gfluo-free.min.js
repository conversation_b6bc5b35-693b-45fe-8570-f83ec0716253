﻿var GfluoBasic=function(e){"use strict";const t={getDuration:(e,t=.5)=>parseFloat(e.getAttribute("gfluo-duration"))||t,createScrollTrigger:(e,t,s={})=>{const r=e.getAttribute("gfluo-scroll-start")||"top 80%",o=e.getAttribute("gfluo-scroll-end"),i=e.getAttribute("gfluo-scroll-scrub"),a=e.getAttribute("gfluo-scroll-toggle")||"play none none reverse",n="true"===e.getAttribute("gfluo-scroll-markers"),l=parseFloat(e.getAttribute("gfluo-delay"))||0;if(!window.gsap||!window.ScrollTrigger)return void console.error("ScrollTrigger plugin is not registered");const p=gsap.timeline({paused:!0,onStart:()=>{gsap.set(e,{visibility:"visible"})}});l&&p.add(gsap.timeline().to({},{duration:l})),p.add(t),ScrollTrigger.create({trigger:e,animation:p,start:r,end:o,scrub:"true"===i,toggleActions:a,markers:n,onEnter:()=>{p.play()},once:!1}),ScrollTrigger.refresh()},splitText:(e,t={})=>{if(!window.SplitType)return console.error("SplitType library is not loaded"),null;try{return new SplitType(e,{types:t.types||"lines, words, chars",tagName:"span"})}catch(e){return console.error("Error splitting text:",e),null}},handleAnimation:(e,s)=>{const r=e.getAttribute("gfluo-trigger"),o=parseFloat(e.getAttribute("gfluo-delay"))||0;if(s)if(gsap.set(e,{visibility:"hidden"}),"load"===r){const t=gsap.timeline({onStart:()=>{gsap.set(e,{visibility:"visible"})}});o&&t.add(gsap.timeline().to({},{duration:o})),t.add(s),requestAnimationFrame((()=>{t.play()}))}else gsap.set(e,{visibility:"hidden"}),t.createScrollTrigger(e,s);else console.error("No animation provided for element:",e)},createBasicAnimation:(e,t={})=>{const s=getConfig(e),r={opacity:1===s.opacity?0:s.opacity,x:s.x,y:s.y,skewX:s.skewX,skewY:s.skewY,...t};return gsap.set(e,r),gsap.to(e,{opacity:1,x:0,y:0,skewX:0,skewY:0,duration:s.duration,ease:s.ease,stagger:s.stagger})}},s={"free.1":{setup:e=>{gsap.set(e,{opacity:1});const s=t.splitText(e,{types:"lines, words, chars"});if(!s)return null;s.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const r=gsap.timeline();return r.set(s.chars,{opacity:0,y:40}),r.to(s.chars,{opacity:1,y:0,duration:.3,stagger:{each:.02,ease:"sine.inOut"}})}},"free.2":{setup:e=>{gsap.set(e,{opacity:1});const s=t.splitText(e,{types:"lines, words, chars"});return s?(s.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.set(s.chars,{transformOrigin:"center center",rotationY:-180,opacity:0}),gsap.to(s.chars,{rotationY:0,opacity:1,duration:.8,stagger:{amount:1},ease:"power2.out"})):null}},"free.3":{setup:e=>{gsap.set(e,{opacity:1});const s=t.splitText(e,{types:"lines, words, chars"});return s?(s.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.set(s.chars,{scale:0,opacity:0}),gsap.to(s.chars,{scale:1,opacity:1,duration:.4,stagger:{amount:.8},ease:"back.out(2)"})):null}},"free.4":{setup:e=>{gsap.set(e,{opacity:1});const s=t.splitText(e,{types:"lines, words"});if(!s)return null;s.words.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.set(s.words,{opacity:0,x:"1em",position:"relative"});return gsap.to(s.words,{opacity:1,x:0,duration:.6,stagger:{amount:.2},ease:"power2.out"})}},"free.5":{setup:e=>{gsap.set(e,{opacity:1});const s=t.splitText(e,{types:"lines, words"});if(!s)return null;s.words.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.set(s.words,{yPercent:100,opacity:0,position:"relative"});return gsap.to(s.words,{yPercent:0,opacity:1,duration:.5,stagger:{amount:.5},ease:"back.out(2)"})}},"free.6":{setup:e=>{gsap.set(e,{opacity:1});const s=t.splitText(e,{types:"lines, words"});if(!s)return null;s.words.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const r=gsap.timeline();return gsap.set(e,{perspective:1e3}),gsap.set(s.words,{transformOrigin:"0% 50%",transformStyle:"preserve-3d"}),r.from(s.words,{rotationX:-90,duration:.6,stagger:{amount:.6},ease:"power2.out"})}},"free.7":{setup:e=>{gsap.set(e,{opacity:1});const s=t.splitText(e,{types:"lines, words, chars"});if(!s)return null;s.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const r=gsap.timeline();return r.set(s.chars,{opacity:0,y:e=>40*Math.sin(.5*e)}),r.to(s.chars,{opacity:1,y:0,duration:.8,stagger:{amount:.5},ease:"elastic.out(1, 0.3)"})}},"free.8":{setup:e=>{gsap.set(e,{opacity:1});const s=t.splitText(e,{types:"lines, words, chars"});if(!s)return null;return gsap.from(s.chars,{opacity:0,duration:.2,stagger:{amount:.8},ease:"power1.out"})}},"free.9":{setup:e=>{gsap.set(e,{opacity:1});const s=t.splitText(e,{types:"lines, words, chars"});if(!s)return null;s.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const r=gsap.timeline();return r.set(s.chars,{opacity:0,scale:3}),r.to(s.chars,{opacity:1,scale:1,duration:.6,stagger:{amount:.8},ease:"power4.out"})}},"free.10":{setup:e=>(gsap.set(e,{opacity:0,x:-100}),gsap.to(e,{opacity:1,x:0,duration:.8,ease:"power2.out"}))},"free.11":{setup:e=>(gsap.set(e,{opacity:0,y:100}),gsap.to(e,{opacity:1,y:0,duration:.8,ease:"power2.out"}))}};class r{constructor(){"undefined"!=typeof window&&window.gsap?window.ScrollTrigger?(gsap.registerPlugin(ScrollTrigger),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(()=>this.initialize())):this.initialize()):console.error("ScrollTrigger plugin is not loaded"):console.error("GSAP is not loaded")}initialize(){document.querySelectorAll("[data-gsap^='free.']").forEach((e=>{const r=e.getAttribute("data-gsap"),o=s[r];if(o){const s=o.setup(e);s&&t.handleAnimation(e,s)}}))}}return"undefined"!=typeof window&&(window.GfluoBasic=r),window.addEventListener("load",(function(){"undefined"!=typeof window&&window.gsap&&window.ScrollTrigger?(window.GfluoBasic=r,window.gfluoBasic=new r):console.error("GfluoBasic is not supported in this environment")})),e.basicAnimations=s,e}({});
//# sourceMappingURL=gfluo-free.min.js.map
