{"code": "(window.webpackJsonp=window.webpackJsonp||[]).push([[18],{610:function(t,e,s){\"use strict\";s.r(e);var a=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"recharge\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"div\",{staticClass:\"header\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"img\",{staticClass:\"back\",attrs:{src:s(734),\"data-v-4e295514\":\"\"},on:{click:t.back}}),t._v(\" \"),a(\"span\",{staticClass:\"titles fs-36 fc-353F52 ff_NunitoSemiBold\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"span\",{staticClass:\"uppercase\",attrs:{\"data-v-4e295514\":\"\"}},[t._v(t._s(t.walletInfo.coin))]),t._v(\" \"+t._s(t.$t(\"key79\"))+\"\\n        \")]),t._v(\" \"),a(\"img\",{staticClass:\"record\",attrs:{src:s(751),\"data-v-4e295514\":\"\"},on:{click:function(e){return t.go(\"/wallet-record\",{coin:t.coin})}}})]),t._v(\" \"),a(\"div\",{staticClass:\"amount\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"div\",{staticClass:\"us_num ff_InterSemiBold\",attrs:{\"data-v-4e295514\":\"\"}},[t._v(\" $ \"+t._s(t.walletInfo.convert_usdt))]),t._v(\" \"),a(\"div\",{staticClass:\"coin_num\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"img\",{staticClass:\"coin_icon\",attrs:{src:t.walletInfo.icon,\"data-v-4e295514\":\"\"}}),t._v(\" \"),a(\"span\",{attrs:{\"data-v-4e295514\":\"\"}},[t._v(\" \"+t._s(t.walletInfo.balance)+\" \"),a(\"span\",{staticClass:\"uppercase\",attrs:{\"data-v-4e295514\":\"\"}},[t._v(t._s(t.walletInfo.coin))])])])]),t._v(\" \"),a(\"div\",{staticClass:\"main_container\",attrs:{\"data-v-4e295514\":\"\"}},[\"USDT\"===t.walletInfo.coin?a(\"div\",[a(\"div\",{staticClass:\"main_content\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"div\",{staticClass:\"titles\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"span\",{staticClass:\"left_icon\",attrs:{\"data-v-4e295514\":\"\"}}),t._v(\" \"),a(\"span\",{attrs:{\"data-v-4e295514\":\"\"}},[t._v(t._s(t.$t(\"key83\")))])]),t._v(\" \"),a(\"div\",{staticClass:\"coin_type\",attrs:{\"data-v-4e295514\":\"\"}},t._l(t.receive_asset,function(e,s){return a(\"div\",{key:s,staticClass:\"coin_item\",class:[t.sendTabIndex===s?\"active\":\"\"],attrs:{\"data-v-4e295514\":\"\"},on:{click:function(e){return t.changesendTabIndex(s)}}},[t._v(\"\\n                        \"+t._s(e.name))])}),0),t._v(\" \"),a(\"div\",{staticClass:\"input_content ff_NunitoSemiBold\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"div\",{staticClass:\"address\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"input\",{staticClass:\"address_input\",attrs:{type:\"text\",readonly:\"\",placeholder:t.$t(\"key89\"),\"data-v-4e295514\":\"\"},domProps:{value:t.sendAddress}})]),t._v(\" \"),a(\"div\",{staticClass:\"address\",attrs:{\"data-v-4e295514\":\"\"},on:{click:function(e){return t.changeshownumberKey(!0)}}},[a(\"img\",{staticClass:\"coin_icon\",attrs:{src:t.walletInfo.icon,\"data-v-4e295514\":\"\"}}),t._v(\" \"),a(\"input\",{directives:[{name:\"model\",rawName:\"v-model\",value:t.num.sendNum,expression:\"num.sendNum\"}],staticClass:\"amount_input\",attrs:{type:\"text\",placeholder:t.$t(\"key9\"),readonly:\"\",\"data-v-4e295514\":\"\"},domProps:{value:t.num.sendNum},on:{input:function(e){e.target.composing||t.$set(t.num,\"sendNum\",e.target.value)}}}),t._v(\" \"),a(\"span\",{staticClass:\"coin_sympol uppercase\",attrs:{\"data-v-4e295514\":\"\"}},[t._v(t._s(t.walletInfo.coin))])])]),t._v(\" \"),a(\"div\",{staticClass:\"send_action fs-32 ff_NunitoBold\",attrs:{\"data-v-4e295514\":\"\"},on:{click:t.withdraw}},[t._v(t._s(t.$t(\"key90\"))+\"\\n                \")]),t._v(\" \"),a(\"div\",{staticClass:\"send_tips ff_NunitoRegular\",attrs:{\"data-v-4e295514\":\"\"}},[t._v(t._s(t.$t(\"key91\"))),a(\"span\",{attrs:{\"data-v-4e295514\":\"\"}},[t._v(\"$10\")])])]),t._v(\" \"),a(\"div\",{staticClass:\"single_tips\",attrs:{\"data-v-4e295514\":\"\"}},[t._v(t._s(t.$t(\"key92\")))])]):a(\"div\",[a(\"div\",{staticClass:\"main_content\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"div\",{staticClass:\"titles\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"span\",{staticClass:\"left_icon\",attrs:{\"data-v-4e295514\":\"\"}}),t._v(\" \"),a(\"span\",{attrs:{\"data-v-4e295514\":\"\"}},[t._v(t._s(t.$t(\"key82\"))+\" usdt\")])]),t._v(\" \"),a(\"div\",{staticClass:\"input_content ff_NunitoSemiBold\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"div\",{staticClass:\"address\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"img\",{staticClass:\"coin_icon\",attrs:{src:t.walletInfo.icon,\"data-v-4e295514\":\"\"}}),t._v(\" \"),a(\"input\",{directives:[{name:\"model\",rawName:\"v-model\",value:t.num.convertNum,expression:\"num.convertNum\"}],staticClass:\"amount_input\",attrs:{type:\"text\",placeholder:t.$t(\"key9\"),readonly:\"\",\"data-v-4e295514\":\"\"},domProps:{value:t.num.convertNum},on:{click:function(e){return t.changeshownumberKey(!0)},input:function(e){e.target.composing||t.$set(t.num,\"convertNum\",e.target.value)}}}),t._v(\" \"),a(\"span\",{staticClass:\"coin_sympol\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"span\",{staticClass:\"uppercase\",attrs:{\"data-v-4e295514\":\"\"}},[t._v(t._s(t.walletInfo.coin))])])]),t._v(\" \"),t._m(0),t._v(\" \"),a(\"div\",{staticClass:\"address swap\",attrs:{\"data-v-4e295514\":\"\"}},[a(\"img\",{staticClass:\"coin_icon\",attrs:{src:s(727),\"data-v-4e295514\":\"\"}}),t._v(\" \"),a(\"input\",{directives:[{name:\"model\",rawName:\"v-model\",value:t.exchange_rate,expression:\"exchange_rate\"}],staticClass:\"amount_input\",attrs:{type:\"text\",placeholder:t.$t(\"key9\"),readonly:\"\",\"data-v-4e295514\":\"\"},domProps:{value:t.exchange_rate},on:{input:function(e){e.target.composing||(t.exchange_rate=e.target.value)}}}),t._v(\" \"),a(\"span\",{staticClass:\"coin_sympol\",attrs:{\"data-v-4e295514\":\"\"}},[t._v(\" USDT \")])])]),t._v(\" \"),a(\"div\",{staticClass:\"send_action fs-32 ff_NunitoBold\",attrs:{\"data-v-4e295514\":\"\"},on:{click:t.exchangeCoin}},[t._v(\"\\n                    \"+t._s(t.$t(\"key82\")))])]),t._v(\" \"),a(\"div\",{staticClass:\"single_tips\",attrs:{\"data-v-4e295514\":\"\"}},[t._v(t._s(t.$t(\"key92\")))])])]),t._v(\" \"),a(\"van-number-keyboard\",{attrs:{theme:\"custom\",\"extra-key\":\".\",\"close-button-text\":\"Close\",show:t.shownumberKey},on:{blur:function(e){t.shownumberKey=!1},input:t.onInput,delete:t.onDelete}})],1)},n=[function(){var t=this.$createElement,e=this._self._c||t;return e(\"div\",{staticClass:\"img_swap_content\",attrs:{\"data-v-4e295514\":\"\"}},[e(\"img\",{staticClass:\"icon_swap\",attrs:{src:s(752),\"data-v-4e295514\":\"\"}})])}];a._withStripped=!0;var i=s(648),c=s(717),r=s.n(c),d=s(66),o=s(943),l=s.n(o),u={name:\"wallet-info\",props:{},components:{tab:i.default},data:()=>({fileList:[],exchange_rate:\"\",shownumberKey:!1,num:{convertNum:\"\",sendNum:\"\",rechargeNumber:\"\"},tabindex:1,codeTabIndex:0,sendTabIndex:0,coin:\"\",walletInfo:{},receive_asset:[],send_asset:[],receiveAddress:\"\",sendAddress:\"\",rechargeImgBase64:\"\",file:null}),mounted(){this.coin=this.$route.query.coin,this.getWalletDetails()},methods:{deleteFile(){this.rechargeImgBase64=\"\",this.file=null,this.fileList=[]},afterRead(t){this.fileList=[{url:t.content,isImage:!0}],this.rechargeImgBase64=t.content,this.file=t.file},recharge(){if(!this.num.rechargeNumber)return this.$toast(this.$t(\"key30\"));if(!this.file)return this.$toast(\"key95\");var t=new FormData;t.append(\"coin\",this.walletInfo.coin),t.append(\"amount\",this.num.rechargeNumber),t.append(\"image\",this.file),Object(d.recharge)(t).then(t=>{let e=t.data;this.$toast(e.msg),1===e.code&&(this.getWalletDetails(),this.num.rechargeNumber=\"\",this.deleteFile())})},withdraw(){if(!this.num.sendNum)return this.$toast(this.$t(\"key30\"));Object(d.withdraw)({coin:this.walletInfo.coin,amount:this.num.sendNum,link:this.sendAddress}).then(t=>{let e=t.data;this.$toast(e.msg),1===e.code&&(this.getWalletDetails(),this.num.sendNum=\"\")})},exchangeCoin(){if(!this.num.convertNum)return this.$toast(this.$t(\"key30\"));Object(d.exchangeCoin)({coin:this.walletInfo.coin,amount:this.num.convertNum}).then(t=>{let e=t.data;this.$toast(e.msg),1===e.code&&(this.num.convertNum=\"\",this.changeRate(),this.getWalletDetails())})},useqrcode(){var t=document.getElementById(\"yqrcode\");l.a.toCanvas(t,this.receiveAddress,{width:180,height:180,margin:1})},getWalletDetails(){Object(d.getWalletDetails)({coin:this.coin}).then(t=>{let e=t.data;if(1===e.code){this.walletInfo=e.data,this.receive_asset=[],this.send_asset=[];for(let t in e.data.receive_asset)this.receive_asset.push({name:t,address:e.data.receive_asset[t]});for(let t in e.data.send_asset)this.send_asset.push({name:t,address:e.data.send_asset[t]});this.receiveAddress=this.receive_asset[0].address,this.sendAddress=this.send_asset[0].address}})},back(){this.$router.back()},go(t,e){this.$router.push({path:t,query:e})},changeshownumberKey(t){this.shownumberKey=t},onInput(t){let e=\"\";2===this.tabindex&&(e=\"convertNum\"),1===this.tabindex&&(e=\"sendNum\"),0===this.tabindex&&(e=\"rechargeNumber\"),this.num[e]+=t,\"convertNum\"===e&&this.changeRate()},onDelete(){let t=\"\";2===this.tabindex&&(t=\"convertNum\"),1===this.tabindex&&(t=\"sendNum\"),0===this.tabindex&&(t=\"rechargeNumber\"),this.num[t]=this.num[t].substring(0,this.num[t].length-1),\"convertNum\"===t&&this.changeRate()},changeRate(){this.num.convertNum?this.exchange_rate=(Number(this.num.convertNum)*this.walletInfo.exchange_rate).toFixed(4):this.exchange_rate=\"\"},copy(){var t=new r.a(\".btn-copy\");t.on(\"success\",e=>{this.$toast({message:this.$t(\"key96\"),icon:\"success\"}),t.destroy()}),t.on(\"error\",e=>{this.$toast({message:this.$t(\"key97\"),icon:\"cross\"}),t.destroy()})},changesendTabIndex(t){this.sendAddress=this.send_asset[t].address,this.sendTabIndex=t},changecodeTabIndex(t){this.receiveAddress=this.receive_asset[t].address,this.codeTabIndex=t,this.useqrcode()}}},v=(s(854),s(52)),h=Object(v.a)(u,a,n,!1,null,null,null);h.options.__file=\"src/view/center/wallet-info.vue\";e.default=h.exports},637:function(t,e,s){},648:function(t,e,s){\"use strict\";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s(\"div\",{staticClass:\"switch_container\",attrs:{\"data-v-7932180b\":\"\"}},[s(\"div\",{staticClass:\"switch_content\",style:\"width: \"+t.width,attrs:{\"data-v-7932180b\":\"\"}},t._l(t.title,function(e,a){return s(\"div\",{key:a,staticClass:\"switch_item\",class:[t.value===a?\"active\":\"\"],attrs:{\"data-v-7932180b\":\"\"},on:{click:function(e){return t.changeTabindex(a)}}},[t._v(t._s(e))])}),0)])};a._withStripped=!0;var n={name:\"tab\",props:{title:{default:[]},width:{default:\"4.44rem\"},value:{default:0}},components:{},data:()=>({}),mounted(){},methods:{changeTabindex(t){this.$emit(\"input\",t)}}},i=(s(663),s(52)),c=Object(i.a)(n,a,[],!1,null,null,null);c.options.__file=\"src/components/tab.vue\";e.default=c.exports},663:function(t,e,s){\"use strict\";var a=s(637);s.n(a).a},696:function(t,e,s){},727:function(t,e){t.exports=\"/img/61d50277ca8872786ce33a35a650843c.93e5b38b_4b39119ebd1e193b3921c93e1748effc.png\"},734:function(t,e){t.exports=\"/img/icon_back.0b3c36a6.ac21430a_35b5bd42099782cc4ae7e4761825f9e4.svg\"},751:function(t,e){t.exports=\"/img/icon_record.7c965f76.6096f376_aff0d86a8cd8a550d5049a16cc439041.svg\"},752:function(t,e){t.exports=\"/img/icon_swap.185cd8f3_30d9cd78a3ffbd80d5f261824beac466.svg\"},854:function(t,e,s){\"use strict\";var a=s(696);s.n(a).a}}]);", "extractedComments": []}