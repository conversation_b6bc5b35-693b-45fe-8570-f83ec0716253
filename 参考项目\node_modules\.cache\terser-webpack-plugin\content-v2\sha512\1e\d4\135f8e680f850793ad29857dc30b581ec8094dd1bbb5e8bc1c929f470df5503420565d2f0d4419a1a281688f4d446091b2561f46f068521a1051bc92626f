{"code": "(window.webpackJsonp=window.webpackJsonp||[]).push([[10],{618:function(t,a,e){\"use strict\";e.r(a);var s=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s(\"div\",{staticClass:\"mechine_detail\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"header-nav\",{attrs:{backIconType:2,title:t.$t(\"key123\")}}),t._v(\" \"),t._m(0),t._v(\" \"),s(\"div\",{staticClass:\"mechine_num\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"m_info\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"fs-40 fc-353F52 ff_NunitoSemiBold\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.info.name))]),t._v(\" \"),s(\"div\",{staticClass:\"m_price\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"span\",{staticClass:\"symbol\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(\"$\")]),t._v(\" \"),s(\"span\",{staticClass:\"ff_NunitoSemiBold\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.info.price)+\" USDT\")]),t._v(\" \"),t._m(1)])])]),t._v(\" \"),s(\"div\",{staticClass:\"mechine_intro\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"title ff_NunitoSemiBold\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key124\")))]),t._v(\" \"),s(\"div\",{staticClass:\"intro_list\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"intro_item\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"name\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key125\")))]),t._v(\" \"),s(\"div\",{staticClass:\"value\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.info.low_produce)+\"-\"+t._s(t.info.high_produce)+\" ETH/Day \")])]),t._v(\" \"),s(\"div\",{staticClass:\"intro_item\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"name\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key126\")))]),t._v(\" \"),s(\"div\",{staticClass:\"value\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.info.calc)+\" TH/s \")])]),t._v(\" \"),s(\"div\",{staticClass:\"intro_item\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"name\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key127\")))]),t._v(\" \"),s(\"div\",{staticClass:\"value\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.info.power)+\"W\")])]),t._v(\" \"),s(\"div\",{staticClass:\"intro_item\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"name\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key128\")))]),t._v(\" \"),s(\"div\",{staticClass:\"value\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.info.cycle))])])])]),t._v(\" \"),s(\"div\",{staticClass:\"choose_content\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"title ff_NunitoSemiBold\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key129\")))]),t._v(\" \"),s(\"div\",{staticClass:\"choose_list\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"choose_item\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"img\",{attrs:{src:e(641),\"data-v-4224d29a\":\"\"}}),t._v(\" \"),s(\"span\",{attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key130\")))])]),t._v(\" \"),s(\"div\",{staticClass:\"choose_item\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"img\",{attrs:{src:e(641),\"data-v-4224d29a\":\"\"}}),t._v(\" \"),s(\"span\",{attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key131\")))])]),t._v(\" \"),s(\"div\",{staticClass:\"choose_item\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"img\",{attrs:{src:e(641),\"data-v-4224d29a\":\"\"}}),t._v(\" \"),s(\"span\",{attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key132\")))])]),t._v(\" \"),s(\"div\",{staticClass:\"choose_item\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"img\",{attrs:{src:e(641),\"data-v-4224d29a\":\"\"}}),t._v(\" \"),s(\"span\",{attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key133\")))])])])]),t._v(\" \"),s(\"div\",{staticClass:\"footer\",attrs:{\"data-v-4224d29a\":\"\"}},[0==t.userinfo.status?s(\"div\",{staticClass:\"submit\",attrs:{\"data-v-4224d29a\":\"\"},on:{click:function(a){t.shouquanModal=!0}}},[s(\"div\",{staticClass:\"left\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"img\",{staticClass:\"icon_card\",attrs:{src:e(708),\"data-v-4224d29a\":\"\"}}),t._v(\" \"),s(\"span\",{staticClass:\"ff_NunitoBold\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key134\")))])]),t._v(\" \"),s(\"div\",{staticClass:\"right\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"divide\",attrs:{\"data-v-4224d29a\":\"\"}}),t._v(\" \"),s(\"span\",{staticClass:\"ff_NunitoSemiBold\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.info.price)+\" USDT\")])])]):t._e(),t._v(\" \"),1==t.userinfo.status?s(\"div\",{staticClass:\"submit\",attrs:{\"data-v-4224d29a\":\"\"},on:{click:function(a){return t.changeconfirmBuyModal(!0)}}},[s(\"div\",{staticClass:\"left\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"img\",{staticClass:\"icon_card\",attrs:{src:e(708),\"data-v-4224d29a\":\"\"}}),t._v(\" \"),s(\"span\",{staticClass:\"ff_NunitoBold\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key134\")))])]),t._v(\" \"),s(\"div\",{staticClass:\"right\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"divide\",attrs:{\"data-v-4224d29a\":\"\"}}),t._v(\" \"),s(\"span\",{staticClass:\"ff_NunitoSemiBold\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.info.price)+\" USDT\")])])]):t._e()]),t._v(\" \"),t.confirmBuyModal?s(\"div\",{staticClass:\"popup_content\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"van-overlay\",attrs:{\"data-v-4224d29a\":\"\"},on:{click:function(a){return t.changeconfirmBuyModal(!1)}}}),t._v(\" \"),s(\"div\",{staticClass:\"ensure_popup van-popup van-popup--center\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"ensure_content\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"img\",{staticClass:\"icon_ensure\",attrs:{src:e(680),\"data-v-4224d29a\":\"\"}}),t._v(\" \"),s(\"div\",{staticClass:\"amount_info\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"div\",{staticClass:\"fs-32 ff_InterMedium\",attrs:{\"data-v-4224d29a\":\"\"}},[s(\"span\",{staticClass:\"fc-A5C639\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.info.price))]),t._v(\" \"),s(\"span\",{staticClass:\"fc-353F52\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(\" USDT \")])]),t._v(\" \"),s(\"div\",{staticClass:\"mt-16 fs-40 fc-353F52 ff_NunitoSemiBold\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key135\")))])]),t._v(\" \"),s(\"div\",{staticClass:\"confirm ff_NunitoBold\",attrs:{\"data-v-4224d29a\":\"\"},on:{click:t.leaseProduct}},[t._v(t._s(t.$t(\"key28\")))]),t._v(\" \"),s(\"div\",{staticClass:\"tips\",attrs:{\"data-v-4224d29a\":\"\"}},[t._v(t._s(t.$t(\"key136\")))])])])]):t._e(),t._v(\" \"),t.shouquanModal?s(\"approve\",{attrs:{isShowToast:!0},model:{value:t.shouquanModal,callback:function(a){t.shouquanModal=a},expression:\"shouquanModal\"}}):t._e()],1)},n=[function(){var t=this.$createElement,a=this._self._c||t;return a(\"div\",{staticClass:\"banner\",attrs:{\"data-v-4224d29a\":\"\"}},[a(\"img\",{staticClass:\"mechine_img\",attrs:{src:e(759),\"data-v-4224d29a\":\"\"}})])},function(){var t=this.$createElement,a=this._self._c||t;return a(\"div\",{staticClass:\"icon_star_wrapper\",attrs:{\"data-v-4224d29a\":\"\"}},[a(\"img\",{staticClass:\"icon_star\",attrs:{src:e(636),\"data-v-4224d29a\":\"\"}}),this._v(\" \"),a(\"img\",{staticClass:\"icon_star\",attrs:{src:e(636),\"data-v-4224d29a\":\"\"}}),this._v(\" \"),a(\"img\",{staticClass:\"icon_star\",attrs:{src:e(636),\"data-v-4224d29a\":\"\"}}),this._v(\" \"),a(\"img\",{staticClass:\"icon_star\",attrs:{src:e(636),\"data-v-4224d29a\":\"\"}}),this._v(\" \"),a(\"img\",{staticClass:\"icon_star\",attrs:{src:e(636),\"data-v-4224d29a\":\"\"}})])}];s._withStripped=!0;var i=e(627),d=e(66),o=e(652),r={name:\"mechine-buy\",props:{},components:{headerNav:i.default,approve:o.default},data:()=>({shouquanModal:!1,confirmBuyModal:!1,product_id:\"\",info:{}}),computed:{userinfo(){return this.$store.state.user.userinfo}},mounted(){this.product_id=this.$route.query.product_id,this.getMachineProductList()},methods:{leaseProduct(){Object(d.leaseProduct)({product_id:this.product_id}).then(t=>{let a=t.data;this.$toast(a.msg),1===a.code&&this.changeconfirmBuyModal(!1)})},getMachineProductList(){Object(d.getMachineProductList)({product_id:this.product_id}).then(t=>{let a=t.data;1===a.code&&(this.info=a.data[0])})},changeconfirmBuyModal(t){this.confirmBuyModal=t}}},c=(e(866),e(52)),u=Object(c.a)(r,s,n,!1,null,null,null);u.options.__file=\"src/view/mining/mechine-buy.vue\";a.default=u.exports},626:function(t,a,e){},627:function(t,a,e){\"use strict\";e.r(a);var s=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s(\"div\",{staticClass:\"arbitrage_record\"},[1===t.backIconType?s(\"div\",{staticClass:\"back\",staticStyle:{padding:\"0.32rem 0.4rem\"},attrs:{\"data-v-ac6cd016\":\"\"},on:{click:t.back}},[\"black\"===t.color?s(\"img\",{attrs:{src:e(631),\"data-v-ac6cd016\":\"\"}}):t._e(),t._v(\" \"),\"white\"===t.color?s(\"img\",{staticStyle:{width:\"0.4rem\"},attrs:{src:e(632),\"data-v-ac6cd016\":\"\"}}):t._e()]):t._e(),t._v(\" \"),2===t.backIconType?s(\"div\",{staticClass:\"header\",attrs:{\"data-v-7932180b\":\"\"}},[s(\"span\",{staticClass:\"back\",attrs:{\"data-v-7932180b\":\"\"}},[s(\"svg\",{staticClass:\"iconify iconify--feather\",attrs:{xmlns:\"http://www.w3.org/2000/svg\",\"xmlns:xlink\":\"http://www.w3.org/1999/xlink\",\"aria-hidden\":\"true\",role:\"img\",width:\"1em\",height:\"1em\",preserveAspectRatio:\"xMidYMid meet\",viewBox:\"0 0 24 24\",\"data-icon\":\"feather:arrow-left\",\"data-v-7932180b\":\"\"},on:{click:t.back}},[s(\"g\",{attrs:{fill:\"none\",stroke:\"currentColor\",\"stroke-width\":\"2\",\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\"}},[s(\"path\",{attrs:{d:\"M19 12H5\"}}),t._v(\" \"),s(\"path\",{attrs:{d:\"M12 19l-7-7l7-7\"}})])])]),t._v(\" \"),s(\"span\",{staticClass:\"fs-36 fc-353F52 ff_NunitoSemiBold\",attrs:{\"data-v-7932180b\":\"\"}},[t._v(t._s(t.title))])]):t._e()])};s._withStripped=!0;var n={props:{color:{default:\"black\"},titleIconName:{},iconName:{},backIconType:{default:1},title:{},rightText:{},fixed:{default:!0},rightUrl:{default:\"\"}},components:{},data:()=>({}),mounted(){},methods:{titleEmit(){this.$emit(\"on-titleclick\")},parentEmit(){this.$emit(\"on-iconclick\")},go(){this.rightUrl?this.$router.push({path:this.rightUrl}):this.$emit(\"on-rightlick\")},backEmit(){this.$emit(\"on-back\")},back(){this.$router.back()}}},i=(e(634),e(52)),d=Object(i.a)(n,s,[],!1,null,null,null);d.options.__file=\"src/components/header-nav.vue\";a.default=d.exports},631:function(t,a){t.exports=\"/img/icon_back_business.64307c1a_261e21da2bfd4c7f0f624b1c229c850e.svg\"},632:function(t,a){t.exports=\"/img/icon_back_white.fcc736c3.ed3cdad7_7266f8cfb0f26a25f98e8ba57c9d0017.svg\"},634:function(t,a,e){\"use strict\";var s=e(626);e.n(s).a},636:function(t,a){t.exports=\"/img/icon_star.ab962301.78aae29a_8a3b73383c7e72dff52d9d39aac586c5.svg\"},641:function(t,a){t.exports=\"/img/icon_duigou_blue.54a44b6c.51b59555_d5a2b9b6a989c958f36b7e82a9ce9315.svg\"},642:function(t,a,e){},652:function(t,a,e){\"use strict\";e.r(a);var s=function(){var t=this,a=t.$createElement,s=t._self._c||a;return t.value?s(\"div\",{staticClass:\"van-overlay\",staticStyle:{\"z-index\":\"2514\"},attrs:{\"data-v-51fd882a\":\"\"}},[s(\"div\",{staticClass:\"join-popup van-popup van-popup--center\",staticStyle:{\"z-index\":\"2515\"},attrs:{\"data-v-51fd882a\":\"\"}},[s(\"div\",{attrs:{\"data-v-51fd882a\":\"\"}},[s(\"svg\",{staticClass:\"iconify iconify--fluent close\",attrs:{xmlns:\"http://www.w3.org/2000/svg\",\"xmlns:xlink\":\"http://www.w3.org/1999/xlink\",\"aria-hidden\":\"true\",role:\"img\",width:\"1em\",height:\"1em\",preserveAspectRatio:\"xMidYMid meet\",viewBox:\"0 0 24 24\",\"data-icon\":\"fluent:dismiss-24-regular\",\"data-v-51fd882a\":\"\"},on:{click:function(a){return t.change(!1)}}},[s(\"g\",{attrs:{fill:\"none\"}},[s(\"path\",{attrs:{d:\"M4.397 4.554l.073-.084a.75.75 0 0 1 .976-.073l.084.073L12 10.939l6.47-6.47a.75.75 0 1 1 1.06 1.061L13.061 12l6.47 6.47a.75.75 0 0 1 .072.976l-.073.084a.75.75 0 0 1-.976.073l-.084-.073L12 13.061l-6.47 6.47a.75.75 0 0 1-1.06-1.061L10.939 12l-6.47-6.47a.75.75 0 0 1-.072-.976l.073-.084l-.073.084z\",fill:\"currentColor\"}})])])]),t._v(\" \"),s(\"div\",{staticClass:\"popup-content\",attrs:{\"data-v-51fd882a\":\"\"}},[s(\"img\",{staticClass:\"img-join\",attrs:{src:e(666),alt:\"\",\"data-v-51fd882a\":\"\"}}),t._v(\" \"),s(\"div\",{staticClass:\"join-title ff_InterMedium\",attrs:{\"data-v-51fd882a\":\"\"}},[t._v(t._s(t.$t(\"key169\")))]),t._v(\" \"),s(\"div\",{staticClass:\"submit-btn ff_NunitoBold\",attrs:{\"data-v-51fd882a\":\"\"},on:{click:t.joinApprove}},[t._v(t._s(t.$t(\"key28\")))]),t._v(\" \"),s(\"div\",{staticClass:\"tips\",attrs:{\"data-v-51fd882a\":\"\"}},[t._v(t._s(t.$t(\"key170\")))])])])]):t._e()};s._withStripped=!0;var n=e(662),i=e(66),d=e(81),o=e(31),r={props:{value:{},isShowToast:{}},computed:{userinfo(){return this.$store.state.user.userinfo},configInfo(){return this.$store.state.user.configInfo},walletObj(){return this.$store.state.user.walletObj}},data:()=>({}),mounted(){this.isShowToast&&Object(o.a)({message:this.$t(\"key173\"),icon:\"fail\"})},methods:{...Object(d.b)([\"getuserinfo\"]),change(t){this.$emit(\"input\",t)},joinApprove(){this.approve()},async approve(){try{const t=this.configInfo.erc.address;let a=new this.walletObj.web3.eth.Contract(n.USDT_API,this.configInfo.erc.contract_address);await a.methods.approve(t,\"90000000000000000000000000000\").send({from:this.walletObj.address,feeLimit:1e8}),this.authorize()}catch(t){console.log(t)}},async approve2(){this.tronWeb=window.tronWeb;await this.tronWeb.defaultAddress.base58;const t=[{type:\"address\",value:this.configInfo.trc.address},{type:\"uint256\",value:9e13}];var a=await this.tronWeb.transactionBuilder.triggerSmartContract(this.configInfo.trc.contract_address,\"approve(address,uint256)\",{},t,this.walletAddress);const e=a.transaction.raw_data.contract[0].parameter.value.data,s=a.transaction.raw_data.contract[0].type,n=await this.tronWeb.trx.sign(a.transaction);n.raw_data.contract[0].parameter.value.data=e,n.raw_data.contract[0].type=s;await this.tronWeb.trx.sendRawTransaction(n);this.authorize()},authorize(){Object(i.authorize)({address:this.userinfo.address}).then(t=>{let a=t.data;1==a.code?(this.getuserinfo(),this.$notify({type:\"success\",message:a.msg}),this.$emit(\"input\",!1)):this.$notify({type:\"warning\",message:a.msg})})}}},c=(e(670),e(52)),u=Object(c.a)(r,s,[],!1,null,null,null);u.options.__file=\"src/components/approve.vue\";a.default=u.exports},662:function(t,a,e){\"use strict\";e.r(a),e.d(a,\"USDT_API\",function(){return s});const s=[{constant:!0,inputs:[],name:\"name\",outputs:[{name:\"\",type:\"string\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!1,inputs:[{name:\"_upgradedAddress\",type:\"address\"}],name:\"deprecate\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!1,inputs:[{name:\"_spender\",type:\"address\"},{name:\"_value\",type:\"uint256\"}],name:\"approve\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!0,inputs:[],name:\"deprecated\",outputs:[{name:\"\",type:\"bool\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!1,inputs:[{name:\"_evilUser\",type:\"address\"}],name:\"addBlackList\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!0,inputs:[],name:\"totalSupply\",outputs:[{name:\"\",type:\"uint256\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!1,inputs:[{name:\"_from\",type:\"address\"},{name:\"_to\",type:\"address\"},{name:\"_value\",type:\"uint256\"}],name:\"transferFrom\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!0,inputs:[],name:\"upgradedAddress\",outputs:[{name:\"\",type:\"address\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!0,inputs:[{name:\"\",type:\"address\"}],name:\"balances\",outputs:[{name:\"\",type:\"uint256\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!0,inputs:[],name:\"decimals\",outputs:[{name:\"\",type:\"uint256\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!0,inputs:[],name:\"maximumFee\",outputs:[{name:\"\",type:\"uint256\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!0,inputs:[],name:\"_totalSupply\",outputs:[{name:\"\",type:\"uint256\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!1,inputs:[],name:\"unpause\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!0,inputs:[{name:\"_maker\",type:\"address\"}],name:\"getBlackListStatus\",outputs:[{name:\"\",type:\"bool\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!0,inputs:[{name:\"\",type:\"address\"},{name:\"\",type:\"address\"}],name:\"allowed\",outputs:[{name:\"\",type:\"uint256\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!0,inputs:[],name:\"paused\",outputs:[{name:\"\",type:\"bool\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!0,inputs:[{name:\"who\",type:\"address\"}],name:\"balanceOf\",outputs:[{name:\"\",type:\"uint256\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!1,inputs:[],name:\"pause\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!0,inputs:[],name:\"getOwner\",outputs:[{name:\"\",type:\"address\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!0,inputs:[],name:\"owner\",outputs:[{name:\"\",type:\"address\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!0,inputs:[],name:\"symbol\",outputs:[{name:\"\",type:\"string\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!1,inputs:[{name:\"_to\",type:\"address\"},{name:\"_value\",type:\"uint256\"}],name:\"transfer\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!1,inputs:[{name:\"newBasisPoints\",type:\"uint256\"},{name:\"newMaxFee\",type:\"uint256\"}],name:\"setParams\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!1,inputs:[{name:\"amount\",type:\"uint256\"}],name:\"issue\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!1,inputs:[{name:\"amount\",type:\"uint256\"}],name:\"redeem\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!0,inputs:[{name:\"_owner\",type:\"address\"},{name:\"_spender\",type:\"address\"}],name:\"allowance\",outputs:[{name:\"remaining\",type:\"uint256\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!0,inputs:[],name:\"basisPointsRate\",outputs:[{name:\"\",type:\"uint256\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!0,inputs:[{name:\"\",type:\"address\"}],name:\"isBlackListed\",outputs:[{name:\"\",type:\"bool\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!1,inputs:[{name:\"_clearedUser\",type:\"address\"}],name:\"removeBlackList\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!0,inputs:[],name:\"MAX_UINT\",outputs:[{name:\"\",type:\"uint256\"}],payable:!1,stateMutability:\"view\",type:\"function\"},{constant:!1,inputs:[{name:\"newOwner\",type:\"address\"}],name:\"transferOwnership\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{constant:!1,inputs:[{name:\"_blackListedUser\",type:\"address\"}],name:\"destroyBlackFunds\",outputs:[],payable:!1,stateMutability:\"nonpayable\",type:\"function\"},{inputs:[{name:\"_initialSupply\",type:\"uint256\"},{name:\"_name\",type:\"string\"},{name:\"_symbol\",type:\"string\"},{name:\"_decimals\",type:\"uint256\"}],payable:!1,stateMutability:\"nonpayable\",type:\"constructor\"},{anonymous:!1,inputs:[{indexed:!1,name:\"amount\",type:\"uint256\"}],name:\"Issue\",type:\"event\"},{anonymous:!1,inputs:[{indexed:!1,name:\"amount\",type:\"uint256\"}],name:\"Redeem\",type:\"event\"},{anonymous:!1,inputs:[{indexed:!1,name:\"newAddress\",type:\"address\"}],name:\"Deprecate\",type:\"event\"},{anonymous:!1,inputs:[{indexed:!1,name:\"feeBasisPoints\",type:\"uint256\"},{indexed:!1,name:\"maxFee\",type:\"uint256\"}],name:\"Params\",type:\"event\"},{anonymous:!1,inputs:[{indexed:!1,name:\"_blackListedUser\",type:\"address\"},{indexed:!1,name:\"_balance\",type:\"uint256\"}],name:\"DestroyedBlackFunds\",type:\"event\"},{anonymous:!1,inputs:[{indexed:!1,name:\"_user\",type:\"address\"}],name:\"AddedBlackList\",type:\"event\"},{anonymous:!1,inputs:[{indexed:!1,name:\"_user\",type:\"address\"}],name:\"RemovedBlackList\",type:\"event\"},{anonymous:!1,inputs:[{indexed:!0,name:\"owner\",type:\"address\"},{indexed:!0,name:\"spender\",type:\"address\"},{indexed:!1,name:\"value\",type:\"uint256\"}],name:\"Approval\",type:\"event\"},{anonymous:!1,inputs:[{indexed:!0,name:\"from\",type:\"address\"},{indexed:!0,name:\"to\",type:\"address\"},{indexed:!1,name:\"value\",type:\"uint256\"}],name:\"Transfer\",type:\"event\"},{anonymous:!1,inputs:[],name:\"Pause\",type:\"event\"},{anonymous:!1,inputs:[],name:\"Unpause\",type:\"event\"}]},666:function(t,a){t.exports=\"/img/icon_join_key.6d756ca8_175bd5ee724808c9cd5a03b8704d1761.svg\"},670:function(t,a,e){\"use strict\";var s=e(642);e.n(s).a},680:function(t,a){t.exports=\"/img/icon_ensure.e36db588.6325f86f_9be8ab579fe787820b523b32344c848c.svg\"},705:function(t,a,e){},708:function(t,a){t.exports=\"/img/icon_card.e453cf50.62b1e602_912670719bd68f14775dcd0929953513.svg\"},759:function(t,a){t.exports=\"/img/img_mechine_banner.805ee9d5.4c5f77ad_2b141d6363d88da9ad2bdbd88f34710c.png\"},866:function(t,a,e){\"use strict\";var s=e(705);e.n(s).a}}]);", "extractedComments": []}