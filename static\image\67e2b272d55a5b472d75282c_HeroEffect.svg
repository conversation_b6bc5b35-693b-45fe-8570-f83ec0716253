<svg width="1440" height="900" viewBox="0 0 1440 900" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2158_8128)">
<rect x="-405.89" y="50.0898" width="560" height="1380" transform="rotate(-45 -405.89 50.0898)" fill="url(#paint0_radial_2158_8128)"/>
<rect x="-479.42" y="-496.39" width="240" height="1380" transform="rotate(-45 -479.42 -496.39)" fill="url(#paint1_radial_2158_8128)"/>
<rect x="-988.54" y="-377.6" width="240" height="1380" rx="20" transform="rotate(-45 -988.54 -377.6)" fill="url(#paint2_radial_2158_8128)"/>
<g opacity="0.3" filter="url(#filter0_f_2158_8128)">
<path d="M1221 -194H219C98.0496 -194 0 -95.9504 0 25C0 145.95 98.0496 244 219 244H1221C1341.95 244 1440 145.95 1440 25C1440 -95.9504 1341.95 -194 1221 -194Z" fill="#9152FF"/>
</g>
</g>
<defs>
<filter id="filter0_f_2158_8128" x="-300" y="-494" width="2040" height="1038" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_2158_8128"/>
</filter>
<radialGradient id="paint0_radial_2158_8128" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-97.7777 484.238) scale(383.824 948.336)">
<stop stop-color="#D9D9D9" stop-opacity="0.08"/>
<stop offset="0.5" stop-color="#8C8C8C" stop-opacity="0.02"/>
<stop offset="0.8" stop-color="#737373" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint1_radial_2158_8128" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-359.42 193.61) scale(120 690)">
<stop stop-color="#D9D9D9" stop-opacity="0.06"/>
<stop offset="0.8" stop-color="#737373" stop-opacity="0.02"/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial_2158_8128" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-868.54 312.4) scale(120 690)">
<stop stop-color="#D9D9D9" stop-opacity="0.04"/>
<stop offset="0.8" stop-color="#737373" stop-opacity="0.02"/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_2158_8128">
<rect width="1440" height="900" fill="white"/>
</clipPath>
</defs>
</svg>
