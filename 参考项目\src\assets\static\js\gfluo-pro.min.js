﻿var Gfluo=function(){"use strict";const e={duration:.8,stagger:0,ease:"power2.out",x:0,y:0,skew:0,skewX:0,skewY:0,opacity:1},t=(t,a={})=>{const s={...e,...a};if(!t||!t.hasAttribute)return console.warn("Invalid element provided to getConfig"),s;return Array.from(t.attributes||[]).map((e=>e.name)).filter((e=>e.startsWith("gfluo-")&&!e.startsWith("gfluo-scroll-"))).forEach((e=>{const a=t.getAttribute(e),o=e.replace("gfluo-","");let r;r="true"===a||"false"!==a&&(isNaN(a)?a:parseFloat(a)),s[o]=r})),s},a={getDuration:(e,t=.5)=>parseFloat(e.getAttribute("gfluo-duration"))||t,createScrollTrigger:(e,t,a={})=>{const s=e.getAttribute("gfluo-scroll-start")||"top 80%",o=e.getAttribute("gfluo-scroll-end"),r=e.getAttribute("gfluo-scroll-scrub"),i=e.getAttribute("gfluo-scroll-toggle")||"play none none reverse",n="true"===e.getAttribute("gfluo-scroll-markers"),l=parseFloat(e.getAttribute("gfluo-delay"))||0;if(!window.gsap||!window.ScrollTrigger)return void console.error("ScrollTrigger plugin is not registered");const p=gsap.timeline({paused:!0,onStart:()=>{gsap.set(e,{visibility:"visible"})}});l&&p.add(gsap.timeline().to({},{duration:l})),p.add(t),ScrollTrigger.create({trigger:e,animation:p,start:s,end:o,scrub:"true"===r,toggleActions:i,markers:n,onEnter:()=>{p.play()},once:!1}),ScrollTrigger.refresh()},splitText:(e,t={})=>{if(!window.SplitType)return console.error("SplitType library is not loaded"),null;try{return new SplitType(e,{types:t.types||"lines, words, chars",tagName:"span"})}catch(e){return console.error("Error splitting text:",e),null}},handleAnimation:(e,t)=>{const s=e.getAttribute("gfluo-trigger"),o=parseFloat(e.getAttribute("gfluo-delay"))||0;if(t)if(gsap.set(e,{visibility:"hidden"}),"load"===s){const a=gsap.timeline({onStart:()=>{gsap.set(e,{visibility:"visible"})}});o&&a.add(gsap.timeline().to({},{duration:o})),a.add(t),requestAnimationFrame((()=>{a.play()}))}else gsap.set(e,{visibility:"hidden"}),a.createScrollTrigger(e,t);else console.error("No animation provided for element:",e)},createBasicAnimation:(e,t={})=>{const a=getConfig(e),s={opacity:1===a.opacity?0:a.opacity,x:a.x,y:a.y,skewX:a.skewX,skewY:a.skewY,...t};return gsap.set(e,s),gsap.to(e,{opacity:1,x:0,y:0,skewX:0,skewY:0,duration:a.duration,ease:a.ease,stagger:a.stagger})}},s=(e,s,o)=>{const r=t(e,s),i=a.splitText(e,{types:r.splitType});if(!i)return null;gsap.set(e,{opacity:1});return(r.useChars?i.chars:i.words).forEach((e=>{e.style.display="inline-block",e.style.position="relative",r.transformOrigin&&(e.style.transformOrigin=r.transformOrigin)})),o(i,r)},o=e=>({x:e.x??0,y:e.y??0,scale:e.scale??1,rotation:e.rotation??0,opacity:e.opacity??1,duration:e.duration??.5,stagger:e.stagger??0,ease:e.ease??"power2.out"}),r=(e,t={})=>({...{x:e.x??0,y:e.y??0,scale:e.scale??1,rotation:e.rotation??0,rotationX:e.rotationX??0,rotationY:e.rotationY??0,skewX:e.skewX??0,skewY:e.skewY??0,opacity:e.opacity??1,duration:e.duration??.5,stagger:e.stagger??0,ease:e.ease??"power2.out",...e.transformOrigin&&{transformOrigin:e.transformOrigin},...e.force3D&&{force3D:!0},...e.filter&&{filter:e.filter}},...t}),i=e=>{gsap.set(e,{visibility:"visible",opacity:1,immediateRender:!0})},n={"txt.9":"interactive-mouse","txt.10":"typewriter","txt.11":"counter","txt.12":"word-rotation","txt.13":"glitch"},l={"txt.9":{setup:e=>{i(e);const s=((e,t={})=>a.splitText(e,t))(e);if(!s)return null;gsap.set(s.chars,{opacity:1});const o=a=>{const{clientX:o,clientY:r}=a;s.chars.forEach((a=>{const s=a.getBoundingClientRect(),i=s.left+s.width/2,n=s.top+s.height/2,l=o-i,p=r-n,c=t(e,{duration:.3});gsap.to(a,{x:gsap.utils.clamp(-20,20,l/10),y:gsap.utils.clamp(-20,20,p/10),duration:c.duration,ease:"power2.out"})}))};return e._mouseHandler=o,document.addEventListener("mousemove",o),()=>{document.removeEventListener("mousemove",e._mouseHandler),delete e._mouseHandler}},cleanup:e=>{e._mouseHandler&&(document.removeEventListener("mousemove",e._mouseHandler),delete e._mouseHandler)}},"txt.10":{setup:e=>{if(!window.ScrollTrigger)return console.warn("GSAP ScrollTrigger plugin is required for txt.10 animation. Please include the plugin from: https://gsap.com/docs/v3/Plugins/ScrollTrigger/"),null;i(e);const a=e.textContent;e.textContent="";const s=document.createElement("span");s.className="typed-text",e.appendChild(s);const o=document.createElement("span");let r;function n(a,o=0){if(o<a.length){s.innerHTML+=a.charAt(o),o++;const i=t(e,{typingSpeed:.07});r=gsap.delayedCall(i.typingSpeed,(()=>n(a,o)))}}o.className="cursor",o.textContent="|",e.appendChild(o);const l=gsap.to(o,{opacity:0,repeat:-1,yoyo:!0,duration:.6,ease:"expo.inOut"}),p=ScrollTrigger.create({trigger:e,start:"top 90%",end:"bottom 0%",onEnter:()=>{s.innerHTML="",n(a),l.play()},onEnterBack:()=>{s.innerHTML="",n(a),l.play()},onLeave:()=>{r&&r.kill(),s.innerHTML="",l.play()},onLeaveBack:()=>{r&&r.kill(),s.innerHTML="",l.play()}});return gsap.set(e,{opacity:1}),()=>{r&&r.kill(),l.kill(),p.kill(),e.textContent=a}},cleanup:e=>{e._cleanup&&(e._cleanup(),delete e._cleanup)}},"txt.11":{setup:e=>{if(!window.ScrollTrigger)return console.warn("GSAP ScrollTrigger plugin is required for txt.11 animation. Please include the plugin from: https://gsap.com/docs/v3/Plugins/ScrollTrigger/"),null;i(e);const a=e.innerText,s=parseInt(e.innerText);if(isNaN(s))return gsap.set(e,{opacity:1}),null;gsap.set(e,{opacity:1}),e.innerText="0";const o=t(e,{duration:2,delay:0,ease:"power2.out",start:"top 80%",toggleActions:"play none none none"}),r="load"===e.getAttribute("gfluo-trigger"),n=gsap.to(e,{innerText:s,duration:o.duration,delay:o.delay,snap:{innerText:1},ease:o.ease,scrollTrigger:r?null:{trigger:e,start:o.start,toggleActions:o.toggleActions},onComplete:()=>{e.innerText=a}});return()=>{n&&n.kill(),e.innerText=a}},cleanup:e=>{e._cleanup&&(e._cleanup(),delete e._cleanup)}},"txt.12":{setup:e=>{i(e);const a=e.textContent.trim(),s=a.split(",").map((e=>e.trim()));if(s.length<2)return console.log("Add more words divided by `,`. Element doesn't have enough words to animate:",e),null;let o=0;const r=document.createElement("span"),n=document.createElement("span");r.appendChild(n),e.textContent="",e.appendChild(r),n.textContent=s[0],gsap.set(e,{opacity:1});const l=t(e,{fadeDuration:.5,rotationInterval:2e3,yOffset:20});function p(){gsap.to(n,{duration:l.fadeDuration,opacity:0,y:l.yOffset,onComplete:()=>{o=(o+1)%s.length,n.textContent=s[o],gsap.to(n,{duration:l.fadeDuration,opacity:1,y:0})}})}if("load"===e.getAttribute("gfluo-trigger"))gsap.set(n,{opacity:0}),gsap.to(n,{opacity:1,duration:l.fadeDuration,onComplete:()=>{const t=setInterval(p,l.rotationInterval);e._intervalId=t}});else{const t=setInterval(p,l.rotationInterval);e._intervalId=t}return()=>{e._intervalId&&(clearInterval(e._intervalId),delete e._intervalId),e.textContent=a}},cleanup:e=>{e._cleanup&&(e._cleanup(),delete e._cleanup),e._intervalId&&(clearInterval(e._intervalId),delete e._intervalId)}},"txt.13":{setup:e=>{i(e);const a=t(e,{glitchChars:"!<>-_\\/[]{}—=+*^?#________",glitchProbability:.1,glitchInterval:100,minOpacity:.5,skewAngle:10,intermittent:!0,minGlitchDuration:.5,maxGlitchDuration:1.5,minNormalDuration:2,maxNormalDuration:5}),s=e.textContent;function o(){let t="";for(let e=0;e<s.length;e++)t+=Math.random()<a.glitchProbability?a.glitchChars[Math.floor(Math.random()*a.glitchChars.length)]:s[e];e.textContent=t}let r,n,l,p;function c(){r=setInterval(o,a.glitchInterval),n=gsap.to(e,{duration:.05,opacity:a.minOpacity,yoyo:!0,repeat:-1,ease:"power1.inOut"}),l=gsap.to(e,{duration:.05,skewX:a.skewAngle,yoyo:!0,repeat:-1,ease:"power1.inOut"})}function u(){clearInterval(r),n&&n.kill(),l&&l.kill(),gsap.to(e,{opacity:1,skewX:0,duration:.1}),e.textContent=s}if(gsap.set(e,{opacity:1,whiteSpace:"nowrap"}),a.intermittent){function g(){const e=gsap.utils.random(a.minGlitchDuration,a.maxGlitchDuration),t=gsap.utils.random(a.minNormalDuration,a.maxNormalDuration);c(),p=gsap.delayedCall(e,(()=>{u(),p=gsap.delayedCall(t,g)}))}g()}else c();return()=>{u(),p&&p.kill(),e.textContent=s}},cleanup:e=>{e._cleanup&&(e._cleanup(),delete e._cleanup)}},"txt.20":{setup:e=>c(e,{duration:.5,stagger:{amount:.3,from:"start"},ease:"power2.out",opacity:0,y:30,splitType:"words",useChars:!1},((e,t)=>gsap.from(e.words,r(t))))},"txt.21":{setup:e=>c(e,{duration:.6,stagger:{amount:.4,from:"center"},ease:"back.out(1.7)",opacity:0,scale:.5,splitType:"lines, words,chars",useChars:!0},((e,t)=>gsap.from(e.chars,r(t))))},"txt.22":{setup:e=>c(e,{duration:.4,stagger:{amount:.5,from:"random"},ease:"power3.out",opacity:0,rotation:45,splitType:"lines, words, chars",useChars:!0},((e,t)=>gsap.from(e.chars,r(t))))},"txt.23":{setup:e=>c(e,{duration:.7,stagger:{amount:.6,from:"end"},ease:"elastic.out(1, 0.3)",opacity:0,x:-50,splitType:"words",useChars:!1},((e,t)=>gsap.from(e.words,r(t))))}},p={...{"txt.20":{setup:e=>s(e,{duration:.5,stagger:{amount:.3,from:"start"},ease:"power2.out",opacity:0,y:30,splitType:"words",useChars:!1},((e,t)=>gsap.from(e.words,o(t))))},"txt.21":{setup:e=>s(e,{duration:.6,stagger:{amount:.4,from:"center"},ease:"back.out(1.7)",opacity:0,scale:.5,splitType:"lines, words, chars",useChars:!0},((e,t)=>gsap.from(e.chars,o(t))))},"txt.22":{setup:e=>s(e,{duration:.4,stagger:{amount:.5,from:"random"},ease:"power3.out",opacity:0,rotation:45,splitType:"lines, words, chars",useChars:!0},((e,t)=>gsap.from(e.chars,o(t))))},"txt.23":{setup:e=>s(e,{duration:.7,stagger:{amount:.6,from:"end"},ease:"elastic.out(1, 0.3)",opacity:0,x:-50,splitType:"words",useChars:!1},((e,t)=>gsap.from(e.words,o(t))))}},"par.1":{setup:e=>c(e,{duration:.8,stagger:{amount:.15},ease:"power2.out",y:20,opacity:0,rotation:0,scale:1,splitType:"lines"},((e,t)=>gsap.from(e.lines,r(t))))},"par.2":{setup:e=>{const s=t(e,{duration:1,stagger:{amount:.15},ease:"power2.out",x:-50,y:0,skewX:0,opacity:0,rotation:0,scale:1,splitType:"lines"}),o=a.splitText(e,{types:s.splitType});return o?(gsap.set(e,{opacity:1}),o.lines.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.from(o.lines,{x:s.x,y:s.y,skewX:s.skewX,opacity:s.opacity,rotation:s.rotation,scale:s.scale,duration:s.duration,stagger:s.stagger,ease:s.ease})):null}},"par.3":{setup:e=>{const s=t(e,{duration:1,stagger:{amount:.15},ease:"circ.out",x:-20,y:0,skewX:0,opacity:0,rotation:0,scale:1,splitType:"lines"}),o=a.splitText(e,{types:s.splitType});return o?(gsap.set(e,{opacity:1}),o.lines.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.from(o.lines,{x:s.x,y:s.y,skewX:s.skewX,opacity:s.opacity,rotation:s.rotation,scale:s.scale,duration:s.duration,stagger:s.stagger,ease:s.ease})):null}},"par.4":{setup:e=>{const s=t(e,{duration:1,stagger:{amount:.15},ease:"circ.out",x:0,y:20,scaleY:0,opacity:0,rotation:0,scale:1,splitType:"lines"}),o=a.splitText(e,{types:s.splitType});return o?(gsap.set(e,{opacity:1}),o.lines.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.from(o.lines,{x:s.x,y:s.y,scaleY:s.scaleY,opacity:s.opacity,rotation:s.rotation,scale:s.scale,duration:s.duration,stagger:s.stagger,ease:s.ease})):null}},"par.5":{setup:e=>{const s=t(e,{duration:1,stagger:{amount:.15},ease:"circ.out",x:50,y:0,skewX:0,opacity:0,rotation:0,scale:1,splitType:"lines"}),o=a.splitText(e,{types:s.splitType});return o?(gsap.set(e,{opacity:1}),o.lines.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.from(o.lines,{x:s.x,y:s.y,skewX:s.skewX,opacity:s.opacity,rotation:s.rotation,scale:s.scale,duration:s.duration,stagger:s.stagger,ease:s.ease})):null}},"par.6":{setup:e=>{const s=t(e,{duration:.8,stagger:{amount:.1},ease:"power2.out",x:0,y:0,scale:.95,opacity:0,rotation:0,splitType:"lines",transformOrigin:"left center"}),o=a.splitText(e,{types:s.splitType});return o?(gsap.set(e,{opacity:1}),o.lines.forEach((e=>{e.style.display="inline-block",e.style.position="relative",e.style.transformOrigin=s.transformOrigin})),gsap.from(o.lines,{x:s.x,y:s.y,scale:s.scale,opacity:s.opacity,rotation:s.rotation,duration:s.duration,stagger:s.stagger,ease:s.ease})):null}},"par.7":{setup:e=>{const s=t(e,{duration:.8,stagger:{amount:.1},ease:"power2.out",x:0,y:0,scale:.75,opacity:0,rotation:0,splitType:"lines",transformOrigin:"100% 100%"}),o=a.splitText(e,{types:s.splitType});return o?(gsap.set(e,{opacity:1}),o.lines.forEach((e=>{e.style.display="inline-block",e.style.position="relative",e.style.transformOrigin=s.transformOrigin})),gsap.from(o.lines,{x:s.x,y:s.y,scale:s.scale,opacity:s.opacity,rotation:s.rotation,duration:s.duration,stagger:s.stagger,ease:s.ease})):null}},"par.8":{setup:e=>{const s=t(e,{duration:.8,stagger:{amount:.03},ease:"power2.out",x:0,y:20,opacity:0,rotation:0,scale:1,splitType:"words"}),o=a.splitText(e,{types:s.splitType});return o?(gsap.set(e,{opacity:1}),o.words.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.from(o.words,{x:s.x,y:s.y,opacity:s.opacity,rotation:s.rotation,scale:s.scale,duration:s.duration,stagger:s.stagger,ease:s.ease})):null}},"par.9":{setup:e=>{const s=t(e,{duration:.8,stagger:{amount:.6},ease:"power1.out",x:0,y:100,skewX:20,opacity:0,rotation:0,scale:1,splitType:"lines"}),o=a.splitText(e,{types:s.splitType});return o?(gsap.set(e,{opacity:1}),o.lines.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.from(o.lines,{x:s.x,y:s.y,skewX:s.skewX,opacity:s.opacity,rotation:s.rotation,scale:s.scale,duration:s.duration,stagger:s.stagger,ease:s.ease})):null}},"par.10":{setup:e=>{const s=t(e,{duration:.75,stagger:.075,ease:"power1.inOut",x:0,y:0,opacity:0,splitType:"lines"}),o=a.splitText(e,{types:s.splitType});return o?gsap.from(o.lines,{x:s.x,y:s.y,opacity:s.opacity,duration:s.duration,stagger:s.stagger,ease:s.ease}):null}},"txt.1":{setup:e=>{const s=t(e,{duration:.4,stagger:.03,ease:"power2.out",x:0,y:-20,scale:1,rotation:0,skewX:0,skewY:0,opacity:0,transformOrigin:"center center",splitType:"lines, words, chars",useChars:!0}),o=a.splitText(e,{types:s.splitType});if(!o)return null;gsap.set(e,{opacity:1}),o.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const r=gsap.timeline();return r.from(o.chars,{x:s.x,y:s.y,scale:s.scale,rotation:s.rotation,skewX:s.skewX,skewY:s.skewY,opacity:s.opacity,transformOrigin:s.transformOrigin,duration:s.duration,stagger:s.stagger,ease:s.ease}),r}},"txt.24":{setup:e=>{const s=t(e,{duration:1,stagger:.1,ease:"steps(5)",x:0,y:-20,scale:.2,rotation:0,skewX:0,skewY:0,opacity:0,transformOrigin:"center center",splitType:"lines, words, chars",useChars:!0}),o=a.splitText(e,{types:s.splitType});if(!o)return null;gsap.set(e,{opacity:1}),o.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const r=gsap.timeline();return r.to(o.chars,{scale:1,duration:s.duration,stagger:s.stagger,ease:s.ease}),r.from(o.chars,{x:s.x,y:s.y,rotation:s.rotation,skewX:s.skewX,skewY:s.skewY,opacity:s.opacity,transformOrigin:s.transformOrigin,duration:s.duration,stagger:s.stagger,ease:"steps(5)"},0),r}},"txt.2":{setup:e=>{const s=t(e,{duration:1,stagger:.03,ease:"power4.inOut",x:-20,y:0,scale:1,rotation:0,skewX:0,skewY:0,opacity:0,transformOrigin:"center center",splitType:"lines, words, chars"}),o=a.splitText(e,{types:s.splitType});if(!o)return null;gsap.set(e,{opacity:1}),o.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const r=gsap.timeline();return r.from(o.chars,{x:s.x,y:s.y,scale:s.scale,rotation:s.rotation,skewX:s.skewX,skewY:s.skewY,opacity:s.opacity,transformOrigin:s.transformOrigin,duration:s.duration,stagger:s.stagger,ease:s.ease}),r}},"txt.3":{setup:e=>{const s=t(e,{duration:1,stagger:.03,ease:"power4.inOut",x:-20,y:0,scale:1,rotation:0,skewX:0,skewY:0,opacity:0,transformOrigin:"center center",splitType:"lines, words, chars",useChars:!0}),o=a.splitText(e,{types:s.splitType});if(!o)return null;gsap.set(e,{opacity:1}),o.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const r=gsap.timeline();return r.from(o.chars,{x:s.x,y:s.y,scale:s.scale,rotation:s.rotation,skewX:s.skewX,skewY:s.skewY,opacity:s.opacity,transformOrigin:s.transformOrigin,duration:s.duration,stagger:s.stagger,ease:s.ease}),r.to(e,{x:0,y:0,duration:s.duration,ease:s.ease},"<"),r}},"txt.4":{setup:e=>{const s=t(e,{duration:1,stagger:.03,ease:"back.out(2)",x:-30,y:0,scale:1,rotation:0,skewX:20,skewY:0,opacity:0,transformOrigin:"center center",splitType:"lines, words, chars"}),o=a.splitText(e,{types:s.splitType});if(!o)return null;gsap.set(e,{opacity:1}),o.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const r=gsap.timeline();return r.from(o.chars,{x:s.x,y:s.y,scale:s.scale,rotation:s.rotation,skewX:s.skewX,skewY:s.skewY,opacity:s.opacity,transformOrigin:s.transformOrigin,duration:s.duration,stagger:s.stagger,ease:s.ease}),r.to(e,{x:0,y:0,duration:s.duration,ease:s.ease},"<"),r}},"txt.5":{setup:e=>{const s=t(e,{duration:.75,stagger:.03,ease:"power2.out",x:0,y:-20,scale:1,rotation:0,opacity:0,splitType:"lines, words, chars",useChars:!0}),o=a.splitText(e,{types:s.splitType});return o?(gsap.set(e,{opacity:1}),o.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.from(o.chars,{x:s.x,y:s.y,scale:s.scale,rotation:s.rotation,opacity:s.opacity,duration:s.duration,stagger:s.stagger,ease:s.ease})):null}},"txt.6":{setup:e=>{const s=t(e,{duration:.75,stagger:.1,ease:"back.inOut(2)",x:0,y:0,rotation:0,scale:0,opacity:0,splitType:"lines, words, chars",useChars:!0}),o=a.splitText(e,{types:s.splitType});return o?(gsap.set(e,{opacity:1}),o.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.from(o.chars,{x:s.x,y:s.y,rotation:s.rotation,scale:s.scale,opacity:s.opacity,duration:s.duration,stagger:s.stagger,ease:s.ease})):null}},"txt.7":{setup:e=>{const a=t(e,{duration:1,ease:"power3.out",y:30,x:0,rotation:0,skewX:0,skewY:0,scale:.5,filter:"blur(100px)",opacity:0});return gsap.set(e,{opacity:a.opacity,y:a.y,x:a.x,rotation:a.rotation,skewX:a.skewX,skewY:a.skewY,scale:a.scale,filter:a.filter}),gsap.to(e,{opacity:1,y:0,x:0,rotation:0,skewX:0,skewY:0,scale:1,filter:"blur(0px)",duration:a.duration,ease:a.ease})}},"txt.8":{setup:e=>{const s=t(e,{duration:1.5,ease:"power2.out",scale:0,x:0,y:0,opacity:1,letterSpacing:"0.2em",filter:"blur(40px)",splitType:"lines, words, chars"}),o=a.splitText(e,{types:s.splitType});if(!o)return null;gsap.set(e,{opacity:1}),o.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const r=gsap.timeline();return r.from(o.chars,{rotation:s.rotation,opacity:s.opacity,scale:s.scale,x:s.x,y:s.y,duration:s.duration,ease:s.ease,stagger:{amount:.6,from:"center"}}),r.from(e,{letterSpacing:s.letterSpacing,filter:s.filter,duration:s.duration,ease:s.ease},"<"),r}},"txt.14":{setup:e=>c(e,{duration:.8,stagger:.1,ease:"power2.out",y:-30,opacity:0,splitType:"lines, words, chars",useChars:!0},((e,t)=>gsap.from(e.chars,r(t))))},"txt.15":{setup:e=>c(e,{duration:.5,stagger:.02,ease:"power2.out",scale:3,x:50,opacity:0,splitType:"lines, words, chars",useChars:!0},((e,t)=>gsap.from(e.chars,r(t))))},"txt.16":{setup:e=>c(e,{duration:.75,stagger:.02,ease:"power2.out",scale:2,opacity:0,splitType:"lines, words, chars",useChars:!0},((e,t)=>gsap.from(e.chars,r(t))))},"txt.17":{setup:e=>c(e,{duration:.4,stagger:.05,ease:"power2.out",rotationX:-180,scale:.8,opacity:0,transformOrigin:"50% 50% -50",splitType:"lines, words, chars",useChars:!0,force3D:!0},((e,t)=>gsap.timeline().from(e.chars,r(t))))},"txt.18":{setup:e=>c(e,{duration:1,stagger:.1,ease:"back.out",rotationY:-180,opacity:0,splitType:"lines, words, chars",useChars:!0,transformOrigin:"50% 50% -50",force3D:!0},((e,t)=>gsap.from(e.chars,r(t))))},"txt.19":{setup:e=>c(e,{duration:.5,stagger:.03,ease:"back.out(2)",opacity:0,x:()=>gsap.utils.random(-100,100),y:()=>gsap.utils.random(-100,100),rotation:()=>gsap.utils.random(-180,180),scale:()=>gsap.utils.random(.3,2),splitType:"lines,chars",useChars:!0,transformOrigin:"50% 50%"},((e,t)=>gsap.from(e.chars,r(t))))},"free.5":{setup:e=>((e,s={})=>{const o=t(e,{duration:.8,stagger:.03,ease:"power2.out",x:0,y:0,skewX:0,skewY:0,opacity:1,...s}),r=s.splitType||"lines, words, chars",i=a.splitText(e,{types:r});if(!i)return null;gsap.set(e,{opacity:1});const n=s.useLines?i.lines:s.useChars?i.chars:i.words;if(!n||0===n.length)return console.warn("No text targets found for animation"),null;n.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const l=e.getAttribute("gfluo-trigger");return"load"===l?(gsap.set(n,{opacity:1===o.opacity?0:o.opacity,x:o.x,y:o.y,skewX:o.skewX,skewY:o.skewY}),gsap.to(n,{opacity:1,x:0,y:0,skewX:0,skewY:0,duration:o.duration,stagger:o.stagger,ease:o.ease})):"scroll"===l||e.hasAttribute("gfluo-scroll-start")?gsap.from(n,{opacity:1===o.opacity?0:o.opacity,x:o.x,y:o.y,skewX:o.skewX,skewY:o.skewY,duration:o.duration,stagger:o.stagger,ease:o.ease}):gsap.to(n,{duration:0})})(e,{useChars:!1,y:100,opacity:0,stagger:{amount:.5},ease:"back.out(2)"})},"free.6":{setup:e=>{const t=a.splitText(e);return t?(gsap.set(e,{opacity:1}),t.words.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.set(t.words,{transformPerspective:1e3,transformOrigin:"0% 50%",rotationX:-90}),gsap.to(t.words,{rotationX:0,duration:.6,stagger:{amount:.6},ease:"power2.out"})):null}},"free.7":{setup:e=>c(e,{duration:.5,stagger:{amount:.6,from:"random"},ease:"power1.out",opacity:0,splitType:"words",useChars:!1},((e,t)=>gsap.from(e.words,r(t))))},"free.8":{setup:e=>c(e,{duration:.2,stagger:{amount:.8},ease:"power1.out",opacity:0,splitType:"lines, words, chars",useChars:!0},((e,t)=>gsap.from(e.chars,r(t))))},"free.9":{setup:e=>c(e,{duration:.2,stagger:{amount:.6},ease:"power1.out",yPercent:100,opacity:0,splitType:"lines, words, chars",useChars:!0},((e,t)=>gsap.from(e.chars,r(t))))}},c=(e,s,o)=>{const r=t(e,s),i=a.splitText(e,{types:r.splitType});if(!i)return null;gsap.set(e,{opacity:1});return(r.useChars?i.chars:i.words).forEach((e=>{e.style.display="inline-block",e.style.position="relative",r.transformOrigin&&(e.style.transformOrigin=r.transformOrigin)})),o(i,r)},u=(e,t)=>e/t*100,g=e=>{gsap.set(e,{visibility:"visible",opacity:1,immediateRender:!0})},d={"btn.1":{setup:e=>{const a=t(e,{duration:.3,ease:"power1.in",leaveEase:"power1.out",overlayColor:"#00000030"});g(e),gsap.set(e,{overflow:"hidden",position:"relative"});const s=document.createElement("div");s.setAttribute("aria-hidden","true"),Object.assign(s.style,{position:"absolute",backgroundColor:a.overlayColor,top:0,left:0,width:"100%",height:"100%",display:"none"}),e.appendChild(s);const o=t=>{const o=e.getBoundingClientRect(),r=u(t.clientY-o.top,o.height),i=u(t.clientX-o.left,o.width);gsap.set(s,{display:"flex"}),gsap.fromTo(s,{clipPath:`circle(0% at ${i}% ${r}%)`},{clipPath:`circle(141.4% at ${i}% ${r}%)`,duration:a.duration,ease:a.ease})},r=t=>{const o=e.getBoundingClientRect(),r=u(t.clientY-o.top,o.height),i=u(t.clientX-o.left,o.width);gsap.to(s,{clipPath:`circle(0% at ${i}% ${r}%)`,overwrite:!0,duration:a.duration,ease:a.leaveEase})};return e.addEventListener("mouseenter",o),e.addEventListener("mouseleave",r),()=>{e.removeEventListener("mouseenter",o),e.removeEventListener("mouseleave",r),s&&s.parentNode&&s.parentNode.removeChild(s)}}},"btn.2":{setup:e=>{const a=t(e,{duration:.2,scale:1.1,pressScale:.95,pressScaleDuration:.05,releaseScaleDuration:.2,shadowBlur:15,ease:"power2.out"});g(e),gsap.set(e,{opacity:1});const s=()=>{const t=window.getComputedStyle(e).backgroundColor;gsap.to(e,{scale:a.scale,boxShadow:`0 0 ${a.shadowBlur}px ${t.replace(")",", 1)")}`,duration:a.duration,ease:a.ease})},o=()=>{gsap.to(e,{scale:a.pressScale,duration:a.pressScaleDuration})},r=()=>{gsap.to(e,{scale:a.scale,duration:a.releaseScaleDuration})},i=()=>{gsap.to(e,{boxShadow:"none",duration:a.duration,scale:1,ease:a.ease})};return e.addEventListener("mouseenter",s),e.addEventListener("mousedown",o),e.addEventListener("mouseup",r),e.addEventListener("mouseleave",i),()=>{e.removeEventListener("mouseenter",s),e.removeEventListener("mousedown",o),e.removeEventListener("mouseup",r),e.removeEventListener("mouseleave",i)}}},"btn.3":{setup:e=>{const a=t(e,{duration:.3,scale:1.1,ease:"circ.out",maxTrailLength:10,trailSize:3});g(e);const s=document.createElement("canvas");s.style.position="absolute",s.style.top="0",s.style.left="0",s.style.pointerEvents="none",e.appendChild(s);const o=s.getContext("2d");let r,i,n=[],l=!1;function p(){s.width=e.offsetWidth,s.height=e.offsetHeight}function c(){o.clearRect(0,0,s.width,s.height);const t=window.getComputedStyle(e).borderRadius;let[r,i,p,u]=t.split(" ").map((e=>parseFloat(e)));isNaN(i)&&(i=r),isNaN(p)&&(p=r),isNaN(u)&&(u=i),o.beginPath(),o.moveTo(r,0),o.lineTo(s.width-i,0),o.quadraticCurveTo(s.width,0,s.width,i),o.lineTo(s.width,s.height-p),o.quadraticCurveTo(s.width,s.height,s.width-p,s.height),o.lineTo(u,s.height),o.quadraticCurveTo(0,s.height,0,s.height-u),o.lineTo(0,r),o.quadraticCurveTo(0,0,r,0),o.closePath(),o.clip(),n.forEach(((e,t)=>{const s=(a.maxTrailLength-t)*a.trailSize;if(isFinite(e.x)&&isFinite(e.y)&&isFinite(s)){const t=o.createRadialGradient(e.x,e.y,0,e.x,e.y,s);t.addColorStop(0,`rgba(255, 255, 255, ${e.alpha})`),t.addColorStop(1,"rgba(255, 255, 255, 0)"),o.beginPath(),o.arc(e.x,e.y,s,0,2*Math.PI),o.fillStyle=t,o.fill()}e.alpha*=.92})),n=n.filter((e=>e.alpha>=.01&&isFinite(e.x)&&isFinite(e.y))),n.length>0?requestAnimationFrame(c):l=!1}function u(e,t){isFinite(e)&&isFinite(t)&&(n.push({x:e,y:t,alpha:.5}),n.length>a.maxTrailLength&&n.shift(),l||(l=!0,c()))}p(),window.addEventListener("resize",p);const d=t=>{const a=e.getBoundingClientRect(),s=t.clientX-a.left,o=t.clientY-a.top;u(s,o),r=s,i=o},y=()=>{let t=0;const s=setInterval((()=>{t<5&&isFinite(r)&&isFinite(i)?(u(r,i),t++):clearInterval(s)}),32);gsap.to(e,{scale:1,duration:a.duration,ease:a.ease})},m=()=>{gsap.to(e,{scale:a.scale,duration:a.duration,ease:a.ease})};return e.addEventListener("mousemove",d),e.addEventListener("mouseleave",y),e.addEventListener("mouseenter",m),()=>{e.removeEventListener("mousemove",d),e.removeEventListener("mouseleave",y),e.removeEventListener("mouseenter",m),window.removeEventListener("resize",p),s&&s.parentNode&&s.parentNode.removeChild(s)}}},"btn.4":{setup:e=>{const a=t(e,{duration:.3,initialBorderRadius:"50px",hoverBorderRadius:"0px",scale:1.1,ease:"circ.out"});g(e),gsap.set(e,{borderRadius:a.initialBorderRadius});const s=()=>{gsap.killTweensOf(e),gsap.to(e,{duration:a.duration,borderRadius:a.hoverBorderRadius,scale:a.scale,ease:a.ease})},o=()=>{gsap.killTweensOf(e),gsap.to(e,{duration:a.duration,borderRadius:a.initialBorderRadius,scale:1,ease:a.ease})};return e.addEventListener("mouseenter",s),e.addEventListener("mouseleave",o),()=>{e.removeEventListener("mouseenter",s),e.removeEventListener("mouseleave",o),gsap.killTweensOf(e),gsap.set(e,{borderRadius:a.initialBorderRadius,scale:1})}}},"btn.5":{setup:e=>{const a=t(e,{duration:.3,scale:1.1,paddingMultiplier:1.5,ease:"back.out(4)"});g(e);const s=()=>{const t=window.getComputedStyle(e),s=parseFloat(t.paddingLeft),o=parseFloat(t.paddingRight);gsap.to(e,{paddingLeft:s*a.paddingMultiplier,paddingRight:o*a.paddingMultiplier,scale:a.scale,duration:a.duration,ease:a.ease}),e.dataset.initialPaddingLeft=s,e.dataset.initialPaddingRight=o},o=()=>{const t=parseFloat(e.dataset.initialPaddingLeft),s=parseFloat(e.dataset.initialPaddingRight);gsap.to(e,{paddingLeft:t,paddingRight:s,scale:1,duration:a.duration,ease:a.ease})};return e.addEventListener("mouseenter",s),e.addEventListener("mouseleave",o),()=>{e.removeEventListener("mouseenter",s),e.removeEventListener("mouseleave",o),delete e.dataset.initialPaddingLeft,delete e.dataset.initialPaddingRight}}},"btn.6":{setup:e=>{const a=t(e,{enterDuration:.3,leaveDuration:.3,moveDuration:.3,enterScale:1.1,moveDivisor:10,enterEase:"power2.out",leaveEase:"power2.out",moveEase:"power2.out"});gsap.set(e,{opacity:1});const s=t=>{gsap.to(e,{scale:a.enterScale,duration:a.enterDuration,ease:a.enterEase})},o=t=>{gsap.to(e,{scale:1,x:0,y:0,duration:a.leaveDuration,ease:a.leaveEase})},r=t=>{const s=e.getBoundingClientRect(),o=s.left+s.width/2,r=s.top+s.height/2,i=(t.clientX-o)/a.moveDivisor,n=(t.clientY-r)/a.moveDivisor;gsap.to(e,{x:i,y:n,duration:a.moveDuration,ease:a.moveEase})};return e.addEventListener("mouseenter",s),e.addEventListener("mouseleave",o),e.addEventListener("mousemove",r),()=>{e.removeEventListener("mouseenter",s),e.removeEventListener("mouseleave",o),e.removeEventListener("mousemove",r),gsap.killTweensOf(e),gsap.set(e,{scale:1,x:0,y:0})}}},"btn.7":{setup:e=>{const a=t(e,{duration:.3,magneticPullStrength:.2,ease:"power2.out"});let s;if(g(e),e.querySelector("span"))s=e.querySelector("span"),s.style.display="inline-block",s.style.pointerEvents="none";else{const t=e.textContent;e.textContent="",s=document.createElement("span"),s.textContent=t,s.style.display="inline-block",s.style.pointerEvents="none",e.appendChild(s)}gsap.set(s,{x:0,y:0});const o=t=>{const o=e.getBoundingClientRect(),r=o.left+o.width/2,i=o.top+o.height/2,n=(t.clientX-r)*a.magneticPullStrength,l=(t.clientY-i)*a.magneticPullStrength;gsap.to(s,{x:n,y:l,duration:a.duration,ease:a.ease})},r=()=>{gsap.to(s,{x:0,y:0,duration:a.duration,ease:a.ease})};return e.addEventListener("mousemove",o),e.addEventListener("mouseleave",r),()=>{e.removeEventListener("mousemove",o),e.removeEventListener("mouseleave",r),gsap.set(s,{clearProps:"all"})}}},"btn.8":{setup:e=>{const a=t(e,{enterDuration:.2,enterEase:"power2.out",leaveDuration:.2,leaveEase:"power2.in",underlineBottom:"0",underlineHeight:"1px",underlineColor:"currentColor"});g(e),e.style.position="relative",e.style.display="inline-block";const s=document.createElement("span");Object.assign(s.style,{position:"relative",display:"inline-block",color:"inherit",width:"100%",height:"100%",transform:"translateZ(0)",backfaceVisibility:"hidden"}),s.innerHTML=e.innerHTML,e.innerHTML="",e.appendChild(s);const o=document.createElement("div");Object.assign(o.style,{position:"absolute",bottom:a.underlineBottom,left:"0",width:"100%",height:a.underlineHeight,backgroundColor:a.underlineColor,transform:"scaleX(0)",transformOrigin:"left center",pointerEvents:"none",backfaceVisibility:"hidden",willChange:"transform"}),s.appendChild(o);const r=t=>{const s=e.getBoundingClientRect(),r=t.clientX<s.left+s.width/2;gsap.set(o,{transformOrigin:r?"left center":"right center"}),gsap.to(o,{scaleX:1,duration:a.enterDuration,ease:a.enterEase})},i=t=>{const s=e.getBoundingClientRect(),r=t.clientX>s.left+s.width/2;gsap.set(o,{transformOrigin:r?"right center":"left center"}),gsap.to(o,{scaleX:0,duration:a.leaveDuration,ease:a.leaveEase})};return e.addEventListener("mouseenter",r),e.addEventListener("mouseleave",i),()=>{e.removeEventListener("mouseenter",r),e.removeEventListener("mouseleave",i),e.innerHTML=s.innerHTML}}}},y=(e,a,s,o={})=>{const r=t(e,o),i={opacity:0,...a(r)},n=gsap.timeline();return gsap.set(e,{visibility:"hidden"}),n.from(e,{...i,duration:r.duration,ease:r.ease}),n},m={"img.1":{setup:e=>y(e,(()=>({clipPath:"inset(50% 50% 50% 50%)"})),0,{duration:1.5,ease:"power1.out"})},"img.2":{setup:e=>y(e,(()=>({borderRadius:"50%",scale:.5})),0,{duration:1,ease:"power2.out"})},"img.3":{setup:e=>y(e,(()=>({clipPath:"inset(100% 0% 0% 0%)"})),0,{duration:1.5,ease:"power2.inOut"})},"img.4":{setup:e=>y(e,(()=>({clipPath:"inset(0% 50% 0% 50%)"})),0,{duration:1,ease:"ease"})},"img.5":{setup:e=>y(e,(()=>({clipPath:"inset(0% 100% 0% 0%)"})),0,{duration:1,ease:"ease"})},"img.6":{setup:e=>y(e,(()=>({clipPath:"inset(0% 0% 0% 100%)"})),0,{duration:1,ease:"ease"})},"img.7":{setup:e=>y(e,(e=>({scale:e.scale||1.5,filter:"blur(12px)"})),0,{duration:1.5,ease:"power2.out"})},"img.8":{setup:e=>y(e,(e=>({y:e.y||100,rotation:e.rotation||5})),0,{duration:1.2,ease:"power2.out"})},"img.9":{setup:e=>y(e,(e=>({x:e.x||-200,rotation:e.rotation||-45,transformOrigin:e.transformOrigin||"center center"})),0,{duration:1.2,ease:"power2.out"})}},f=(e,a,s,o={})=>{const r=t(e,o),{animationConfig:i,...n}=r,l={opacity:0,x:r.x||0,y:r.y||0,scale:r.scale||1,rotation:r.rotation||0,skewX:r.skewX||0,skewY:r.skewY||0,...a(n)},p=gsap.timeline();return p.from(e,{...l,duration:r.duration,ease:r.ease,...i}),p},h={"el.4":{setup:e=>{const a=t(e,{duration:.75,x:-100,ease:"back.inOut(2)"});return f(e,(e=>({x:e.x})),0,a)}},"el.5":{setup:e=>{const a=t(e,{duration:.5,scale:0,ease:"power1.inOut"});return f(e,(e=>({scale:e.scale})),0,a)}},"el.2":{setup:e=>{const a=t(e,{scale:1.1,duration:.1,bounds:null,axis:null});if(gsap.set(e,{visibility:"visible",opacity:1}),!window.Draggable)return void console.warn("GSAP Draggable plugin is required for el.2 animation. Please include the plugin from: https://gsap.com/docs/v3/Plugins/Draggable/");gsap.registerPlugin(Draggable);const s=Draggable.create(e,{bounds:a.bounds,axis:a.axis,onPress:()=>{gsap.to(e,{scale:a.scale,duration:a.duration})},onRelease:()=>{gsap.to(e,{scale:1,duration:a.duration})}})[0];return()=>{s.kill()}}},"el.3":{setup:e=>{const a=window.matchMedia("(max-width: 990px)").matches,s=t(e,{enterDuration:.3,enterScale:1.1,enterEase:"power2.out",leaveDuration:.3,leaveEase:"power2.out",moveDuration:.3,moveEase:"power2.out",moveDivisor:10});if(gsap.set(e,{visibility:"visible",opacity:1}),!a){const t=t=>{gsap.to(e,{scale:s.enterScale,duration:s.enterDuration,ease:s.enterEase})},a=t=>{gsap.to(e,{scale:1,x:0,y:0,duration:s.leaveDuration,ease:s.leaveEase})},o=t=>{const a=e.getBoundingClientRect(),o=a.left+a.width/2,r=a.top+a.height/2,i=(t.clientX-o)/s.moveDivisor,n=(t.clientY-r)/s.moveDivisor;gsap.to(e,{x:i,y:n,duration:s.moveDuration,ease:s.moveEase})};return e.addEventListener("mouseenter",t),e.addEventListener("mouseleave",a),e.addEventListener("mousemove",o),()=>{e.removeEventListener("mouseenter",t),e.removeEventListener("mouseleave",a),e.removeEventListener("mousemove",o)}}return()=>{}}},"el.6":{setup:e=>{const a=t(e,{duration:.75,startY:88,endY:0,ease:"back.inOut(2)"});return f(e,(e=>({y:e.startY})),0,a)}},"el.1":{setup:e=>{const a=t(e,{duration:.75,x:88,ease:"back.inOut(2)"});return f(e,(e=>({x:e.x})),0,a)}}},w={"free.1":{setup:e=>{gsap.set(e,{opacity:1});const t=a.splitText(e,{types:"lines, words, chars"});if(!t)return null;t.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const s=gsap.timeline();return s.set(t.chars,{opacity:0,y:40}),s.to(t.chars,{opacity:1,y:0,duration:.3,stagger:{each:.02,ease:"sine.inOut"}})}},"free.2":{setup:e=>{gsap.set(e,{opacity:1});const t=a.splitText(e,{types:"lines, words, chars"});return t?(t.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.set(t.chars,{transformOrigin:"center center",rotationY:-180,opacity:0}),gsap.to(t.chars,{rotationY:0,opacity:1,duration:.8,stagger:{amount:1},ease:"power2.out"})):null}},"free.3":{setup:e=>{gsap.set(e,{opacity:1});const t=a.splitText(e,{types:"lines, words, chars"});return t?(t.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.set(t.chars,{scale:0,opacity:0}),gsap.to(t.chars,{scale:1,opacity:1,duration:.4,stagger:{amount:.8},ease:"back.out(2)"})):null}},"free.4":{setup:e=>{gsap.set(e,{opacity:1});const t=a.splitText(e,{types:"lines, words"});if(!t)return null;t.words.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.set(t.words,{opacity:0,x:"1em",position:"relative"});return gsap.to(t.words,{opacity:1,x:0,duration:.6,stagger:{amount:.2},ease:"power2.out"})}},"free.5":{setup:e=>{gsap.set(e,{opacity:1});const t=a.splitText(e,{types:"lines, words"});if(!t)return null;t.words.forEach((e=>{e.style.display="inline-block",e.style.position="relative"})),gsap.set(t.words,{yPercent:100,opacity:0,position:"relative"});return gsap.to(t.words,{yPercent:0,opacity:1,duration:.5,stagger:{amount:.5},ease:"back.out(2)"})}},"free.6":{setup:e=>{gsap.set(e,{opacity:1});const t=a.splitText(e,{types:"lines, words"});if(!t)return null;t.words.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const s=gsap.timeline();return gsap.set(e,{perspective:1e3}),gsap.set(t.words,{transformOrigin:"0% 50%",transformStyle:"preserve-3d"}),s.from(t.words,{rotationX:-90,duration:.6,stagger:{amount:.6},ease:"power2.out"})}},"free.7":{setup:e=>{gsap.set(e,{opacity:1});const t=a.splitText(e,{types:"lines, words, chars"});if(!t)return null;t.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const s=gsap.timeline();return s.set(t.chars,{opacity:0,y:e=>40*Math.sin(.5*e)}),s.to(t.chars,{opacity:1,y:0,duration:.8,stagger:{amount:.5},ease:"elastic.out(1, 0.3)"})}},"free.8":{setup:e=>{gsap.set(e,{opacity:1});const t=a.splitText(e,{types:"lines, words, chars"});if(!t)return null;return gsap.from(t.chars,{opacity:0,duration:.2,stagger:{amount:.8},ease:"power1.out"})}},"free.9":{setup:e=>{gsap.set(e,{opacity:1});const t=a.splitText(e,{types:"lines, words, chars"});if(!t)return null;t.chars.forEach((e=>{e.style.display="inline-block",e.style.position="relative"}));const s=gsap.timeline();return s.set(t.chars,{opacity:0,scale:3}),s.to(t.chars,{opacity:1,scale:1,duration:.6,stagger:{amount:.8},ease:"power4.out"})}},"free.10":{setup:e=>(gsap.set(e,{opacity:0,x:-100}),gsap.to(e,{opacity:1,x:0,duration:.8,ease:"power2.out"}))},"free.11":{setup:e=>(gsap.set(e,{opacity:0,y:100}),gsap.to(e,{opacity:1,y:0,duration:.8,ease:"power2.out"}))}};class v{constructor(){"undefined"!=typeof window&&window.gsap?window.ScrollTrigger?(gsap.registerPlugin(ScrollTrigger),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(()=>this.initialize())):this.initialize()):console.error("ScrollTrigger plugin is not loaded"):console.error("GSAP is not loaded")}initialize(){document.querySelectorAll("[data-gsap^='free.']").forEach((e=>{const t=e.getAttribute("data-gsap"),s=w[t];if(s){const t=s.setup(e);t&&a.handleAnimation(e,t)}}))}}"undefined"!=typeof window&&(window.GfluoBasic=v);class x{constructor(e={}){this.basicInstance=new v,"undefined"!=typeof window&&window.gsap?(window.ScrollTrigger&&(gsap.registerPlugin(ScrollTrigger),ScrollTrigger.defaults({markers:!1})),this.options=e,this.globalRules=e.globalRules||{},this.animations=new Set,"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(()=>this.initializePro())):this.initializePro()):console.error("GSAP is not loaded")}initializePro(){this.applyGlobalRules(),window.ScrollTrigger&&ScrollTrigger.refresh(),requestAnimationFrame((()=>{document.querySelectorAll('[data-gsap^="txt."], [data-gsap^="par."], [data-gsap^="free."][data-gsap$="5"], [data-gsap^="free."][data-gsap$="6"], [data-gsap^="free."][data-gsap$="7"], [data-gsap^="free."][data-gsap$="8"], [data-gsap^="free."][data-gsap$="9"]').forEach((e=>{const t=e.getAttribute("data-gsap");if(n[t]){const a=l[t];return void(a&&a.setup(e))}const s=p[t];if(s){const t=s.setup(e);t&&a.handleAnimation(e,t)}else console.warn("No handler found for animation type:",t)})),document.querySelectorAll('[data-gsap^="btn."]').forEach((e=>{const t=e.getAttribute("data-gsap"),a=d[t];a&&a.setup(e)})),document.querySelectorAll('[data-gsap^="img."]').forEach((e=>{const t=e.getAttribute("data-gsap"),s=m[t];if(s){const t=s.setup(e);t&&a.handleAnimation(e,t)}})),document.querySelectorAll('[data-gsap^="el."], [data-gsap^="btn."]').forEach((e=>{const t=e.getAttribute("data-gsap"),s=h[t];if(s){const t=s.setup(e);t&&"function"!=typeof t?a.handleAnimation(e,t):t&&"function"==typeof t&&(e._cleanup=t)}})),window.ScrollTrigger&&ScrollTrigger.refresh()}))}applyGlobalRules(){this.globalRules&&Object.entries(this.globalRules).forEach((([e,t])=>{document.querySelectorAll(`${e}:not([data-gsap])`).forEach((e=>{e.setAttribute("data-gsap",t),e.hasAttribute("gfluo-trigger")||e.hasAttribute("gfluo-scroll-start")||(e.setAttribute("gfluo-scroll-start","top 80%"),e.setAttribute("gfluo-scroll-toggle","play none none none"))}))}))}refresh(){this.initializePro()}trackAnimation(e){e&&this.animations.add(e)}getActiveAnimations(){return Array.from(this.animations)}}return window.addEventListener("load",(function(){if("undefined"!=typeof window&&window.gsap&&window.ScrollTrigger){window.Gfluo=x;const e=window.gfluoOptions||{};window.gfluo=new x(e)}else console.error("Gfluo is not supported in this environment")})),console.log("Gfluo loaded"),x}();
//# sourceMappingURL=gfluo-pro.min.js.map
