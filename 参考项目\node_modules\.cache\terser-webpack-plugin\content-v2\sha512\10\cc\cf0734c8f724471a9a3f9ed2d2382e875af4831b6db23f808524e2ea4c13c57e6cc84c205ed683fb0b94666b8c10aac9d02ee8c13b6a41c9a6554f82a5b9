{"code": "(window.webpackJsonp=window.webpackJsonp||[]).push([[1],{779:function(t,i,n){\"use strict\";var e={allowDownsampling:!0};var s,r,o,h=function(){function t(t,i){var n=this;this._resolutionMediaQueryList=null,this._resolutionListener=function(t){return n._onResolutionChanged()},this._canvasConfiguredListeners=[],this.canvas=t,this._canvasSize={width:this.canvas.clientWidth,height:this.canvas.clientHeight},this._options=i,this._configureCanvas(),this._installResolutionListener()}return t.prototype.destroy=function(){this._canvasConfiguredListeners.length=0,this._uninstallResolutionListener(),this.canvas=null},Object.defineProperty(t.prototype,\"canvasSize\",{get:function(){return{width:this._canvasSize.width,height:this._canvasSize.height}},enumerable:!0,configurable:!0}),t.prototype.resizeCanvas=function(t){this._canvasSize={width:t.width,height:t.height},this._configureCanvas()},Object.defineProperty(t.prototype,\"pixelRatio\",{get:function(){var t=this.canvas.ownerDocument.defaultView;if(null==t)throw new Error(\"No window is associated with the canvas\");return t.devicePixelRatio>1||this._options.allowDownsampling?t.devicePixelRatio:1},enumerable:!0,configurable:!0}),t.prototype.subscribeCanvasConfigured=function(t){this._canvasConfiguredListeners.push(t)},t.prototype.unsubscribeCanvasConfigured=function(t){this._canvasConfiguredListeners=this._canvasConfiguredListeners.filter(function(i){return i!=t})},t.prototype._configureCanvas=function(){var t=this.pixelRatio;this.canvas.style.width=this._canvasSize.width+\"px\",this.canvas.style.height=this._canvasSize.height+\"px\",this.canvas.width=this._canvasSize.width*t,this.canvas.height=this._canvasSize.height*t,this._emitCanvasConfigured()},t.prototype._emitCanvasConfigured=function(){var t=this;this._canvasConfiguredListeners.forEach(function(i){return i.call(t)})},t.prototype._installResolutionListener=function(){if(null!==this._resolutionMediaQueryList)throw new Error(\"Resolution listener is already installed\");var t=this.canvas.ownerDocument.defaultView;if(null==t)throw new Error(\"No window is associated with the canvas\");var i=t.devicePixelRatio;this._resolutionMediaQueryList=t.matchMedia(\"all and (resolution: \"+i+\"dppx)\"),this._resolutionMediaQueryList.addListener(this._resolutionListener)},t.prototype._uninstallResolutionListener=function(){null!==this._resolutionMediaQueryList&&(this._resolutionMediaQueryList.removeListener(this._resolutionListener),this._resolutionMediaQueryList=null)},t.prototype._reinstallResolutionListener=function(){this._uninstallResolutionListener(),this._installResolutionListener()},t.prototype._onResolutionChanged=function(){this._configureCanvas(),this._reinstallResolutionListener()},t}();function u(t,i){var n,e=((n={})[0]=[],n[1]=[t.lineWidth,t.lineWidth],n[2]=[2*t.lineWidth,2*t.lineWidth],n[3]=[6*t.lineWidth,6*t.lineWidth],n[4]=[t.lineWidth,4*t.lineWidth],n)[i];t.setLineDash(e)}function a(t,i,n,e){t.beginPath();var s=t.lineWidth%2?.5:0;t.moveTo(n,i+s),t.lineTo(e,i+s),t.stroke()}function l(t,i){if(!t)throw new Error(\"Assertion failed\"+(i?\": \"+i:\"\"))}function c(t){if(void 0===t)throw new Error(\"Value is undefined\");return t}function f(t){if(null===t)throw new Error(\"Value is null\");return t}function p(t){return f(c(t))}n.d(i,\"a\",function(){return Pe}),(o=s||(s={}))[o.Simple=0]=\"Simple\",o[o.WithSteps=1]=\"WithSteps\",function(t){t[t.Solid=0]=\"Solid\",t[t.Dotted=1]=\"Dotted\",t[t.Dashed=2]=\"Dashed\",t[t.LargeDashed=3]=\"LargeDashed\",t[t.SparseDotted=4]=\"SparseDotted\"}(r||(r={}));var v={khaki:\"#f0e68c\",azure:\"#f0ffff\",aliceblue:\"#f0f8ff\",ghostwhite:\"#f8f8ff\",gold:\"#ffd700\",goldenrod:\"#daa520\",gainsboro:\"#dcdcdc\",gray:\"#808080\",green:\"#008000\",honeydew:\"#f0fff0\",floralwhite:\"#fffaf0\",lightblue:\"#add8e6\",lightcoral:\"#f08080\",lemonchiffon:\"#fffacd\",hotpink:\"#ff69b4\",lightyellow:\"#ffffe0\",greenyellow:\"#adff2f\",lightgoldenrodyellow:\"#fafad2\",limegreen:\"#32cd32\",linen:\"#faf0e6\",lightcyan:\"#e0ffff\",magenta:\"#f0f\",maroon:\"#800000\",olive:\"#808000\",orange:\"#ffa500\",oldlace:\"#fdf5e6\",mediumblue:\"#0000cd\",transparent:\"#0000\",lime:\"#0f0\",lightpink:\"#ffb6c1\",mistyrose:\"#ffe4e1\",moccasin:\"#ffe4b5\",midnightblue:\"#191970\",orchid:\"#da70d6\",mediumorchid:\"#ba55d3\",mediumturquoise:\"#48d1cc\",orangered:\"#ff4500\",royalblue:\"#4169e1\",powderblue:\"#b0e0e6\",red:\"#f00\",coral:\"#ff7f50\",turquoise:\"#40e0d0\",white:\"#fff\",whitesmoke:\"#f5f5f5\",wheat:\"#f5deb3\",teal:\"#008080\",steelblue:\"#4682b4\",bisque:\"#ffe4c4\",aquamarine:\"#7fffd4\",aqua:\"#0ff\",sienna:\"#a0522d\",silver:\"#c0c0c0\",springgreen:\"#00ff7f\",antiquewhite:\"#faebd7\",burlywood:\"#deb887\",brown:\"#a52a2a\",beige:\"#f5f5dc\",chocolate:\"#d2691e\",chartreuse:\"#7fff00\",cornflowerblue:\"#6495ed\",cornsilk:\"#fff8dc\",crimson:\"#dc143c\",cadetblue:\"#5f9ea0\",tomato:\"#ff6347\",fuchsia:\"#f0f\",blue:\"#00f\",salmon:\"#fa8072\",blanchedalmond:\"#ffebcd\",slateblue:\"#6a5acd\",slategray:\"#708090\",thistle:\"#d8bfd8\",tan:\"#d2b48c\",cyan:\"#0ff\",darkblue:\"#00008b\",darkcyan:\"#008b8b\",darkgoldenrod:\"#b8860b\",darkgray:\"#a9a9a9\",blueviolet:\"#8a2be2\",black:\"#000\",darkmagenta:\"#8b008b\",darkslateblue:\"#483d8b\",darkkhaki:\"#bdb76b\",darkorchid:\"#9932cc\",darkorange:\"#ff8c00\",darkgreen:\"#006400\",darkred:\"#8b0000\",dodgerblue:\"#1e90ff\",darkslategray:\"#2f4f4f\",dimgray:\"#696969\",deepskyblue:\"#00bfff\",firebrick:\"#b22222\",forestgreen:\"#228b22\",indigo:\"#4b0082\",ivory:\"#fffff0\",lavenderblush:\"#fff0f5\",feldspar:\"#d19275\",indianred:\"#cd5c5c\",lightgreen:\"#90ee90\",lightgrey:\"#d3d3d3\",lightskyblue:\"#87cefa\",lightslategray:\"#789\",lightslateblue:\"#8470ff\",snow:\"#fffafa\",lightseagreen:\"#20b2aa\",lightsalmon:\"#ffa07a\",darksalmon:\"#e9967a\",darkviolet:\"#9400d3\",mediumpurple:\"#9370d8\",mediumaquamarine:\"#66cdaa\",skyblue:\"#87ceeb\",lavender:\"#e6e6fa\",lightsteelblue:\"#b0c4de\",mediumvioletred:\"#c71585\",mintcream:\"#f5fffa\",navajowhite:\"#ffdead\",navy:\"#000080\",olivedrab:\"#6b8e23\",palevioletred:\"#d87093\",violetred:\"#d02090\",yellow:\"#ff0\",yellowgreen:\"#9acd32\",lawngreen:\"#7cfc00\",pink:\"#ffc0cb\",paleturquoise:\"#afeeee\",palegoldenrod:\"#eee8aa\",darkolivegreen:\"#556b2f\",darkseagreen:\"#8fbc8f\",darkturquoise:\"#00ced1\",peachpuff:\"#ffdab9\",deeppink:\"#ff1493\",violet:\"#ee82ee\",palegreen:\"#98fb98\",mediumseagreen:\"#3cb371\",peru:\"#cd853f\",saddlebrown:\"#8b4513\",sandybrown:\"#f4a460\",rosybrown:\"#bc8f8f\",purple:\"#800080\",seagreen:\"#2e8b57\",seashell:\"#fff5ee\",papayawhip:\"#ffefd5\",mediumslateblue:\"#7b68ee\",plum:\"#dda0dd\",mediumspringgreen:\"#00fa9a\"};function d(t){return t<0?0:t>255?255:Math.round(t)||0}function y(t){return t<=0||t>0?t<0?0:t>1?1:Math.round(1e4*t)/1e4:0}var w=/^#([0-9a-f])([0-9a-f])([0-9a-f])([0-9a-f])?$/i,g=/^#([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})?$/i,m=/^rgb\\(\\s*(-?\\d{1,10})\\s*,\\s*(-?\\d{1,10})\\s*,\\s*(-?\\d{1,10})\\s*\\)$/,M=/^rgba\\(\\s*(-?\\d{1,10})\\s*,\\s*(-?\\d{1,10})\\s*,\\s*(-?\\d{1,10})\\s*,\\s*(-?[\\d]{0,10}(?:\\.\\d+)?)\\s*\\)$/;function _(t){var i;if((t=t.toLowerCase())in v&&(t=v[t]),i=M.exec(t)||m.exec(t))return[d(parseInt(i[1],10)),d(parseInt(i[2],10)),d(parseInt(i[3],10)),y(i.length<5?1:parseFloat(i[4]))];if(i=g.exec(t))return[d(parseInt(i[1],16)),d(parseInt(i[2],16)),d(parseInt(i[3],16)),1];if(i=w.exec(t))return[d(17*parseInt(i[1],16)),d(17*parseInt(i[2],16)),d(17*parseInt(i[3],16)),1];throw new Error(\"Cannot parse color: \".concat(t))}function b(t){var i,n=_(t);return{t:\"rgb(\".concat(n[0],\", \").concat(n[1],\", \").concat(n[2],\")\"),i:(i=n,.199*i[0]+.687*i[1]+.114*i[2]>160?\"black\":\"white\")}}var S=function(t,i){return(S=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])})(t,i)};function C(t,i){if(\"function\"!=typeof i&&null!==i)throw new TypeError(\"Class extends value \"+String(i)+\" is not a constructor or null\");function n(){this.constructor=t}S(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}var x=function(){return(x=Object.assign||function(t){for(var i,n=1,e=arguments.length;n<e;n++)for(var s in i=arguments[n])Object.prototype.hasOwnProperty.call(i,s)&&(t[s]=i[s]);return t}).apply(this,arguments)};function B(t,i,n){if(n||2===arguments.length)for(var e,s=0,r=i.length;s<r;s++)!e&&s in i||(e||(e=Array.prototype.slice.call(i,0,s)),e[s]=i[s]);return t.concat(e||Array.prototype.slice.call(i))}var z=function(){function t(){this.h=[]}return t.prototype.u=function(t,i,n){var e={o:t,l:i,v:!0===n};this.h.push(e)},t.prototype._=function(t){var i=this.h.findIndex(function(i){return t===i.o});i>-1&&this.h.splice(i,1)},t.prototype.M=function(t){this.h=this.h.filter(function(i){return i.l!==t})},t.prototype.m=function(t,i){var n=B([],this.h,!0);this.h=this.h.filter(function(t){return!t.v}),n.forEach(function(n){return n.o(t,i)})},t.prototype.p=function(){return this.h.length>0},t.prototype.g=function(){this.h=[]},t}();function T(t){for(var i=[],n=1;n<arguments.length;n++)i[n-1]=arguments[n];for(var e=0,s=i;e<s.length;e++){var r=s[e];for(var o in r)void 0!==r[o]&&(\"object\"!=typeof r[o]||void 0===t[o]?t[o]=r[o]:T(t[o],r[o]))}return t}function k(t){return\"number\"==typeof t&&isFinite(t)}function D(t){return\"number\"==typeof t&&t%1==0}function E(t){return\"string\"==typeof t}function P(t){return\"boolean\"==typeof t}function L(t){var i,n,e,s=t;if(!s||\"object\"!=typeof s)return s;for(n in i=Array.isArray(s)?[]:{},s)s.hasOwnProperty(n)&&(e=s[n],i[n]=e&&\"object\"==typeof e?L(e):e);return i}function W(t){return null!==t}function N(t){return null===t?void 0:t}var R=\"'Trebuchet MS', Roboto, Ubuntu, sans-serif\";function O(t,i,n){return n=void 0!==n?\"\".concat(n,\" \"):\"\",void 0===i&&(i=R),\"\".concat(n).concat(t,\"px \").concat(i)}var A=function(){function t(t){this.k={N:1,C:4,S:NaN,T:\"\",D:\"\",A:\"\",B:0,L:0,F:0,O:0,V:0},this.P=t}return t.prototype.W=function(){var t=this.k,i=this.R(),n=this.I();return t.S===i&&t.D===n||(t.S=i,t.D=n,t.T=O(i,n),t.O=Math.floor(i/3.5),t.B=t.O,t.L=Math.max(Math.ceil(i/2-t.C/2),0),t.F=Math.ceil(i/2+t.C/2),t.V=Math.round(i/10)),t.A=this.j(),this.k},t.prototype.j=function(){return this.P.W().layout.textColor},t.prototype.R=function(){return this.P.W().layout.fontSize},t.prototype.I=function(){return this.P.W().layout.fontFamily},t}(),G=function(){function t(){this.q=[]}return t.prototype.U=function(t){this.q=t},t.prototype.H=function(t,i,n,e){this.q.forEach(function(s){t.save(),s.H(t,i,n,e),t.restore()})},t}(),V=function(){function t(){}return t.prototype.H=function(t,i,n,e){t.save(),t.scale(i,i),this.Y(t,n,e),t.restore()},t.prototype.$=function(t,i,n,e){t.save(),t.scale(i,i),this.K(t,n,e),t.restore()},t.prototype.K=function(t,i,n){},t}(),F=function(t){function i(){var i=null!==t&&t.apply(this,arguments)||this;return i.X=null,i}return C(i,t),i.prototype.Z=function(t){this.X=t},i.prototype.Y=function(t){if(null!==this.X&&null!==this.X.J){var i=this.X.J,n=this.X,e=function(e){t.beginPath();for(var s=i.to-1;s>=i.from;--s){var r=n.G[s];t.moveTo(r.tt,r.it),t.arc(r.tt,r.it,e,0,2*Math.PI)}t.fill()};t.fillStyle=n.nt,e(n.st+2),t.fillStyle=n.ht,e(n.st)}},i}(V);function j(){return{G:[{tt:0,it:0,rt:0,et:0}],ht:\"\",nt:\"\",st:0,J:null}}var X={from:0,to:1},Y=function(){function t(t,i){this.ut=new G,this.ot=[],this.lt=[],this.ft=!0,this.P=t,this.ct=i,this.ut.U(this.ot)}return t.prototype.vt=function(t){var i=this.P._t();i.length!==this.ot.length&&(this.lt=i.map(j),this.ot=this.lt.map(function(t){var i=new F;return i.Z(t),i}),this.ut.U(this.ot)),this.ft=!0},t.prototype.dt=function(t,i,n){return this.ft&&(this.wt(t),this.ft=!1),this.ut},t.prototype.wt=function(t){var i=this,n=this.P._t(),e=this.ct.Mt(),s=this.P.bt();n.forEach(function(n,r){var o,h=i.lt[r],u=n.gt(e);if(null!==u&&n.yt()){var a=f(n.kt());h.ht=u.Nt,h.st=u.st,h.G[0].et=u.et,h.G[0].it=n.Ct().xt(u.et,a.St),h.nt=null!==(o=u.Tt)&&void 0!==o?o:i.P.Dt(h.G[0].it/t),h.G[0].rt=e,h.G[0].tt=s.At(e),h.J=X}else h.J=null})},t}(),J=function(){function t(t){this.Bt=t}return t.prototype.H=function(t,i,n,e){if(null!==this.Bt){var s=this.Bt.Lt.yt,r=this.Bt.Et.yt;if(s||r){t.save();var o=Math.round(this.Bt.tt*i),h=Math.round(this.Bt.it*i),l=Math.ceil(this.Bt.Ft*i),c=Math.ceil(this.Bt.Ot*i);t.lineCap=\"butt\",s&&o>=0&&(t.lineWidth=Math.floor(this.Bt.Lt.Vt*i),t.strokeStyle=this.Bt.Lt.A,t.fillStyle=this.Bt.Lt.A,u(t,this.Bt.Lt.Pt),function(t,i,n,e){t.beginPath();var s=t.lineWidth%2?.5:0;t.moveTo(i+s,0),t.lineTo(i+s,e),t.stroke()}(t,o,0,c)),r&&h>=0&&(t.lineWidth=Math.floor(this.Bt.Et.Vt*i),t.strokeStyle=this.Bt.Et.A,t.fillStyle=this.Bt.Et.A,u(t,this.Bt.Et.Pt),a(t,h,0,l)),t.restore()}}},t}(),I=function(){function t(t){this.ft=!0,this.Wt={Lt:{Vt:1,Pt:0,A:\"\",yt:!1},Et:{Vt:1,Pt:0,A:\"\",yt:!1},Ft:0,Ot:0,tt:0,it:0},this.zt=new J(this.Wt),this.Rt=t}return t.prototype.vt=function(){this.ft=!0},t.prototype.dt=function(t,i){return this.ft&&(this.wt(),this.ft=!1),this.zt},t.prototype.wt=function(){var t=this.Rt.yt(),i=f(this.Rt.It()),n=i.jt().W().crosshair,e=this.Wt;e.Et.yt=t&&this.Rt.qt(i),e.Lt.yt=t&&this.Rt.Ut(),e.Et.Vt=n.horzLine.width,e.Et.Pt=n.horzLine.style,e.Et.A=n.horzLine.color,e.Lt.Vt=n.vertLine.width,e.Lt.Pt=n.vertLine.style,e.Lt.A=n.vertLine.color,e.Ft=i.Ht(),e.Ot=i.Yt(),e.tt=this.Rt.$t(),e.it=this.Rt.Kt()},t}();function Z(t,i,n,e,s,r){t.fillRect(i+r,n,e-2*r,r),t.fillRect(i+r,n+s-r,e-2*r,r),t.fillRect(i,n,r,s),t.fillRect(i+e-r,n,r,s)}function K(t,i,n){t.save(),t.scale(i,i),n(),t.restore()}function H(t,i,n,e,s,r){t.save(),t.globalCompositeOperation=\"copy\",t.fillStyle=r,t.fillRect(i,n,e,s),t.restore()}function U(t,i,n,e,s,r,o){t.save(),t.globalCompositeOperation=\"copy\";var h=t.createLinearGradient(0,0,0,s);h.addColorStop(0,r),h.addColorStop(1,o),t.fillStyle=h,t.fillRect(i,n,e,s),t.restore()}var Q,$=function(){function t(t,i){this.Z(t,i)}return t.prototype.Z=function(t,i){this.Bt=t,this.Xt=i},t.prototype.H=function(t,i,n,e,s,r){if(this.Bt.yt){t.font=i.T;var o=this.Bt.Zt||!this.Bt.Jt?i.C:0,h=i.N,u=i.O,a=i.B,l=i.L,c=i.F,f=this.Bt.Gt,p=Math.ceil(n.Qt(t,f)),v=i.V,d=i.S+u+a,y=Math.ceil(.5*d),w=h+p+l+c+o,g=this.Xt.ti;this.Xt.ii&&(g=this.Xt.ii);var m,M,_=(g=Math.round(g))-y,b=_+d,S=\"right\"===s,C=S?e:0,x=Math.ceil(e*r),B=C;if(t.fillStyle=this.Xt.t,t.lineWidth=1,t.lineCap=\"butt\",f){S?(m=C-o,M=(B=C-w)+c):(B=C+w,m=C+o,M=C+h+o+l);var z=Math.max(1,Math.floor(r)),T=Math.max(1,Math.floor(h*r)),k=S?x:0,D=Math.round(_*r),E=Math.round(B*r),P=Math.round(g*r)-Math.floor(.5*r),L=P+z+(P-D),W=Math.round(m*r);t.save(),t.beginPath(),t.moveTo(k,D),t.lineTo(E,D),t.lineTo(E,L),t.lineTo(k,L),t.fill(),t.fillStyle=this.Bt.Tt,t.fillRect(S?x-T:0,D,T,L-D),this.Bt.Zt&&(t.fillStyle=this.Xt.A,t.fillRect(k,P,W-k,z)),t.textAlign=\"left\",t.fillStyle=this.Xt.A,K(t,r,function(){t.fillText(f,M,b-a-v)}),t.restore()}}},t.prototype.Yt=function(t,i){return this.Bt.yt?t.S+t.O+t.B:0},t}(),q=function(){function t(t){this.ni={ti:0,A:\"#FFF\",t:\"#000\"},this.si={Gt:\"\",yt:!1,Zt:!0,Jt:!1,Tt:\"\"},this.hi={Gt:\"\",yt:!1,Zt:!1,Jt:!0,Tt:\"\"},this.ft=!0,this.ri=new(t||$)(this.si,this.ni),this.ei=new(t||$)(this.hi,this.ni)}return t.prototype.Gt=function(){return this.ui(),this.si.Gt},t.prototype.ti=function(){return this.ui(),this.ni.ti},t.prototype.vt=function(){this.ft=!0},t.prototype.Yt=function(t,i){return void 0===i&&(i=!1),Math.max(this.ri.Yt(t,i),this.ei.Yt(t,i))},t.prototype.ai=function(){return this.ni.ii||0},t.prototype.oi=function(t){this.ni.ii=t},t.prototype.li=function(){return this.ui(),this.si.yt||this.hi.yt},t.prototype.fi=function(){return this.ui(),this.si.yt},t.prototype.dt=function(t){return this.ui(),this.si.Zt=this.si.Zt&&t.W().drawTicks,this.hi.Zt=this.hi.Zt&&t.W().drawTicks,this.ri.Z(this.si,this.ni),this.ei.Z(this.hi,this.ni),this.ri},t.prototype.ci=function(){return this.ui(),this.ri.Z(this.si,this.ni),this.ei.Z(this.hi,this.ni),this.ei},t.prototype.ui=function(){this.ft&&(this.si.Zt=!0,this.hi.Zt=!1,this.vi(this.si,this.hi,this.ni))},t}(),tt=function(t){function i(i,n,e){var s=t.call(this)||this;return s.Rt=i,s._i=n,s.di=e,s}return C(i,t),i.prototype.vi=function(t,i,n){t.yt=!1;var e=this.Rt.W().horzLine;if(e.labelVisible){var s=this._i.kt();if(this.Rt.yt()&&!this._i.wi()&&null!==s){var r=b(e.labelBackgroundColor);n.t=r.t,n.A=r.i;var o=this.di(this._i);n.ti=o.ti,t.Gt=this._i.Mi(o.et,s),t.yt=!0}}},i}(q),it=/[1-9]/g,nt=function(){function t(){this.Bt=null}return t.prototype.Z=function(t){this.Bt=t},t.prototype.H=function(t,i,n){var e=this;if(null!==this.Bt&&!1!==this.Bt.yt&&0!==this.Bt.Gt.length){t.font=i.T;var s=Math.round(i.bi.Qt(t,this.Bt.Gt,it));if(!(s<=0)){t.save();var r=i.mi,o=s+2*r,h=o/2,u=this.Bt.Ht,a=this.Bt.ti,l=Math.floor(a-h)+.5;l<0?(a+=Math.abs(0-l),l=Math.floor(a-h)+.5):l+o>u&&(a-=Math.abs(u-(l+o)),l=Math.floor(a-h)+.5);var c=l+o,p=0+i.N+i.O+i.S+i.B;t.fillStyle=this.Bt.t;var v=Math.round(l*n),d=Math.round(0*n),y=Math.round(c*n),w=Math.round(p*n);t.fillRect(v,d,y-v,w-d);var g=Math.round(this.Bt.ti*n),m=d,M=Math.round((m+i.N+i.C)*n);t.fillStyle=this.Bt.A;var _=Math.max(1,Math.floor(n)),b=Math.floor(.5*n);t.fillRect(g-b,m,_,M-m);var S=p-i.V-i.B;t.textAlign=\"left\",t.fillStyle=this.Bt.A,K(t,n,function(){t.fillText(f(e.Bt).Gt,l+r,S)}),t.restore()}}},t}(),et=function(){function t(t,i,n){this.ft=!0,this.zt=new nt,this.Wt={yt:!1,t:\"#4c525e\",A:\"white\",Gt:\"\",Ht:0,ti:NaN},this.ct=t,this.pi=i,this.di=n}return t.prototype.vt=function(){this.ft=!0},t.prototype.dt=function(){return this.ft&&(this.wt(),this.ft=!1),this.zt.Z(this.Wt),this.zt},t.prototype.wt=function(){var t=this.Wt;t.yt=!1;var i=this.ct.W().vertLine;if(i.labelVisible){var n=this.pi.bt();if(!n.wi()){var e=n.gi(this.ct.Mt());t.Ht=n.Ht();var s=this.di();if(s.rt){t.ti=s.ti,t.Gt=n.yi(f(e)),t.yt=!0;var r=b(i.labelBackgroundColor);t.t=r.t,t.A=r.i}}}},t}(),st=function(){function t(){this.ki=null,this.Ni=0}return t.prototype.xi=function(){return this.Ni},t.prototype.Ci=function(t){this.Ni=t},t.prototype.Ct=function(){return this.ki},t.prototype.Si=function(t){this.ki=t},t.prototype.Ti=function(){return[]},t.prototype.yt=function(){return!0},t}();!function(t){t[t.Normal=0]=\"Normal\",t[t.Magnet=1]=\"Magnet\"}(Q||(Q={}));var rt=function(t){function i(i,n){var e,s,r=t.call(this)||this;r.Di=null,r.Ai=NaN,r.Bi=0,r.Li=!0,r.Ei=new Map,r.Fi=!1,r.Oi=NaN,r.Vi=NaN,r.Pi=NaN,r.Wi=NaN,r.pi=i,r.zi=n,r.Ri=new Y(i,r),r.Ii=(e=function(){return r.Ai},s=function(){return r.Vi},function(t){var i=s(),n=e();if(t===f(r.Di).ji())return{et:n,ti:i};var o=f(t.kt());return{et:t.qi(i,o),ti:i}});var o=function(){return{rt:r.pi.bt().gi(r.Bi),ti:r.$t()}};return r.Ui=new et(r,i,o),r.Hi=new I(r),r}return C(i,t),i.prototype.W=function(){return this.zi},i.prototype.Yi=function(t,i){this.Pi=t,this.Wi=i},i.prototype.$i=function(){this.Pi=NaN,this.Wi=NaN},i.prototype.Ki=function(){return this.Pi},i.prototype.Xi=function(){return this.Wi},i.prototype.Zi=function(t,i,n){this.Fi||(this.Fi=!0),this.Li=!0,this.Ji(t,i,n)},i.prototype.Mt=function(){return this.Bi},i.prototype.$t=function(){return this.Oi},i.prototype.Kt=function(){return this.Vi},i.prototype.yt=function(){return this.Li},i.prototype.Gi=function(){this.Li=!1,this.Qi(),this.Ai=NaN,this.Oi=NaN,this.Vi=NaN,this.Di=null,this.$i()},i.prototype.tn=function(t){return null!==this.Di?[this.Hi,this.Ri]:[]},i.prototype.qt=function(t){return t===this.Di&&this.zi.horzLine.visible},i.prototype.Ut=function(){return this.zi.vertLine.visible},i.prototype.nn=function(t,i){this.Li&&this.Di===t||this.Ei.clear();var n=[];return this.Di===t&&n.push(this.sn(this.Ei,i,this.Ii)),n},i.prototype.Ti=function(){return this.Li?[this.Ui]:[]},i.prototype.It=function(){return this.Di},i.prototype.hn=function(){this.Hi.vt(),this.Ei.forEach(function(t){return t.vt()}),this.Ui.vt(),this.Ri.vt()},i.prototype.rn=function(t){return t&&!t.ji().wi()?t.ji():null},i.prototype.Ji=function(t,i,n){this.en(t,i,n)&&this.hn()},i.prototype.en=function(t,i,n){var e=this.Oi,s=this.Vi,r=this.Ai,o=this.Bi,h=this.Di,u=this.rn(n);this.Bi=t,this.Oi=isNaN(t)?NaN:this.pi.bt().At(t),this.Di=n;var a=null!==u?u.kt():null;return null!==u&&null!==a?(this.Ai=i,this.Vi=u.xt(i,a)):(this.Ai=NaN,this.Vi=NaN),e!==this.Oi||s!==this.Vi||o!==this.Bi||r!==this.Ai||h!==this.Di},i.prototype.Qi=function(){var t=this.pi._t().map(function(t){return t.an().un()}).filter(W),i=0===t.length?null:Math.max.apply(Math,t);this.Bi=null!==i?i:NaN},i.prototype.sn=function(t,i,n){var e=t.get(i);return void 0===e&&(e=new tt(this,i,n),t.set(i,e)),e},i}(st);function ot(t){return\"left\"===t||\"right\"===t}var ht=function(){function t(t){this.on=new Map,this.ln=[],this.fn=t}return t.prototype.cn=function(t,i){var n=function(t,i){return void 0===t?i:{vn:Math.max(t.vn,i.vn),_n:t._n||i._n}}(this.on.get(t),i);this.on.set(t,n)},t.prototype.dn=function(){return this.fn},t.prototype.wn=function(t){var i=this.on.get(t);return void 0===i?{vn:this.fn}:{vn:Math.max(this.fn,i.vn),_n:i._n}},t.prototype.Mn=function(){this.ln=[{bn:0}]},t.prototype.mn=function(t){this.ln=[{bn:1,St:t}]},t.prototype.pn=function(){this.ln=[{bn:4}]},t.prototype.gn=function(t){this.ln.push({bn:2,St:t})},t.prototype.yn=function(t){this.ln.push({bn:3,St:t})},t.prototype.kn=function(){return this.ln},t.prototype.Nn=function(t){for(var i=this,n=0,e=t.ln;n<e.length;n++){var s=e[n];this.xn(s)}this.fn=Math.max(this.fn,t.fn),t.on.forEach(function(t,n){i.cn(n,t)})},t.prototype.xn=function(t){switch(t.bn){case 0:this.Mn();break;case 1:this.mn(t.St);break;case 2:this.gn(t.St);break;case 3:this.yn(t.St);break;case 4:this.pn()}},t}();function ut(t,i){if(!k(t))return\"n/a\";if(!D(i))throw new TypeError(\"invalid length\");if(i<0||i>16)throw new TypeError(\"invalid length\");return 0===i?t.toString():(\"0000000000000000\"+t.toString()).slice(-i)}var at=function(){function t(t,i){if(i||(i=1),k(t)&&D(t)||(t=100),t<0)throw new TypeError(\"invalid base\");this._i=t,this.Cn=i,this.Sn()}return t.prototype.format=function(t){var i=t<0?\"−\":\"\";return t=Math.abs(t),i+this.Tn(t)},t.prototype.Sn=function(){if(this.Dn=0,this._i>0&&this.Cn>0)for(var t=this._i;t>1;)t/=10,this.Dn++},t.prototype.Tn=function(t){var i=this._i/this.Cn,n=Math.floor(t),e=\"\",s=void 0!==this.Dn?this.Dn:NaN;if(i>1){var r=+(Math.round(t*i)-n*i).toFixed(this.Dn);r>=i&&(r-=i,n+=1),e=\".\"+ut(+r.toFixed(this.Dn)*this.Cn,s)}else n=Math.round(n*i)/i,s>0&&(e=\".\"+ut(0,s));return n.toFixed(0)+e},t}(),lt=function(t){function i(i){return void 0===i&&(i=100),t.call(this,i)||this}return C(i,t),i.prototype.format=function(i){return\"\".concat(t.prototype.format.call(this,i),\"%\")},i}(at),ct=function(){function t(t){this.An=t}return t.prototype.format=function(t){var i=\"\";return t<0&&(i=\"-\",t=-t),t<995?i+this.Bn(t):t<999995?i+this.Bn(t/1e3)+\"K\":t<999999995?(t=1e3*Math.round(t/1e3),i+this.Bn(t/1e6)+\"M\"):(t=1e6*Math.round(t/1e6),i+this.Bn(t/1e9)+\"B\")},t.prototype.Bn=function(t){var i=Math.pow(10,this.An);return((t=Math.round(t*i)/i)>=1e-15&&t<1?t.toFixed(this.An).replace(/\\.?0+$/,\"\"):String(t)).replace(/(\\.[1-9]*)0+$/,function(t,i){return i})},t}();function ft(t,i,n,e){if(0!==i.length){var s=i[e.from].tt,r=i[e.from].it;t.moveTo(s,r);for(var o=e.from+1;o<e.to;++o){var h=i[o];if(1===n){var u=i[o-1].it,a=h.tt;t.lineTo(a,u)}t.lineTo(h.tt,h.it)}}}var pt=function(t){function i(){var i=null!==t&&t.apply(this,arguments)||this;return i.X=null,i}return C(i,t),i.prototype.Z=function(t){this.X=t},i.prototype.Y=function(t){if(null!==this.X&&0!==this.X.G.length&&null!==this.X.J){if(t.lineCap=\"butt\",t.lineJoin=\"round\",t.lineWidth=this.X.Vt,u(t,this.X.Pt),t.lineWidth=1,t.beginPath(),1===this.X.G.length){var i=this.X.G[0],n=this.X.Ln/2;t.moveTo(i.tt-n,this.X.En),t.lineTo(i.tt-n,i.it),t.lineTo(i.tt+n,i.it),t.lineTo(i.tt+n,this.X.En)}else t.moveTo(this.X.G[this.X.J.from].tt,this.X.En),t.lineTo(this.X.G[this.X.J.from].tt,this.X.G[this.X.J.from].it),ft(t,this.X.G,this.X.Fn,this.X.J),this.X.J.to>this.X.J.from&&(t.lineTo(this.X.G[this.X.J.to-1].tt,this.X.En),t.lineTo(this.X.G[this.X.J.from].tt,this.X.En));t.closePath(),t.fillStyle=this.On(t),t.fill()}},i}(V),vt=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return C(i,t),i.prototype.On=function(t){var i=this.X,n=t.createLinearGradient(0,0,0,i.Vn);return n.addColorStop(0,i.Pn),n.addColorStop(1,i.Wn),n},i}(pt),dt=function(t){function i(){var i=null!==t&&t.apply(this,arguments)||this;return i.X=null,i}return C(i,t),i.prototype.Z=function(t){this.X=t},i.prototype.Y=function(t){if(null!==this.X&&0!==this.X.G.length&&null!==this.X.J)if(t.lineCap=\"butt\",t.lineWidth=this.X.Vt,u(t,this.X.Pt),t.strokeStyle=this.zn(t),t.lineJoin=\"round\",1===this.X.G.length){t.beginPath();var i=this.X.G[0];t.moveTo(i.tt-this.X.Ln/2,i.it),t.lineTo(i.tt+this.X.Ln/2,i.it),void 0!==i.A&&(t.strokeStyle=i.A),t.stroke()}else this.Rn(t,this.X)},i.prototype.Rn=function(t,i){t.beginPath(),ft(t,i.G,i.Fn,i.J),t.stroke()},i}(V),yt=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return C(i,t),i.prototype.Rn=function(t,i){var n,e,s=i.G,r=i.J,o=i.Fn,h=i.ht;if(0!==s.length&&null!==r){t.beginPath();var u=s[r.from];t.moveTo(u.tt,u.it);var a=null!==(n=u.A)&&void 0!==n?n:h;t.strokeStyle=a;for(var l=function(i){t.stroke(),t.beginPath(),t.strokeStyle=i,a=i},c=r.from+1;c<r.to;++c){var f=s[c],p=s[c-1],v=null!==(e=f.A)&&void 0!==e?e:h;1===o&&(t.lineTo(f.tt,p.it),v!==a&&(l(v),t.moveTo(f.tt,p.it))),t.lineTo(f.tt,f.it),1!==o&&v!==a&&(l(v),t.moveTo(f.tt,f.it))}t.stroke()}},i.prototype.zn=function(){return this.X.ht},i}(dt);function wt(t,i,n,e,s){void 0===e&&(e=0),void 0===s&&(s=t.length);for(var r=s-e;0<r;){var o=r>>1,h=e+o;n(t[h],i)?(e=h+1,r-=o+1):r=o}return e}function gt(t,i,n,e,s){void 0===e&&(e=0),void 0===s&&(s=t.length);for(var r=s-e;0<r;){var o=r>>1,h=e+o;n(i,t[h])?r=o:(e=h+1,r-=o+1)}return e}function mt(t,i){return t.rt<i}function Mt(t,i){return t<i.rt}function _t(t,i,n){var e=i.In(),s=i.jn(),r=wt(t,e,mt),o=gt(t,s,Mt);if(!n)return{from:r,to:o};var h=r,u=o;return r>0&&r<t.length&&t[r].rt>=e&&(h=r-1),o>0&&o<t.length&&t[o-1].rt<=s&&(u=o+1),{from:h,to:u}}var bt=function(){function t(t,i,n){this.qn=!0,this.Un=!0,this.Hn=!0,this.Yn=[],this.$n=null,this.Kn=t,this.Xn=i,this.Zn=n}return t.prototype.vt=function(t){this.qn=!0,\"data\"===t&&(this.Un=!0),\"options\"===t&&(this.Hn=!0)},t.prototype.Jn=function(){this.Un&&(this.Gn(),this.Un=!1),this.qn&&(this.Qn(),this.qn=!1),this.Hn&&(this.ts(),this.Hn=!1)},t.prototype.ns=function(){this.$n=null},t.prototype.Qn=function(){var t=this.Kn.Ct(),i=this.Xn.bt();if(this.ns(),!i.wi()&&!t.wi()){var n=i.ss();if(null!==n&&0!==this.Kn.an().hs()){var e=this.Kn.kt();null!==e&&(this.$n=_t(this.Yn,n,this.Zn),this.rs(t,i,e.St))}}},t}(),St=function(t){function i(i,n){return t.call(this,i,n,!0)||this}return C(i,t),i.prototype.rs=function(t,i,n){i.es(this.Yn,N(this.$n)),t.us(this.Yn,n,N(this.$n))},i.prototype.os=function(t,i){return{rt:t,et:i,tt:NaN,it:NaN}},i.prototype.ts=function(){},i.prototype.Gn=function(){var t=this,i=this.Kn.ls();this.Yn=this.Kn.an().fs().map(function(n){var e=n.St[3];return t.cs(n.vs,e,i)})},i}(bt),Ct=function(t){function i(i,n){var e=t.call(this,i,n)||this;return e.zt=new G,e._s=new vt,e.ds=new yt,e.zt.U([e._s,e.ds]),e}return C(i,t),i.prototype.dt=function(t,i){if(!this.Kn.yt())return null;var n=this.Kn.W();return this.Jn(),this._s.Z({Fn:n.lineType,G:this.Yn,Pt:n.lineStyle,Vt:n.lineWidth,Pn:n.topColor,Wn:n.bottomColor,En:t,Vn:t,J:this.$n,Ln:this.Xn.bt().ws()}),this.ds.Z({Fn:n.lineType,G:this.Yn,ht:n.lineColor,Pt:n.lineStyle,Vt:n.lineWidth,J:this.$n,Ln:this.Xn.bt().ws()}),this.zt},i.prototype.cs=function(t,i){return this.os(t,i)},i}(St),xt=function(){function t(){this.Bt=null,this.Ms=0,this.bs=0}return t.prototype.Z=function(t){this.Bt=t},t.prototype.H=function(t,i,n,e){if(null!==this.Bt&&0!==this.Bt.an.length&&null!==this.Bt.J){this.Ms=this.ps(i),this.Ms>=2&&Math.max(1,Math.floor(i))%2!=this.Ms%2&&this.Ms--,this.bs=this.Bt.gs?Math.min(this.Ms,Math.floor(i)):this.Ms;for(var s=null,r=this.bs<=this.Ms&&this.Bt.ws>=Math.floor(1.5*i),o=this.Bt.J.from;o<this.Bt.J.to;++o){var h=this.Bt.an[o];s!==h.A&&(t.fillStyle=h.A,s=h.A);var u=Math.floor(.5*this.bs),a=Math.round(h.tt*i),l=a-u,c=this.bs,f=l+c-1,p=Math.min(h.ys,h.ks),v=Math.max(h.ys,h.ks),d=Math.round(p*i)-u,y=Math.round(v*i)+u,w=Math.max(y-d,this.bs);t.fillRect(l,d,c,w);var g=Math.ceil(1.5*this.Ms);if(r){if(this.Bt.Ns){var m=a-g,M=Math.max(d,Math.round(h.xs*i)-u),_=M+c-1;_>d+w-1&&(M=(_=d+w-1)-c+1),t.fillRect(m,M,l-m,_-M+1)}var b=a+g,S=Math.max(d,Math.round(h.Cs*i)-u),C=S+c-1;C>d+w-1&&(S=(C=d+w-1)-c+1),t.fillRect(f+1,S,b-f,C-S+1)}}}},t.prototype.ps=function(t){var i=Math.floor(t);return Math.max(i,Math.floor(function(t,i){return Math.floor(.3*t*i)}(f(this.Bt).ws,t)))},t}(),Bt=function(t){function i(i,n){return t.call(this,i,n,!1)||this}return C(i,t),i.prototype.rs=function(t,i,n){i.es(this.Yn,N(this.$n)),t.Ss(this.Yn,n,N(this.$n))},i.prototype.Ts=function(t,i,n){return{rt:t,open:i.St[0],high:i.St[1],low:i.St[2],close:i.St[3],tt:NaN,xs:NaN,ys:NaN,ks:NaN,Cs:NaN}},i.prototype.Gn=function(){var t=this,i=this.Kn.ls();this.Yn=this.Kn.an().fs().map(function(n){return t.cs(n.vs,n,i)})},i}(bt),zt=function(t){function i(){var i=null!==t&&t.apply(this,arguments)||this;return i.zt=new xt,i}return C(i,t),i.prototype.dt=function(t,i){if(!this.Kn.yt())return null;var n=this.Kn.W();this.Jn();var e={an:this.Yn,ws:this.Xn.bt().ws(),Ns:n.openVisible,gs:n.thinBars,J:this.$n};return this.zt.Z(e),this.zt},i.prototype.ts=function(){var t=this;this.Yn.forEach(function(i){i.A=t.Kn.ls().As(i.rt).Ds})},i.prototype.cs=function(t,i,n){return x(x({},this.Ts(t,i,n)),{A:n.As(t).Ds})},i}(Bt);function Tt(t,i,n){return Math.min(Math.max(t,i),n)}function kt(t,i,n){return i-t<=n}function Dt(t){return t<=0?NaN:Math.log(t)/Math.log(10)}function Et(t){var i=Math.ceil(t);return i%2!=0?i-1:i}function Pt(t){var i=Math.ceil(t);return i%2==0?i-1:i}var Lt=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return C(i,t),i.prototype.On=function(t){var i=this.X,n=t.createLinearGradient(0,0,0,i.Vn),e=Tt(i.En/i.Vn,0,1);return n.addColorStop(0,i.Bs),n.addColorStop(e,i.Ls),n.addColorStop(e,i.Es),n.addColorStop(1,i.Fs),n},i}(pt),Wt=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return C(i,t),i.prototype.zn=function(t){var i=this.X,n=t.createLinearGradient(0,0,0,i.Vn),e=Tt(i.En/i.Vn,0,1);return n.addColorStop(0,i.Pn),n.addColorStop(e,i.Pn),n.addColorStop(e,i.Wn),n.addColorStop(1,i.Wn),n},i}(dt),Nt=function(t){function i(i,n){var e=t.call(this,i,n)||this;return e.Os=new Lt,e.Vs=new Wt,e.ut=new G,e.ut.U([e.Os,e.Vs]),e}return C(i,t),i.prototype.dt=function(t,i){if(!this.Kn.yt())return null;var n=this.Kn.kt();if(null===n)return null;var e=this.Kn.W();this.Jn();var s=this.Kn.Ct().xt(e.baseValue.price,n.St),r=this.Xn.bt().ws();return this.Os.Z({G:this.Yn,Bs:e.topFillColor1,Ls:e.topFillColor2,Es:e.bottomFillColor1,Fs:e.bottomFillColor2,Vt:e.lineWidth,Pt:e.lineStyle,Fn:0,En:s,Vn:t,J:this.$n,Ln:r}),this.Vs.Z({G:this.Yn,Pn:e.topLineColor,Wn:e.bottomLineColor,Vt:e.lineWidth,Pt:e.lineStyle,Fn:0,En:s,Vn:t,J:this.$n,Ln:r}),this.ut},i.prototype.cs=function(t,i){return this.os(t,i)},i}(St),Rt=function(){function t(){this.Bt=null,this.Ms=0}return t.prototype.Z=function(t){this.Bt=t},t.prototype.H=function(t,i,n,e){if(null!==this.Bt&&0!==this.Bt.an.length&&null!==this.Bt.J){this.Ms=function(t,i){if(t>=2.5&&t<=4)return Math.floor(3*i);var n=1-.2*Math.atan(Math.max(4,t)-4)/(.5*Math.PI),e=Math.floor(t*n*i),s=Math.floor(t*i),r=Math.min(e,s);return Math.max(Math.floor(i),r)}(this.Bt.ws,i),this.Ms>=2&&Math.floor(i)%2!=this.Ms%2&&this.Ms--;var s=this.Bt.an;this.Bt.Ps&&this.Ws(t,s,this.Bt.J,i),this.Bt.zs&&this.Rs(t,s,this.Bt.J,this.Bt.ws,i);var r=this.Is(i);(!this.Bt.zs||this.Ms>2*r)&&this.js(t,s,this.Bt.J,i)}},t.prototype.Ws=function(t,i,n,e){if(null!==this.Bt){var s=\"\",r=Math.min(Math.floor(e),Math.floor(this.Bt.ws*e));r=Math.max(Math.floor(e),Math.min(r,this.Ms));for(var o=Math.floor(.5*r),h=null,u=n.from;u<n.to;u++){var a=i[u];a.qs!==s&&(t.fillStyle=a.qs,s=a.qs);var l=Math.round(Math.min(a.xs,a.Cs)*e),c=Math.round(Math.max(a.xs,a.Cs)*e),f=Math.round(a.ys*e),p=Math.round(a.ks*e),v=Math.round(e*a.tt)-o,d=v+r-1;null!==h&&(v=Math.max(h+1,v),v=Math.min(v,d));var y=d-v+1;t.fillRect(v,f,y,l-f),t.fillRect(v,c+1,y,p-c),h=d}}},t.prototype.Is=function(t){var i=Math.floor(1*t);this.Ms<=2*i&&(i=Math.floor(.5*(this.Ms-1)));var n=Math.max(Math.floor(t),i);return this.Ms<=2*n?Math.max(Math.floor(t),Math.floor(1*t)):n},t.prototype.Rs=function(t,i,n,e,s){if(null!==this.Bt)for(var r=\"\",o=this.Is(s),h=null,u=n.from;u<n.to;u++){var a=i[u];a.Tt!==r&&(t.fillStyle=a.Tt,r=a.Tt);var l=Math.round(a.tt*s)-Math.floor(.5*this.Ms),c=l+this.Ms-1,f=Math.round(Math.min(a.xs,a.Cs)*s),p=Math.round(Math.max(a.xs,a.Cs)*s);if(null!==h&&(l=Math.max(h+1,l),l=Math.min(l,c)),this.Bt.ws*s>2*o)Z(t,l,f,c-l+1,p-f+1,o);else{var v=c-l+1;t.fillRect(l,f,v,p-f+1)}h=c}},t.prototype.js=function(t,i,n,e){if(null!==this.Bt)for(var s=\"\",r=this.Is(e),o=n.from;o<n.to;o++){var h=i[o],u=Math.round(Math.min(h.xs,h.Cs)*e),a=Math.round(Math.max(h.xs,h.Cs)*e),l=Math.round(h.tt*e)-Math.floor(.5*this.Ms),c=l+this.Ms-1;if(h.A!==s){var f=h.A;t.fillStyle=f,s=f}this.Bt.zs&&(l+=r,u+=r,c-=r,a-=r),u>a||t.fillRect(l,u,c-l+1,a-u+1)}},t}(),Ot=function(t){function i(){var i=null!==t&&t.apply(this,arguments)||this;return i.zt=new Rt,i}return C(i,t),i.prototype.dt=function(t,i){if(!this.Kn.yt())return null;var n=this.Kn.W();this.Jn();var e={an:this.Yn,ws:this.Xn.bt().ws(),Ps:n.wickVisible,zs:n.borderVisible,J:this.$n};return this.zt.Z(e),this.zt},i.prototype.ts=function(){var t=this;this.Yn.forEach(function(i){var n=t.Kn.ls().As(i.rt);i.A=n.Ds,i.qs=n.Us,i.Tt=n.Hs})},i.prototype.cs=function(t,i,n){var e=n.As(t);return x(x({},this.Ts(t,i,n)),{A:e.Ds,qs:e.Us,Tt:e.Hs})},i}(Bt),At=function(){function t(){this.Bt=null,this.Ys=[]}return t.prototype.Z=function(t){this.Bt=t,this.Ys=[]},t.prototype.H=function(t,i,n,e){if(null!==this.Bt&&0!==this.Bt.G.length&&null!==this.Bt.J){this.Ys.length||this.$s(i);for(var s=Math.max(1,Math.floor(i)),r=Math.round(this.Bt.Ks*i)-Math.floor(s/2),o=r+s,h=this.Bt.J.from;h<this.Bt.J.to;h++){var u=this.Bt.G[h],a=this.Ys[h-this.Bt.J.from],l=Math.round(u.it*i);t.fillStyle=u.A;var c=void 0,f=void 0;l<=r?(c=l,f=o):(c=r,f=l-Math.floor(s/2)+s),t.fillRect(a.In,c,a.jn-a.In+1,f-c)}}},t.prototype.$s=function(t){if(null!==this.Bt&&0!==this.Bt.G.length&&null!==this.Bt.J){var i=Math.ceil(this.Bt.ws*t)<=1?0:Math.max(1,Math.floor(t)),n=Math.round(this.Bt.ws*t)-i;this.Ys=new Array(this.Bt.J.to-this.Bt.J.from);for(var e=this.Bt.J.from;e<this.Bt.J.to;e++){var s,r=this.Bt.G[e],o=Math.round(r.tt*t),h=void 0,u=void 0;n%2?(h=o-(s=(n-1)/2),u=o+s):(h=o-(s=n/2),u=o+s-1),this.Ys[e-this.Bt.J.from]={In:h,jn:u,Xs:o,Zs:r.tt*t,rt:r.rt}}for(e=this.Bt.J.from+1;e<this.Bt.J.to;e++){var a=this.Ys[e-this.Bt.J.from],l=this.Ys[e-this.Bt.J.from-1];a.rt===l.rt+1&&a.In-l.jn!==i+1&&(l.Xs>l.Zs?l.jn=a.In-i-1:a.In=l.jn+i+1)}var c=Math.ceil(this.Bt.ws*t);for(e=this.Bt.J.from;e<this.Bt.J.to;e++){(a=this.Ys[e-this.Bt.J.from]).jn<a.In&&(a.jn=a.In);var f=a.jn-a.In+1;c=Math.min(f,c)}if(i>0&&c<4)for(e=this.Bt.J.from;e<this.Bt.J.to;e++)(f=(a=this.Ys[e-this.Bt.J.from]).jn-a.In+1)>c&&(a.Xs>a.Zs?a.jn-=1:a.In+=1)}else this.Ys=[]},t}();function Gt(t){return{G:[],ws:t,Ks:NaN,J:null}}function Vt(t,i,n){return{rt:t,et:i,tt:NaN,it:NaN,A:n}}var Ft=function(t){function i(i,n){var e=t.call(this,i,n,!1)||this;return e.ut=new G,e.Js=Gt(0),e.zt=new At,e}return C(i,t),i.prototype.dt=function(t,i){return this.Kn.yt()?(this.Jn(),this.ut):null},i.prototype.Gn=function(){var t=this.Xn.bt().ws();this.Js=Gt(t);for(var i=0,n=0,e=this.Kn.W().color,s=0,r=this.Kn.an().fs();s<r.length;s++){var o=r[s],h=o.St[3],u=void 0!==o.A?o.A:e,a=Vt(o.vs,h,u);++i<this.Js.G.length?this.Js.G[i]=a:this.Js.G.push(a),this.Yn[n++]={rt:o.vs,tt:0}}this.zt.Z(this.Js),this.ut.U([this.zt])},i.prototype.ts=function(){},i.prototype.ns=function(){t.prototype.ns.call(this),this.Js.J=null},i.prototype.rs=function(t,i,n){if(null!==this.$n){var e=i.ws(),s=f(i.ss()),r=t.xt(this.Kn.W().base,n);i.es(this.Js.G),t.us(this.Js.G,n),this.Js.Ks=r,this.Js.J=_t(this.Js.G,s,!1),this.Js.ws=e,this.zt.Z(this.Js)}},i}(bt),jt=function(t){function i(i,n){var e=t.call(this,i,n)||this;return e.ds=new yt,e}return C(i,t),i.prototype.dt=function(t,i){if(!this.Kn.yt())return null;var n=this.Kn.W();this.Jn();var e={G:this.Yn,ht:n.color,Pt:n.lineStyle,Fn:n.lineType,Vt:n.lineWidth,J:this.$n,Ln:this.Xn.bt().ws()};return this.ds.Z(e),this.ds},i.prototype.ts=function(){var t=this;this.Yn.forEach(function(i){i.A=t.Kn.ls().As(i.rt).Ds})},i.prototype.cs=function(t,i,n){var e=this.os(t,i);return e.A=n.As(t).Ds,e},i}(St),Xt=/[2-9]/g,Yt=function(){function t(t){void 0===t&&(t=50),this.Gs=new Map,this.Qs=0,this.th=Array.from(new Array(t))}return t.prototype.ih=function(){this.Gs.clear(),this.th.fill(void 0)},t.prototype.Qt=function(t,i,n){var e=n||Xt,s=String(i).replace(e,\"0\"),r=this.Gs.get(s);if(void 0===r){if(0===(r=t.measureText(s).width)&&0!==i.length)return 0;var o=this.th[this.Qs];void 0!==o&&this.Gs.delete(o),this.th[this.Qs]=s,this.Qs=(this.Qs+1)%this.th.length,this.Gs.set(s,r)}return r},t}(),Jt=function(){function t(t){this.nh=null,this.k=null,this.sh=\"right\",this.hh=0,this.rh=t}return t.prototype.eh=function(t,i,n,e){this.nh=t,this.k=i,this.hh=n,this.sh=e},t.prototype.H=function(t,i){null!==this.k&&null!==this.nh&&this.nh.H(t,this.k,this.rh,this.hh,this.sh,i)},t}(),It=function(){function t(t,i,n){this.uh=t,this.rh=new Yt(50),this.ah=i,this.P=n,this.R=-1,this.zt=new Jt(this.rh)}return t.prototype.dt=function(t,i){var n=this.P.oh(this.ah);if(null===n)return null;var e=n.lh(this.ah)?n.fh():this.ah.Ct();if(null===e)return null;var s=n._h(e);if(\"overlay\"===s)return null;var r=this.P.dh();return r.S!==this.R&&(this.R=r.S,this.rh.ih()),this.zt.eh(this.uh.ci(),r,i,s),this.zt},t}(),Zt=function(){function t(){this.Bt=null}return t.prototype.Z=function(t){this.Bt=t},t.prototype.H=function(t,i,n,e){if(null!==this.Bt&&!1!==this.Bt.yt){var s=Math.round(this.Bt.it*i);if(!(s<0||s>Math.ceil(this.Bt.Yt*i))){var r=Math.ceil(this.Bt.Ht*i);t.lineCap=\"butt\",t.strokeStyle=this.Bt.A,t.lineWidth=Math.floor(this.Bt.Vt*i),u(t,this.Bt.Pt),a(t,s,0,r)}}},t}(),Kt=function(){function t(t){this.wh={Ht:0,Yt:0,it:0,A:\"rgba(0, 0, 0, 0)\",Vt:1,Pt:0,yt:!1},this.Mh=new Zt,this.ft=!0,this.Kn=t,this.Xn=t.jt(),this.Mh.Z(this.wh)}return t.prototype.vt=function(){this.ft=!0},t.prototype.dt=function(t,i){return this.Kn.yt()?(this.ft&&(this.bh(t,i),this.ft=!1),this.Mh):null},t}(),Ht=function(t){function i(i){return t.call(this,i)||this}return C(i,t),i.prototype.bh=function(t,i){this.wh.yt=!1;var n=this.Kn.Ct(),e=n.mh().mh;if(2===e||3===e){var s=this.Kn.W();if(s.baseLineVisible&&this.Kn.yt()){var r=this.Kn.kt();null!==r&&(this.wh.yt=!0,this.wh.it=n.xt(r.St,r.St),this.wh.Ht=i,this.wh.Yt=t,this.wh.A=s.baseLineColor,this.wh.Vt=s.baseLineWidth,this.wh.Pt=s.baseLineStyle)}}},i}(Kt),Ut=function(){function t(){this.Bt=null}return t.prototype.Z=function(t){this.Bt=t},t.prototype.ph=function(){return this.Bt},t.prototype.H=function(t,i,n,e){var s=this.Bt;if(null!==s){t.save();var r=Math.max(1,Math.floor(i)),o=r%2/2,h=Math.round(s.Zs.x*i)+o,u=s.Zs.y*i;t.fillStyle=s.gh,t.beginPath();var a=Math.max(2,1.5*s.yh)*i;t.arc(h,u,a,0,2*Math.PI,!1),t.fill(),t.fillStyle=s.kh,t.beginPath(),t.arc(h,u,s.st*i,0,2*Math.PI,!1),t.fill(),t.lineWidth=r,t.strokeStyle=s.Nh,t.beginPath(),t.arc(h,u,s.st*i+r/2,0,2*Math.PI,!1),t.stroke(),t.restore()}},t}(),Qt=[{xh:0,Ch:.25,Sh:4,Th:10,Dh:.25,Ah:0,Bh:.4,Lh:.8},{xh:.25,Ch:.525,Sh:10,Th:14,Dh:0,Ah:0,Bh:.8,Lh:0},{xh:.525,Ch:1,Sh:14,Th:14,Dh:0,Ah:0,Bh:0,Lh:0}];function $t(t,i,n,e){return function(t,i){if(\"transparent\"===t)return t;var n=_(t),e=n[3];return\"rgba(\".concat(n[0],\", \").concat(n[1],\", \").concat(n[2],\", \").concat(i*e,\")\")}(t,n+(e-n)*i)}function qt(t,i){for(var n,e=t%2600/2600,s=0,r=Qt;s<r.length;s++){var o=r[s];if(e>=o.xh&&e<=o.Ch){n=o;break}}l(void 0!==n,\"Last price animation internal logic error\");var h,u,a,c=(e-n.xh)/(n.Ch-n.xh);return{kh:$t(i,c,n.Dh,n.Ah),Nh:$t(i,c,n.Bh,n.Lh),st:(h=c,u=n.Sh,a=n.Th,u+(a-u)*h)}}var ti=function(){function t(t){this.zt=new Ut,this.ft=!0,this.Eh=!0,this.Fh=performance.now(),this.Oh=this.Fh-1,this.Vh=t}return t.prototype.Ph=function(){this.Oh=this.Fh-1,this.vt()},t.prototype.Wh=function(){if(this.vt(),2===this.Vh.W().lastPriceAnimation){var t=performance.now(),i=this.Oh-t;if(i>0)return void(i<650&&(this.Oh+=2600));this.Fh=t,this.Oh=t+2600}},t.prototype.vt=function(){this.ft=!0},t.prototype.zh=function(){this.Eh=!0},t.prototype.yt=function(){return 0!==this.Vh.W().lastPriceAnimation},t.prototype.Rh=function(){switch(this.Vh.W().lastPriceAnimation){case 0:return!1;case 1:return!0;case 2:return performance.now()<=this.Oh}},t.prototype.dt=function(t,i){return this.ft?(this.wt(t,i),this.ft=!1,this.Eh=!1):this.Eh&&(this.Ih(),this.Eh=!1),this.zt},t.prototype.wt=function(t,i){this.zt.Z(null);var n=this.Vh.jt().bt(),e=n.ss(),s=this.Vh.kt();if(null!==e&&null!==s){var r=this.Vh.jh(!0);if(!r.qh&&e.Uh(r.vs)){var o={x:n.At(r.vs),y:this.Vh.Ct().xt(r.et,s.St)},h=r.A,u=this.Vh.W().lineWidth,a=qt(this.Hh(),h);this.zt.Z({gh:h,yh:u,kh:a.kh,Nh:a.Nh,st:a.st,Zs:o})}}},t.prototype.Ih=function(){var t=this.zt.ph();if(null!==t){var i=qt(this.Hh(),t.gh);t.kh=i.kh,t.Nh=i.Nh,t.st=i.st}},t.prototype.Hh=function(){return this.Rh()?performance.now()-this.Fh:2599},t}();function ii(t,i){return Pt(Math.min(Math.max(t,12),30)*i)}function ni(t,i){switch(t){case\"arrowDown\":case\"arrowUp\":return ii(i,1);case\"circle\":return ii(i,.8);case\"square\":return ii(i,.7)}}function ei(t){return Et(ii(t,1))}function si(t){return Math.max(ii(t,.1),3)}function ri(t,i,n,e,s){var r=ni(\"square\",n),o=(r-1)/2,h=t-o,u=i-o;return e>=h&&e<=h+r&&s>=u&&s<=u+r}function oi(t,i,n,e,s){var r=(ni(\"arrowUp\",s)-1)/2,o=(Pt(s/2)-1)/2;i.beginPath(),t?(i.moveTo(n-r,e),i.lineTo(n,e-r),i.lineTo(n+r,e),i.lineTo(n+o,e),i.lineTo(n+o,e+r),i.lineTo(n-o,e+r),i.lineTo(n-o,e)):(i.moveTo(n-r,e),i.lineTo(n,e+r),i.lineTo(n+r,e),i.lineTo(n+o,e),i.lineTo(n+o,e-r),i.lineTo(n-o,e-r),i.lineTo(n-o,e)),i.fill()}var hi=function(t){function i(){var i=null!==t&&t.apply(this,arguments)||this;return i.Bt=null,i.rh=new Yt,i.R=-1,i.I=\"\",i.Yh=\"\",i}return C(i,t),i.prototype.Z=function(t){this.Bt=t},i.prototype.eh=function(t,i){this.R===t&&this.I===i||(this.R=t,this.I=i,this.Yh=O(t,i),this.rh.ih())},i.prototype.$h=function(t,i){if(null===this.Bt||null===this.Bt.J)return null;for(var n=this.Bt.J.from;n<this.Bt.J.to;n++){var e=this.Bt.G[n];if(ai(e,t,i))return{Kh:e.Xh,Zh:e.Zh}}return null},i.prototype.Y=function(t,i,n){if(null!==this.Bt&&null!==this.Bt.J){t.textBaseline=\"middle\",t.font=this.Yh;for(var e=this.Bt.J.from;e<this.Bt.J.to;e++){var s=this.Bt.G[e];void 0!==s.Gt&&(s.Gt.Ht=this.rh.Qt(t,s.Gt.Jh),s.Gt.Yt=this.R),ui(s,t)}}},i}(V);function ui(t,i){i.fillStyle=t.A,void 0!==t.Gt&&function(t,i,n,e){t.fillText(i,n,e)}(i,t.Gt.Jh,t.tt-t.Gt.Ht/2,t.Gt.it),function(t,i){if(0!==t.hs){switch(t.Gh){case\"arrowDown\":return void oi(!1,i,t.tt,t.it,t.hs);case\"arrowUp\":return void oi(!0,i,t.tt,t.it,t.hs);case\"circle\":return void function(t,i,n,e){var s=(ni(\"circle\",e)-1)/2;t.beginPath(),t.arc(i,n,s,0,2*Math.PI,!1),t.fill()}(i,t.tt,t.it,t.hs);case\"square\":return void function(t,i,n,e){var s=ni(\"square\",e),r=(s-1)/2,o=i-r,h=n-r;t.fillRect(o,h,s,s)}(i,t.tt,t.it,t.hs)}t.Gh}}(t,i)}function ai(t,i,n){return!(void 0===t.Gt||!function(t,i,n,e,s,r){var o=e/2;return s>=t&&s<=t+n&&r>=i-o&&r<=i+o}(t.tt,t.Gt.it,t.Gt.Ht,t.Gt.Yt,i,n))||function(t,i,n){if(0===t.hs)return!1;switch(t.Gh){case\"arrowDown\":case\"arrowUp\":return function(t,i,n,e,s,r){return ri(i,n,e,s,r)}(0,t.tt,t.it,t.hs,i,n);case\"circle\":return function(t,i,n,e,s){var r=2+ni(\"circle\",n)/2,o=t-e,h=i-s;return Math.sqrt(o*o+h*h)<=r}(t.tt,t.it,t.hs,i,n);case\"square\":return ri(t.tt,t.it,t.hs,i,n)}}(t,i,n)}function li(t,i,n,e,s,r,o,h,u){var a=k(n)?n:n.close,l=k(n)?n:n.high,c=k(n)?n:n.low,f=k(i.size)?Math.max(i.size,0):1,p=ei(h.ws())*f,v=p/2;switch(t.hs=p,i.position){case\"inBar\":return t.it=o.xt(a,u),void(void 0!==t.Gt&&(t.Gt.it=t.it+v+r+.6*s));case\"aboveBar\":return t.it=o.xt(l,u)-v-e.Qh,void 0!==t.Gt&&(t.Gt.it=t.it-v-.6*s,e.Qh+=1.2*s),void(e.Qh+=p+r);case\"belowBar\":return t.it=o.xt(c,u)+v+e.tr,void 0!==t.Gt&&(t.Gt.it=t.it+v+r+.6*s,e.tr+=1.2*s),void(e.tr+=p+r)}i.position}var ci=function(){function t(t,i){this.ft=!0,this.ir=!0,this.nr=!0,this.sr=null,this.zt=new hi,this.Vh=t,this.pi=i,this.Bt={G:[],J:null}}return t.prototype.vt=function(t){this.ft=!0,this.nr=!0,\"data\"===t&&(this.ir=!0)},t.prototype.dt=function(t,i,n){if(!this.Vh.yt())return null;this.ft&&this.Jn();var e=this.pi.W().layout;return this.zt.eh(e.fontSize,e.fontFamily),this.zt.Z(this.Bt),this.zt},t.prototype.hr=function(){if(this.nr){if(this.Vh.rr().length>0){var t=this.pi.bt().ws(),i=si(t),n=1.5*ei(t)+2*i;this.sr={above:n,below:n}}else this.sr=null;this.nr=!1}return this.sr},t.prototype.Jn=function(){var t=this.Vh.Ct(),i=this.pi.bt(),n=this.Vh.rr();this.ir&&(this.Bt.G=n.map(function(t){return{rt:t.time,tt:0,it:0,hs:0,Gh:t.shape,A:t.color,Xh:t.Xh,Zh:t.id,Gt:void 0}}),this.ir=!1);var e=this.pi.W().layout;this.Bt.J=null;var s=i.ss();if(null!==s){var r=this.Vh.kt();if(null!==r&&0!==this.Bt.G.length){var o=NaN,h=si(i.ws()),u={Qh:h,tr:h};this.Bt.J=_t(this.Bt.G,s,!0);for(var a=this.Bt.J.from;a<this.Bt.J.to;a++){var l=n[a];l.time!==o&&(u.Qh=h,u.tr=h,o=l.time);var c=this.Bt.G[a];c.tt=i.At(l.time),void 0!==l.text&&l.text.length>0&&(c.Gt={Jh:l.text,it:0,Ht:0,Yt:0});var f=this.Vh.er(l.time);null!==f&&li(c,l,f,u,e.fontSize,h,t,i,r.St)}this.ft=!1}}},t}(),fi=function(t){function i(i){return t.call(this,i)||this}return C(i,t),i.prototype.bh=function(t,i){var n=this.wh;n.yt=!1;var e=this.Kn.W();if(e.priceLineVisible&&this.Kn.yt()){var s=this.Kn.jh(0===e.priceLineSource);s.qh||(n.yt=!0,n.it=s.ti,n.A=this.Kn.ur(s.A),n.Ht=i,n.Yt=t,n.Vt=e.priceLineWidth,n.Pt=e.priceLineStyle)}},i}(Kt),pi=function(t){function i(i){var n=t.call(this)||this;return n.Rt=i,n}return C(i,t),i.prototype.vi=function(t,i,n){t.yt=!1,i.yt=!1;var e=this.Rt;if(e.yt()){var s=e.W(),r=s.lastValueVisible,o=\"\"!==e.ar(),h=0===s.seriesLastValueMode,u=e.jh(!1);if(!u.qh){r&&(t.Gt=this.lr(u,r,h),t.yt=0!==t.Gt.length),(o||h)&&(i.Gt=this.cr(u,r,o,h),i.yt=i.Gt.length>0);var a=e.ur(u.A),l=b(a);n.t=l.t,n.A=l.i,n.ti=u.ti,i.Tt=e.jt().Dt(u.ti/e.Ct().Yt()),t.Tt=a}}},i.prototype.cr=function(t,i,n,e){var s=\"\",r=this.Rt.ar();return n&&0!==r.length&&(s+=\"\".concat(r,\" \")),i&&e&&(s+=this.Rt.Ct().vr()?t._r:t.dr),s.trim()},i.prototype.lr=function(t,i,n){return i?n?this.Rt.Ct().vr()?t.dr:t._r:t.Gt:\"\"},i}(q),vi=function(){function t(t,i){this.wr=t,this.Mr=i}return t.prototype.br=function(t){return null!==t&&this.wr===t.wr&&this.Mr===t.Mr},t.prototype.mr=function(){return new t(this.wr,this.Mr)},t.prototype.pr=function(){return this.wr},t.prototype.gr=function(){return this.Mr},t.prototype.yr=function(){return this.Mr-this.wr},t.prototype.wi=function(){return this.Mr===this.wr||Number.isNaN(this.Mr)||Number.isNaN(this.wr)},t.prototype.Nn=function(i){return null===i?this:new t(Math.min(this.pr(),i.pr()),Math.max(this.gr(),i.gr()))},t.prototype.kr=function(t){if(k(t)&&0!=this.Mr-this.wr){var i=.5*(this.Mr+this.wr),n=this.Mr-i,e=this.wr-i;n*=t,e*=t,this.Mr=i+n,this.wr=i+e}},t.prototype.Nr=function(t){k(t)&&(this.Mr+=t,this.wr+=t)},t.prototype.Cr=function(){return{minValue:this.wr,maxValue:this.Mr}},t.Sr=function(i){return null===i?null:new t(i.minValue,i.maxValue)},t}(),di=function(){function t(t,i){this.Tr=t,this.Dr=i||null}return t.prototype.Ar=function(){return this.Tr},t.prototype.Br=function(){return this.Dr},t.prototype.Cr=function(){return null===this.Tr?null:{priceRange:this.Tr.Cr(),margins:this.Dr||void 0}},t.Sr=function(i){return null===i?null:new t(vi.Sr(i.priceRange),i.margins)},t}(),yi=function(t){function i(i,n){var e=t.call(this,i)||this;return e.Lr=n,e}return C(i,t),i.prototype.bh=function(t,i){var n=this.wh;n.yt=!1;var e=this.Lr.W();if(this.Kn.yt()&&e.lineVisible){var s=this.Lr.Er();null!==s&&(n.yt=!0,n.it=s,n.A=e.color,n.Ht=i,n.Yt=t,n.Vt=e.lineWidth,n.Pt=e.lineStyle)}},i}(Kt),wi=function(t){function i(i,n){var e=t.call(this)||this;return e.Vh=i,e.Lr=n,e}return C(i,t),i.prototype.vi=function(t,i,n){t.yt=!1,i.yt=!1;var e=this.Lr.W(),s=e.axisLabelVisible,r=\"\"!==e.title,o=this.Vh;if(s&&o.yt()){var h=this.Lr.Er();if(null!==h){r&&(i.Gt=e.title,i.yt=!0),i.Tt=o.jt().Dt(h/o.Ct().Yt()),t.Gt=o.Ct().Fr(e.price),t.yt=!0;var u=b(e.color);n.t=u.t,n.A=u.i,n.ti=h}}},i}(q),gi=function(){function t(t,i){this.Vh=t,this.zi=i,this.Or=new yi(t,this),this.uh=new wi(t,this),this.Vr=new It(this.uh,t,t.jt())}return t.prototype.Pr=function(t){T(this.zi,t),this.vt(),this.Vh.jt().Wr()},t.prototype.W=function(){return this.zi},t.prototype.tn=function(){return[this.Or,this.Vr]},t.prototype.zr=function(){return this.uh},t.prototype.vt=function(){this.Or.vt(),this.uh.vt()},t.prototype.Er=function(){var t=this.Vh,i=t.Ct();if(t.jt().bt().wi()||i.wi())return null;var n=t.kt();return null===n?null:i.xt(this.zi.price,n.St)},t}(),mi=function(t){function i(i){var n=t.call(this)||this;return n.pi=i,n}return C(i,t),i.prototype.jt=function(){return this.pi},i}(st),Mi={Ds:\"\",Hs:\"\",Us:\"\"},_i=function(){function t(t){this.Vh=t}return t.prototype.As=function(t,i){var n=this.Vh.Rr(),e=this.Vh.W();switch(n){case\"Line\":return this.Ir(e,t,i);case\"Area\":return this.jr(e);case\"Baseline\":return this.qr(e,t,i);case\"Bar\":return this.Ur(e,t,i);case\"Candlestick\":return this.Hr(e,t,i);case\"Histogram\":return this.Yr(e,t,i)}throw new Error(\"Unknown chart style\")},t.prototype.Ur=function(t,i,n){var e=x({},Mi),s=t.upColor,r=t.downColor,o=s,h=r,u=f(this.$r(i,n)),a=p(u.St[0])<=p(u.St[3]);return void 0!==u.A?(e.Ds=u.A,e.Hs=u.A):(e.Ds=a?s:r,e.Hs=a?o:h),e},t.prototype.Hr=function(t,i,n){var e,s,r,o=x({},Mi),h=t.upColor,u=t.downColor,a=t.borderUpColor,l=t.borderDownColor,c=t.wickUpColor,v=t.wickDownColor,d=f(this.$r(i,n)),y=p(d.St[0])<=p(d.St[3]);return o.Ds=null!==(e=d.A)&&void 0!==e?e:y?h:u,o.Hs=null!==(s=d.Tt)&&void 0!==s?s:y?a:l,o.Us=null!==(r=d.qs)&&void 0!==r?r:y?c:v,o},t.prototype.jr=function(t){return x(x({},Mi),{Ds:t.lineColor})},t.prototype.qr=function(t,i,n){var e=f(this.$r(i,n)).St[3]>=t.baseValue.price;return x(x({},Mi),{Ds:e?t.topLineColor:t.bottomLineColor})},t.prototype.Ir=function(t,i,n){var e,s=f(this.$r(i,n));return x(x({},Mi),{Ds:null!==(e=s.A)&&void 0!==e?e:t.color})},t.prototype.Yr=function(t,i,n){var e=x({},Mi),s=f(this.$r(i,n));return e.Ds=void 0!==s.A?s.A:t.color,e},t.prototype.$r=function(t,i){return void 0!==i?i.St:this.Vh.an().Kr(t)},t}(),bi=function(){function t(){this.Xr=[],this.Zr=new Map,this.Jr=new Map}return t.prototype.Gr=function(){return this.hs()>0?this.Xr[this.Xr.length-1]:null},t.prototype.Qr=function(){return this.hs()>0?this.te(0):null},t.prototype.un=function(){return this.hs()>0?this.te(this.Xr.length-1):null},t.prototype.hs=function(){return this.Xr.length},t.prototype.wi=function(){return 0===this.hs()},t.prototype.Uh=function(t){return null!==this.ie(t,0)},t.prototype.Kr=function(t){return this.ne(t)},t.prototype.ne=function(t,i){void 0===i&&(i=0);var n=this.ie(t,i);return null===n?null:x(x({},this.se(n)),{vs:this.te(n)})},t.prototype.fs=function(){return this.Xr},t.prototype.he=function(t,i,n){if(this.wi())return null;for(var e=null,s=0,r=n;s<r.length;s++){var o=r[s];e=Si(e,this.re(t,i,o))}return e},t.prototype.Z=function(t){this.Jr.clear(),this.Zr.clear(),this.Xr=t},t.prototype.te=function(t){return this.Xr[t].vs},t.prototype.se=function(t){return this.Xr[t]},t.prototype.ie=function(t,i){var n=this.ee(t);if(null===n&&0!==i)switch(i){case-1:return this.ue(t);case 1:return this.ae(t);default:throw new TypeError(\"Unknown search mode\")}return n},t.prototype.ue=function(t){var i=this.oe(t);return i>0&&(i-=1),i!==this.Xr.length&&this.te(i)<t?i:null},t.prototype.ae=function(t){var i=this.le(t);return i!==this.Xr.length&&t<this.te(i)?i:null},t.prototype.ee=function(t){var i=this.oe(t);return i===this.Xr.length||t<this.Xr[i].vs?null:i},t.prototype.oe=function(t){return wt(this.Xr,t,function(t,i){return t.vs<i})},t.prototype.le=function(t){return gt(this.Xr,t,function(t,i){return i.vs>t})},t.prototype.fe=function(t,i,n){for(var e=null,s=t;s<i;s++){var r=this.Xr[s].St[n];Number.isNaN(r)||(null===e?e={ce:r,ve:r}:(r<e.ce&&(e.ce=r),r>e.ve&&(e.ve=r)))}return e},t.prototype.re=function(t,i,n){if(this.wi())return null;var e=null,s=f(this.Qr()),r=f(this.un()),o=Math.max(t,s),h=Math.min(i,r),u=30*Math.ceil(o/30),a=Math.max(u,30*Math.floor(h/30)),l=this.oe(o),c=this.le(Math.min(h,u,i));e=Si(e,this.fe(l,c,n));var p=this.Zr.get(n);void 0===p&&(p=new Map,this.Zr.set(n,p));for(var v=Math.max(u+1,o);v<a;v+=30){var d=Math.floor(v/30),y=p.get(d);if(void 0===y){var w=this.oe(30*d),g=this.le(30*(d+1)-1);y=this.fe(w,g,n),p.set(d,y)}e=Si(e,y)}return l=this.oe(a),c=this.le(h),Si(e,this.fe(l,c,n))},t}();function Si(t,i){return null===t?i:null===i?t:{ce:Math.min(t.ce,i.ce),ve:Math.max(t.ve,i.ve)}}var Ci=function(t){function i(i,n,e){var s=t.call(this,i)||this;s.Bt=new bi,s.Or=new fi(s),s._e=[],s.de=new Ht(s),s.we=null,s.Me=null,s.be=[],s.me=[],s.pe=null,s.zi=n,s.ge=e;var r=new pi(s);return s.Ei=[r],s.Vr=new It(r,s,i),\"Area\"!==e&&\"Line\"!==e&&\"Baseline\"!==e||(s.we=new ti(s)),s.ye(),s.ke(),s}return C(i,t),i.prototype.g=function(){null!==this.pe&&clearTimeout(this.pe)},i.prototype.ur=function(t){return this.zi.priceLineColor||t},i.prototype.jh=function(t){var i={qh:!0},n=this.Ct();if(this.jt().bt().wi()||n.wi()||this.Bt.wi())return i;var e,s,r=this.jt().bt().ss(),o=this.kt();if(null===r||null===o)return i;if(t){var h=this.Bt.Gr();if(null===h)return i;e=h,s=h.vs}else{var u=this.Bt.ne(r.jn(),-1);if(null===u)return i;if(null===(e=this.Bt.Kr(u.vs)))return i;s=u.vs}var a=e.St[3],l=this.ls().As(s,{St:e}),c=n.xt(a,o.St);return{qh:!1,et:a,Gt:n.Mi(a,o.St),_r:n.Fr(a),dr:n.Ne(a,o.St),A:l.Ds,ti:c,vs:s}},i.prototype.ls=function(){return null!==this.Me||(this.Me=new _i(this)),this.Me},i.prototype.W=function(){return this.zi},i.prototype.Pr=function(t){var i=t.priceScaleId;void 0!==i&&i!==this.zi.priceScaleId&&this.jt().xe(this,i),T(this.zi,t),null!==this.ki&&void 0!==t.scaleMargins&&this.ki.Pr({scaleMargins:t.scaleMargins}),void 0!==t.priceFormat&&(this.ye(),this.jt().Ce()),this.jt().Se(this),this.jt().Te(),this.Hi.vt(\"options\")},i.prototype.Z=function(t,i){this.Bt.Z(t),this.De(),this.Hi.vt(\"data\"),this.Ri.vt(\"data\"),null!==this.we&&(i&&i.Ae?this.we.Wh():0===t.length&&this.we.Ph());var n=this.jt().oh(this);this.jt().Be(n),this.jt().Se(this),this.jt().Te(),this.jt().Wr()},i.prototype.Le=function(t){this.be=t.map(function(t){return x({},t)}),this.De();var i=this.jt().oh(this);this.Ri.vt(\"data\"),this.jt().Be(i),this.jt().Se(this),this.jt().Te(),this.jt().Wr()},i.prototype.rr=function(){return this.me},i.prototype.Ee=function(t){var i=new gi(this,t);return this._e.push(i),this.jt().Se(this),i},i.prototype.Fe=function(t){var i=this._e.indexOf(t);-1!==i&&this._e.splice(i,1),this.jt().Se(this)},i.prototype.Rr=function(){return this.ge},i.prototype.kt=function(){var t=this.Oe();return null===t?null:{St:t.St[3],Ve:t.rt}},i.prototype.Oe=function(){var t=this.jt().bt().ss();if(null===t)return null;var i=t.In();return this.Bt.ne(i,1)},i.prototype.an=function(){return this.Bt},i.prototype.er=function(t){var i=this.Bt.Kr(t);return null===i?null:\"Bar\"===this.ge||\"Candlestick\"===this.ge?{open:i.St[0],high:i.St[1],low:i.St[2],close:i.St[3]}:i.St[3]},i.prototype.Pe=function(t){var i=this,n=this.we;return null!==n&&n.yt()?(null===this.pe&&n.Rh()&&(this.pe=setTimeout(function(){i.pe=null,i.jt().We()},0)),n.zh(),[n]):[]},i.prototype.tn=function(){var t=[];this.ze()||t.push(this.de);for(var i=0,n=this._e;i<n.length;i++){var e=n[i];t.push.apply(t,e.tn())}return t.push(this.Hi,this.Or,this.Vr,this.Ri),t},i.prototype.nn=function(t,i){if(i!==this.ki&&!this.ze())return[];for(var n=B([],this.Ei,!0),e=0,s=this._e;e<s.length;e++){var r=s[e];n.push(r.zr())}return n},i.prototype.Re=function(t,i){var n=this;if(void 0!==this.zi.autoscaleInfoProvider){var e=this.zi.autoscaleInfoProvider(function(){var e=n.Ie(t,i);return null===e?null:e.Cr()});return di.Sr(e)}return this.Ie(t,i)},i.prototype.je=function(){return this.zi.priceFormat.minMove},i.prototype.qe=function(){return this.Ue},i.prototype.hn=function(){var t;this.Hi.vt(),this.Ri.vt();for(var i=0,n=this.Ei;i<n.length;i++)n[i].vt();for(var e=0,s=this._e;e<s.length;e++)s[e].vt();this.Or.vt(),this.de.vt(),null===(t=this.we)||void 0===t||t.vt()},i.prototype.Ct=function(){return f(t.prototype.Ct.call(this))},i.prototype.gt=function(t){if(\"Line\"!==this.ge&&\"Area\"!==this.ge&&\"Baseline\"!==this.ge||!this.zi.crosshairMarkerVisible)return null;var i=this.Bt.Kr(t);return null===i?null:{et:i.St[3],st:this.He(),Tt:this.Ye(),Nt:this.$e(t)}},i.prototype.ar=function(){return this.zi.title},i.prototype.yt=function(){return this.zi.visible},i.prototype.ze=function(){return!ot(this.Ct().Ke())},i.prototype.Ie=function(t,i){if(!D(t)||!D(i)||this.Bt.wi())return null;var n=\"Line\"===this.ge||\"Area\"===this.ge||\"Baseline\"===this.ge||\"Histogram\"===this.ge?[3]:[2,1],e=this.Bt.he(t,i,n),s=null!==e?new vi(e.ce,e.ve):null;if(\"Histogram\"===this.Rr()){var r=this.zi.base,o=new vi(r,r);s=null!==s?s.Nn(o):o}return new di(s,this.Ri.hr())},i.prototype.He=function(){switch(this.ge){case\"Line\":case\"Area\":case\"Baseline\":return this.zi.crosshairMarkerRadius}return 0},i.prototype.Ye=function(){switch(this.ge){case\"Line\":case\"Area\":case\"Baseline\":var t=this.zi.crosshairMarkerBorderColor;if(0!==t.length)return t}return null},i.prototype.$e=function(t){switch(this.ge){case\"Line\":case\"Area\":case\"Baseline\":var i=this.zi.crosshairMarkerBackgroundColor;if(0!==i.length)return i}return this.ls().As(t).Ds},i.prototype.ye=function(){switch(this.zi.priceFormat.type){case\"custom\":this.Ue={format:this.zi.priceFormat.formatter};break;case\"volume\":this.Ue=new ct(this.zi.priceFormat.precision);break;case\"percent\":this.Ue=new lt(this.zi.priceFormat.precision);break;default:var t=Math.pow(10,this.zi.priceFormat.precision);this.Ue=new at(t,this.zi.priceFormat.minMove*t)}null!==this.ki&&this.ki.Xe()},i.prototype.De=function(){var t=this,i=this.jt().bt();if(i.wi()||0===this.Bt.hs())this.me=[];else{var n=f(this.Bt.Qr());this.me=this.be.map(function(e,s){var r=f(i.Ze(e.time,!0)),o=r<n?1:-1;return{time:f(t.Bt.ne(r,o)).vs,position:e.position,shape:e.shape,color:e.color,id:e.id,Xh:s,text:e.text,size:e.size}})}},i.prototype.ke=function(){switch(this.Ri=new ci(this,this.jt()),this.ge){case\"Bar\":this.Hi=new zt(this,this.jt());break;case\"Candlestick\":this.Hi=new Ot(this,this.jt());break;case\"Line\":this.Hi=new jt(this,this.jt());break;case\"Area\":this.Hi=new Ct(this,this.jt());break;case\"Baseline\":this.Hi=new Nt(this,this.jt());break;case\"Histogram\":this.Hi=new Ft(this,this.jt());break;default:throw Error(\"Unknown chart style assigned: \"+this.ge)}},i}(mi),xi=function(){function t(t){this.zi=t}return t.prototype.Je=function(t,i,n){var e=t;if(0===this.zi.mode)return e;var s=n.ji(),r=s.kt();if(null===r)return e;var o=s.xt(t,r),h=n.Ge().filter(function(t){return t instanceof Ci}).reduce(function(t,e){if(n.lh(e)||!e.yt())return t;var s=e.Ct(),r=e.an();if(s.wi()||!r.Uh(i))return t;var o=r.Kr(i);if(null===o)return t;var h=p(e.kt());return t.concat([s.xt(o.St[3],h.St)])},[]);if(0===h.length)return e;h.sort(function(t,i){return Math.abs(t-o)-Math.abs(i-o)});var u=h[0];return s.qi(u,r)},t}(),Bi=function(){function t(){this.Bt=null}return t.prototype.Z=function(t){this.Bt=t},t.prototype.H=function(t,i,n,e){var s=this;if(null!==this.Bt){var r=Math.max(1,Math.floor(i));t.lineWidth=r;var o=Math.ceil(this.Bt.Ot*i),h=Math.ceil(this.Bt.Ft*i);!function(t,i){t.save(),t.lineWidth%2&&t.translate(.5,.5),i(),t.restore()}(t,function(){var n=f(s.Bt);if(n.Qe){t.strokeStyle=n.tu,u(t,n.iu),t.beginPath();for(var e=0,a=n.nu;e<a.length;e++){var l=a[e],c=Math.round(l.su*i);t.moveTo(c,-r),t.lineTo(c,o+r)}t.stroke()}if(n.hu){t.strokeStyle=n.ru,u(t,n.eu),t.beginPath();for(var p=0,v=n.uu;p<v.length;p++){var d=v[p],y=Math.round(d.su*i);t.moveTo(-r,y),t.lineTo(h+r,y)}t.stroke()}})}},t}(),zi=function(){function t(t){this.zt=new Bi,this.ft=!0,this.Di=t}return t.prototype.vt=function(){this.ft=!0},t.prototype.dt=function(t,i){if(this.ft){var n=this.Di.jt().W().grid,e={Ot:t,Ft:i,hu:n.horzLines.visible,Qe:n.vertLines.visible,ru:n.horzLines.color,tu:n.vertLines.color,eu:n.horzLines.style,iu:n.vertLines.style,uu:this.Di.ji().au(),nu:this.Di.jt().bt().au()||[]};this.zt.Z(e),this.ft=!1}return this.zt},t}(),Ti=function(){function t(t){this.Hi=new zi(t)}return t.prototype.ou=function(){return this.Hi},t}(),ki={lu:4,fu:1e-4};function Di(t,i){var n=100*(t-i)/i;return i<0?-n:n}function Ei(t,i){var n=Di(t.pr(),i),e=Di(t.gr(),i);return new vi(n,e)}function Pi(t,i){var n=100*(t-i)/i+100;return i<0?-n:n}function Li(t,i){var n=Pi(t.pr(),i),e=Pi(t.gr(),i);return new vi(n,e)}function Wi(t,i){var n=Math.abs(t);if(n<1e-15)return 0;var e=Dt(n+i.fu)+i.lu;return t<0?-e:e}function Ni(t,i){var n=Math.abs(t);if(n<1e-15)return 0;var e=Math.pow(10,n-i.lu)-i.fu;return t<0?-e:e}function Ri(t,i){if(null===t)return null;var n=Wi(t.pr(),i),e=Wi(t.gr(),i);return new vi(n,e)}function Oi(t,i){if(null===t)return null;var n=Ni(t.pr(),i),e=Ni(t.gr(),i);return new vi(n,e)}function Ai(t){if(null===t)return ki;var i=Math.abs(t.gr()-t.pr());if(i>=1||i<1e-15)return ki;var n=Math.ceil(Math.abs(Math.log10(i))),e=ki.lu+n;return{lu:e,fu:1/Math.pow(10,e)}}var Gi,Vi=function(){function t(t,i){if(this.cu=t,this.vu=i,function(t){if(t<0)return!1;for(var i=t;i>1;i/=10)if(i%10!=0)return!1;return!0}(this.cu))this._u=[2,2.5,2];else{this._u=[];for(var n=this.cu;1!==n;){if(n%2==0)this._u.push(2),n/=2;else{if(n%5!=0)throw new Error(\"unexpected base\");this._u.push(2,2.5),n/=5}if(this._u.length>100)throw new Error(\"something wrong with base\")}}}return t.prototype.du=function(t,i,n){for(var e,s=0===this.cu?0:1/this.cu,r=Math.pow(10,Math.max(0,Math.ceil(Dt(t-i)))),o=0,h=this.vu[0];;){var u=kt(r,s,1e-14)&&r>s+1e-14,a=kt(r,n*h,1e-14),l=kt(r,1,1e-14);if(!(u&&a&&l))break;r/=h,h=this.vu[++o%this.vu.length]}if(r<=s+1e-14&&(r=s),r=Math.max(1,r),this._u.length>0&&(e=r,1,1e-14,Math.abs(e-1)<1e-14))for(o=0,h=this._u[0];kt(r,n*h,1e-14)&&r>s+1e-14;)r/=h,h=this._u[++o%this._u.length];return r},t}(),Fi=function(){function t(t,i,n,e){this.wu=[],this._i=t,this.cu=i,this.Mu=n,this.bu=e}return t.prototype.du=function(t,i){if(t<i)throw new Error(\"high < low\");var n=this._i.Yt(),e=(t-i)*this.mu()/n,s=new Vi(this.cu,[2,2.5,2]),r=new Vi(this.cu,[2,2,2.5]),o=new Vi(this.cu,[2.5,2,2]),h=[];return h.push(s.du(t,i,e),r.du(t,i,e),o.du(t,i,e)),function(t){if(t.length<1)throw Error(\"array is empty\");for(var i=t[0],n=1;n<t.length;++n)t[n]<i&&(i=t[n]);return i}(h)},t.prototype.pu=function(){var t=this._i,i=t.kt();if(null!==i){var n=t.Yt(),e=this.Mu(n-1,i),s=this.Mu(0,i),r=this._i.W().entireTextOnly?this.gu()/2:0,o=r,h=n-1-r,u=Math.max(e,s),a=Math.min(e,s);if(u!==a){for(var l=this.du(u,a),c=u%l,f=u>=a?1:-1,p=null,v=0,d=u-(c+=c<0?l:0);d>a;d-=l){var y=this.bu(d,i,!0);null!==p&&Math.abs(y-p)<this.mu()||y<o||y>h||(v<this.wu.length?(this.wu[v].su=y,this.wu[v].yu=t.ku(d)):this.wu.push({su:y,yu:t.ku(d)}),v++,p=y,t.Nu()&&(l=this.du(d*f,a)))}this.wu.length=v}else this.wu=[]}else this.wu=[]},t.prototype.au=function(){return this.wu},t.prototype.gu=function(){return this._i.S()},t.prototype.mu=function(){return Math.ceil(2.5*this.gu())},t}();function ji(t){return t.slice().sort(function(t,i){return f(t.xi())-f(i.xi())})}!function(t){t[t.Normal=0]=\"Normal\",t[t.Logarithmic=1]=\"Logarithmic\",t[t.Percentage=2]=\"Percentage\",t[t.IndexedTo100=3]=\"IndexedTo100\"}(Gi||(Gi={}));var Xi=new lt,Yi=new at(100,1),Ji=function(){function t(t,i,n,e){this.xu=0,this.Cu=null,this.Tr=null,this.Su=null,this.Tu={Du:!1,Au:null},this.Bu=0,this.Lu=0,this.Eu=new z,this.Fu=new z,this.Ou=[],this.Vu=null,this.Pu=null,this.Wu=null,this.zu=null,this.Ue=Yi,this.Ru=Ai(null),this.Iu=t,this.zi=i,this.ju=n,this.qu=e,this.Uu=new Fi(this,100,this.Hu.bind(this),this.Yu.bind(this))}return t.prototype.Ke=function(){return this.Iu},t.prototype.W=function(){return this.zi},t.prototype.Pr=function(t){if(T(this.zi,t),this.Xe(),void 0!==t.mode&&this.$u({mh:t.mode}),void 0!==t.scaleMargins){var i=c(t.scaleMargins.top),n=c(t.scaleMargins.bottom);if(i<0||i>1)throw new Error(\"Invalid top margin - expect value between 0 and 1, given=\".concat(i));if(n<0||n>1||i+n>1)throw new Error(\"Invalid bottom margin - expect value between 0 and 1, given=\".concat(n));if(i+n>1)throw new Error(\"Invalid margins - sum of margins must be less than 1, given=\".concat(i+n));this.Ku(),this.Pu=null}},t.prototype.Xu=function(){return this.zi.autoScale},t.prototype.Nu=function(){return 1===this.zi.mode},t.prototype.vr=function(){return 2===this.zi.mode},t.prototype.Zu=function(){return 3===this.zi.mode},t.prototype.mh=function(){return{_n:this.zi.autoScale,Ju:this.zi.invertScale,mh:this.zi.mode}},t.prototype.$u=function(t){var i=this.mh(),n=null;void 0!==t._n&&(this.zi.autoScale=t._n),void 0!==t.mh&&(this.zi.mode=t.mh,2!==t.mh&&3!==t.mh||(this.zi.autoScale=!0),this.Tu.Du=!1),1===i.mh&&t.mh!==i.mh&&(function(t,i){if(null===t)return!1;var n=Ni(t.pr(),i),e=Ni(t.gr(),i);return isFinite(n)&&isFinite(e)}(this.Tr,this.Ru)?null!==(n=Oi(this.Tr,this.Ru))&&this.Gu(n):this.zi.autoScale=!0),1===t.mh&&t.mh!==i.mh&&null!==(n=Ri(this.Tr,this.Ru))&&this.Gu(n);var e=i.mh!==this.zi.mode;e&&(2===i.mh||this.vr())&&this.Xe(),e&&(3===i.mh||this.Zu())&&this.Xe(),void 0!==t.Ju&&i.Ju!==t.Ju&&(this.zi.invertScale=t.Ju,this.Qu()),this.Fu.m(i,this.mh())},t.prototype.ta=function(){return this.Fu},t.prototype.S=function(){return this.ju.fontSize},t.prototype.Yt=function(){return this.xu},t.prototype.ia=function(t){this.xu!==t&&(this.xu=t,this.Ku(),this.Pu=null)},t.prototype.na=function(){if(this.Cu)return this.Cu;var t=this.Yt()-this.sa()-this.ha();return this.Cu=t,t},t.prototype.Ar=function(){return this.ra(),this.Tr},t.prototype.Gu=function(t,i){var n=this.Tr;(i||null===n&&null!==t||null!==n&&!n.br(t))&&(this.Pu=null,this.Tr=t)},t.prototype.wi=function(){return this.ra(),0===this.xu||!this.Tr||this.Tr.wi()},t.prototype.ea=function(t){return this.Ju()?t:this.Yt()-1-t},t.prototype.xt=function(t,i){return this.vr()?t=Di(t,i):this.Zu()&&(t=Pi(t,i)),this.Yu(t,i)},t.prototype.us=function(t,i,n){this.ra();for(var e=this.ha(),s=f(this.Ar()),r=s.pr(),o=s.gr(),h=this.na()-1,u=this.Ju(),a=h/(o-r),l=void 0===n?0:n.from,c=void 0===n?t.length:n.to,p=this.ua(),v=l;v<c;v++){var d=t[v],y=d.et;if(!isNaN(y)){var w=y;null!==p&&(w=p(d.et,i));var g=e+a*(w-r),m=u?g:this.xu-1-g;d.it=m}}},t.prototype.Ss=function(t,i,n){this.ra();for(var e=this.ha(),s=f(this.Ar()),r=s.pr(),o=s.gr(),h=this.na()-1,u=this.Ju(),a=h/(o-r),l=void 0===n?0:n.from,c=void 0===n?t.length:n.to,p=this.ua(),v=l;v<c;v++){var d=t[v],y=d.open,w=d.high,g=d.low,m=d.close;null!==p&&(y=p(d.open,i),w=p(d.high,i),g=p(d.low,i),m=p(d.close,i));var M=e+a*(y-r),_=u?M:this.xu-1-M;d.xs=_,M=e+a*(w-r),_=u?M:this.xu-1-M,d.ys=_,M=e+a*(g-r),_=u?M:this.xu-1-M,d.ks=_,M=e+a*(m-r),_=u?M:this.xu-1-M,d.Cs=_}},t.prototype.qi=function(t,i){var n=this.Hu(t,i);return this.aa(n,i)},t.prototype.aa=function(t,i){var n=t;return this.vr()?n=function(t,i){return i<0&&(t=-t),t/100*i+i}(n,i):this.Zu()&&(n=function(t,i){return t-=100,i<0&&(t=-t),t/100*i+i}(n,i)),n},t.prototype.Ge=function(){return this.Ou},t.prototype.oa=function(){if(this.Vu)return this.Vu;for(var t=[],i=0;i<this.Ou.length;i++){var n=this.Ou[i];null===n.xi()&&n.Ci(i+1),t.push(n)}return t=ji(t),this.Vu=t,this.Vu},t.prototype.la=function(t){-1===this.Ou.indexOf(t)&&(this.Ou.push(t),this.Xe(),this.fa())},t.prototype.ca=function(t){var i=this.Ou.indexOf(t);if(-1===i)throw new Error(\"source is not attached to scale\");this.Ou.splice(i,1),0===this.Ou.length&&(this.$u({_n:!0}),this.Gu(null)),this.Xe(),this.fa()},t.prototype.kt=function(){for(var t=null,i=0,n=this.Ou;i<n.length;i++){var e=n[i].kt();null!==e&&(null===t||e.Ve<t.Ve)&&(t=e)}return null===t?null:t.St},t.prototype.Ju=function(){return this.zi.invertScale},t.prototype.au=function(){var t=null===this.kt();if(null!==this.Pu&&(t||this.Pu.va===t))return this.Pu.au;this.Uu.pu();var i=this.Uu.au();return this.Pu={au:i,va:t},this.Eu.m(),i},t.prototype._a=function(){return this.Eu},t.prototype.da=function(t){this.vr()||this.Zu()||null===this.Wu&&null===this.Su&&(this.wi()||(this.Wu=this.xu-t,this.Su=f(this.Ar()).mr()))},t.prototype.wa=function(t){if(!this.vr()&&!this.Zu()&&null!==this.Wu){this.$u({_n:!1}),(t=this.xu-t)<0&&(t=0);var i=(this.Wu+.2*(this.xu-1))/(t+.2*(this.xu-1)),n=f(this.Su).mr();i=Math.max(i,.1),n.kr(i),this.Gu(n)}},t.prototype.Ma=function(){this.vr()||this.Zu()||(this.Wu=null,this.Su=null)},t.prototype.ba=function(t){this.Xu()||null===this.zu&&null===this.Su&&(this.wi()||(this.zu=t,this.Su=f(this.Ar()).mr()))},t.prototype.ma=function(t){if(!this.Xu()&&null!==this.zu){var i=f(this.Ar()).yr()/(this.na()-1),n=t-this.zu;this.Ju()&&(n*=-1);var e=n*i,s=f(this.Su).mr();s.Nr(e),this.Gu(s,!0),this.Pu=null}},t.prototype.pa=function(){this.Xu()||null!==this.zu&&(this.zu=null,this.Su=null)},t.prototype.qe=function(){return this.Ue||this.Xe(),this.Ue},t.prototype.Mi=function(t,i){switch(this.zi.mode){case 2:return this.qe().format(Di(t,i));case 3:return this.qe().format(Pi(t,i));default:return this.ga(t)}},t.prototype.ku=function(t){switch(this.zi.mode){case 2:case 3:return this.qe().format(t);default:return this.ga(t)}},t.prototype.Fr=function(t){return this.ga(t,f(this.ya()).qe())},t.prototype.Ne=function(t,i){return t=Di(t,i),Xi.format(t)},t.prototype.ka=function(){return this.Ou},t.prototype.Na=function(t){this.Tu={Au:t,Du:!1}},t.prototype.hn=function(){this.Ou.forEach(function(t){return t.hn()})},t.prototype.Xe=function(){this.Pu=null;var t=this.ya(),i=100;null!==t&&(i=Math.round(1/t.je())),this.Ue=Yi,this.vr()?(this.Ue=Xi,i=100):this.Zu()?(this.Ue=new at(100,1),i=100):null!==t&&(this.Ue=t.qe()),this.Uu=new Fi(this,i,this.Hu.bind(this),this.Yu.bind(this)),this.Uu.pu()},t.prototype.fa=function(){this.Vu=null},t.prototype.ya=function(){return this.Ou[0]||null},t.prototype.sa=function(){return this.Ju()?this.zi.scaleMargins.bottom*this.Yt()+this.Lu:this.zi.scaleMargins.top*this.Yt()+this.Bu},t.prototype.ha=function(){return this.Ju()?this.zi.scaleMargins.top*this.Yt()+this.Bu:this.zi.scaleMargins.bottom*this.Yt()+this.Lu},t.prototype.ra=function(){this.Tu.Du||(this.Tu.Du=!0,this.xa())},t.prototype.Ku=function(){this.Cu=null},t.prototype.Yu=function(t,i){if(this.ra(),this.wi())return 0;t=this.Nu()&&t?Wi(t,this.Ru):t;var n=f(this.Ar()),e=this.ha()+(this.na()-1)*(t-n.pr())/n.yr();return this.ea(e)},t.prototype.Hu=function(t,i){if(this.ra(),this.wi())return 0;var n=this.ea(t),e=f(this.Ar()),s=e.pr()+e.yr()*((n-this.ha())/(this.na()-1));return this.Nu()?Ni(s,this.Ru):s},t.prototype.Qu=function(){this.Pu=null,this.Uu.pu()},t.prototype.xa=function(){var t=this.Tu.Au;if(null!==t){for(var i,n,e=null,s=0,r=0,o=0,h=this.ka();o<h.length;o++){var u=h[o];if(u.yt()){var a=u.kt();if(null!==a){var l=u.Re(t.In(),t.jn()),c=l&&l.Ar();if(null!==c){switch(this.zi.mode){case 1:c=Ri(c,this.Ru);break;case 2:c=Ei(c,a.St);break;case 3:c=Li(c,a.St)}if(e=null===e?c:e.Nn(f(c)),null!==l){var p=l.Br();null!==p&&(s=Math.max(s,p.above),r=Math.max(s,p.below))}}}}}if(s===this.Bu&&r===this.Lu||(this.Bu=s,this.Lu=r,this.Pu=null,this.Ku()),null!==e){if(e.pr()===e.gr()){var v=this.ya(),d=5*(null===v||this.vr()||this.Zu()?1:v.je());this.Nu()&&(e=Oi(e,this.Ru)),e=new vi(e.pr()-d,e.gr()+d),this.Nu()&&(e=Ri(e,this.Ru))}if(this.Nu()){var y=Oi(e,this.Ru),w=Ai(y);if(i=w,n=this.Ru,i.lu!==n.lu||i.fu!==n.fu){var g=null!==this.Su?Oi(this.Su,this.Ru):null;this.Ru=w,e=Ri(y,w),null!==g&&(this.Su=Ri(g,w))}}this.Gu(e)}else null===this.Tr&&(this.Gu(new vi(-.5,.5)),this.Ru=Ai(null));this.Tu.Du=!0}},t.prototype.ua=function(){var t=this;return this.vr()?Di:this.Zu()?Pi:this.Nu()?function(i){return Wi(i,t.Ru)}:null},t.prototype.ga=function(t,i){return void 0===this.qu.priceFormatter?(void 0===i&&(i=this.qe()),i.format(t)):this.qu.priceFormatter(t)},t}(),Ii=function(){function t(t,i){this.Ou=[],this.Ca=new Map,this.xu=0,this.hh=0,this.Sa=1e3,this.Vu=null,this.Ta=new z,this.Da=t,this.pi=i,this.Aa=new Ti(this);var n=i.W();this.Ba=this.La(\"left\",n.leftPriceScale),this.Ea=this.La(\"right\",n.rightPriceScale),this.Ba.ta().u(this.Fa.bind(this,this.Ba),this),this.Ea.ta().u(this.Fa.bind(this,this.Ba),this),this.Oa(n)}return t.prototype.Oa=function(t){if(t.leftPriceScale&&this.Ba.Pr(t.leftPriceScale),t.rightPriceScale&&this.Ea.Pr(t.rightPriceScale),t.localization&&(this.Ba.Xe(),this.Ea.Xe()),t.overlayPriceScales)for(var i=0,n=Array.from(this.Ca.values());i<n.length;i++){var e=f(n[i][0].Ct());e.Pr(t.overlayPriceScales),t.localization&&e.Xe()}},t.prototype.Va=function(t){switch(t){case\"left\":return this.Ba;case\"right\":return this.Ea}return this.Ca.has(t)?c(this.Ca.get(t))[0].Ct():null},t.prototype.g=function(){this.jt().Pa().M(this),this.Ba.ta().M(this),this.Ea.ta().M(this),this.Ou.forEach(function(t){t.g&&t.g()}),this.Ta.m()},t.prototype.Wa=function(){return this.Sa},t.prototype.za=function(t){this.Sa=t},t.prototype.jt=function(){return this.pi},t.prototype.Ht=function(){return this.hh},t.prototype.Yt=function(){return this.xu},t.prototype.Ra=function(t){this.hh=t,this.Ia()},t.prototype.ia=function(t){var i=this;this.xu=t,this.Ba.ia(t),this.Ea.ia(t),this.Ou.forEach(function(n){if(i.lh(n)){var e=n.Ct();null!==e&&e.ia(t)}}),this.Ia()},t.prototype.Ge=function(){return this.Ou},t.prototype.lh=function(t){var i=t.Ct();return null===i||this.Ba!==i&&this.Ea!==i},t.prototype.la=function(t,i,n){var e=void 0!==n?n:this.qa().ja+1;this.Ua(t,i,e)},t.prototype.ca=function(t){var i=this.Ou.indexOf(t);l(-1!==i,\"removeDataSource: invalid data source\"),this.Ou.splice(i,1);var n=f(t.Ct()).Ke();if(this.Ca.has(n)){var e=c(this.Ca.get(n)),s=e.indexOf(t);-1!==s&&(e.splice(s,1),0===e.length&&this.Ca.delete(n))}var r=t.Ct();r&&r.Ge().indexOf(t)>=0&&r.ca(t),null!==r&&(r.fa(),this.Ha(r)),this.Vu=null},t.prototype._h=function(t){return t===this.Ba?\"left\":t===this.Ea?\"right\":\"overlay\"},t.prototype.Ya=function(){return this.Ba},t.prototype.$a=function(){return this.Ea},t.prototype.Ka=function(t,i){t.da(i)},t.prototype.Xa=function(t,i){t.wa(i),this.Ia()},t.prototype.Za=function(t){t.Ma()},t.prototype.Ja=function(t,i){t.ba(i)},t.prototype.Ga=function(t,i){t.ma(i),this.Ia()},t.prototype.Qa=function(t){t.pa()},t.prototype.Ia=function(){this.Ou.forEach(function(t){t.hn()})},t.prototype.ji=function(){var t=null;return this.pi.W().rightPriceScale.visible&&0!==this.Ea.Ge().length?t=this.Ea:this.pi.W().leftPriceScale.visible&&0!==this.Ba.Ge().length?t=this.Ba:0!==this.Ou.length&&(t=this.Ou[0].Ct()),null===t&&(t=this.Ea),t},t.prototype.fh=function(){var t=null;return this.pi.W().rightPriceScale.visible?t=this.Ea:this.pi.W().leftPriceScale.visible&&(t=this.Ba),t},t.prototype.Ha=function(t){null!==t&&t.Xu()&&this.io(t)},t.prototype.no=function(t){var i=this.Da.ss();t.$u({_n:!0}),null!==i&&t.Na(i),this.Ia()},t.prototype.so=function(){this.io(this.Ba),this.io(this.Ea)},t.prototype.ho=function(){var t=this;this.Ha(this.Ba),this.Ha(this.Ea),this.Ou.forEach(function(i){t.lh(i)&&t.Ha(i.Ct())}),this.Ia(),this.pi.Wr()},t.prototype.oa=function(){return null===this.Vu&&(this.Vu=ji(this.Ou)),this.Vu},t.prototype.ro=function(){return this.Ta},t.prototype.eo=function(){return this.Aa},t.prototype.io=function(t){var i=t.ka();if(i&&i.length>0&&!this.Da.wi()){var n=this.Da.ss();null!==n&&t.Na(n)}t.hn()},t.prototype.qa=function(){var t=this.oa();if(0===t.length)return{uo:0,ja:0};for(var i=0,n=0,e=0;e<t.length;e++){var s=t[e].xi();null!==s&&(s<i&&(i=s),s>n&&(n=s))}return{uo:i,ja:n}},t.prototype.Ua=function(t,i,n){var e=this.Va(i);if(null===e&&(e=this.La(i,this.pi.W().overlayPriceScales)),this.Ou.push(t),!ot(i)){var s=this.Ca.get(i)||[];s.push(t),this.Ca.set(i,s)}e.la(t),t.Si(e),t.Ci(n),this.Ha(e),this.Vu=null},t.prototype.Fa=function(t,i,n){i.mh!==n.mh&&this.io(t)},t.prototype.La=function(t,i){var n=x({visible:!0,autoScale:!0},L(i)),e=new Ji(t,n,this.pi.W().layout,this.pi.W().localization);return e.ia(this.Yt()),e},t}(),Zi=function(t){return t.getUTCFullYear()};var Ki=function(){function t(t,i){void 0===t&&(t=\"yyyy-MM-dd\"),void 0===i&&(i=\"default\"),this.ao=t,this.oo=i}return t.prototype.lo=function(t){return function(t,i,n){return i.replace(/yyyy/g,function(t){return ut(Zi(t),4)}(t)).replace(/yy/g,function(t){return ut(Zi(t)%100,2)}(t)).replace(/MMMM/g,function(t,i){return new Date(t.getUTCFullYear(),t.getUTCMonth(),1).toLocaleString(i,{month:\"long\"})}(t,n)).replace(/MMM/g,function(t,i){return new Date(t.getUTCFullYear(),t.getUTCMonth(),1).toLocaleString(i,{month:\"short\"})}(t,n)).replace(/MM/g,function(t){return ut(function(t){return t.getUTCMonth()+1}(t),2)}(t)).replace(/dd/g,function(t){return ut(function(t){return t.getUTCDate()}(t),2)}(t))}(t,this.ao,this.oo)},t}(),Hi=function(){function t(t){this.fo=t||\"%h:%m:%s\"}return t.prototype.lo=function(t){return this.fo.replace(\"%h\",ut(t.getUTCHours(),2)).replace(\"%m\",ut(t.getUTCMinutes(),2)).replace(\"%s\",ut(t.getUTCSeconds(),2))},t}(),Ui={co:\"yyyy-MM-dd\",vo:\"%h:%m:%s\",_o:\" \",do:\"default\"},Qi=function(){function t(t){void 0===t&&(t={});var i=x(x({},Ui),t);this.wo=new Ki(i.co,i.do),this.Mo=new Hi(i.vo),this.bo=i._o}return t.prototype.lo=function(t){return\"\".concat(this.wo.lo(t)).concat(this.bo).concat(this.Mo.lo(t))},t}(),$i=function(){function t(t,i){void 0===i&&(i=50),this.mo=0,this.po=1,this.yo=1,this.Gs=new Map,this.ko=new Map,this.No=t,this.xo=i}return t.prototype.lo=function(t){var i=void 0===t.Co?new Date(1e3*t.So).getTime():new Date(Date.UTC(t.Co.year,t.Co.month-1,t.Co.day)).getTime(),n=this.Gs.get(i);if(void 0!==n)return n.To;if(this.mo===this.xo){var e=this.ko.get(this.yo);this.ko.delete(this.yo),this.Gs.delete(c(e)),this.yo++,this.mo--}var s=this.No(t);return this.Gs.set(i,{To:s,Do:this.po}),this.ko.set(this.po,i),this.mo++,this.po++,s},t}(),qi=function(){function t(t,i){l(t<=i,\"right should be >= left\"),this.Ao=t,this.Bo=i}return t.prototype.In=function(){return this.Ao},t.prototype.jn=function(){return this.Bo},t.prototype.Lo=function(){return this.Bo-this.Ao+1},t.prototype.Uh=function(t){return this.Ao<=t&&t<=this.Bo},t.prototype.br=function(t){return this.Ao===t.In()&&this.Bo===t.jn()},t}();function tn(t,i){return null===t||null===i?t===i:t.br(i)}var nn,en=function(){function t(){this.Eo=new Map,this.Gs=null}return t.prototype.Fo=function(t,i){this.Oo(i),this.Gs=null;for(var n=i;n<t.length;++n){var e=t[n],s=this.Eo.get(e.Vo);void 0===s&&(s=[],this.Eo.set(e.Vo,s)),s.push({vs:n,rt:e.rt,Po:e.Vo})}},t.prototype.Wo=function(t,i){var n=Math.ceil(i/t);return null!==this.Gs&&this.Gs.zo===n||(this.Gs={au:this.Ro(n),zo:n}),this.Gs.au},t.prototype.Oo=function(t){if(0!==t){var i=[];this.Eo.forEach(function(n,e){t<=n[0].vs?i.push(e):n.splice(wt(n,t,function(i){return i.vs<t}),1/0)});for(var n=0,e=i;n<e.length;n++){var s=e[n];this.Eo.delete(s)}}else this.Eo.clear()},t.prototype.Ro=function(t){for(var i=[],n=0,e=Array.from(this.Eo.keys()).sort(function(t,i){return i-t});n<e.length;n++){var s=e[n];if(this.Eo.get(s)){var r=i;i=[];for(var o=r.length,h=0,u=c(this.Eo.get(s)),a=u.length,l=1/0,f=-1/0,p=0;p<a;p++){for(var v=u[p],d=v.vs;h<o;){var y=r[h],w=y.vs;if(!(w<d)){l=w;break}h++,i.push(y),f=w,l=1/0}l-d>=t&&d-f>=t&&(i.push(v),f=d)}for(;h<o;h++)i.push(r[h])}}return i},t}(),sn=function(){function t(t){this.Io=t}return t.prototype.jo=function(){return null===this.Io?null:new qi(Math.floor(this.Io.In()),Math.ceil(this.Io.jn()))},t.prototype.qo=function(){return this.Io},t.Uo=function(){return new t(null)},t}();!function(t){t[t.Year=0]=\"Year\",t[t.Month=1]=\"Month\",t[t.DayOfMonth=2]=\"DayOfMonth\",t[t.Time=3]=\"Time\",t[t.TimeWithSeconds=4]=\"TimeWithSeconds\"}(nn||(nn={}));var rn,on=function(){function t(t,i,n){this.hh=0,this.Ho=null,this.Yo=[],this.zu=null,this.Wu=null,this.$o=new en,this.Ko=new Map,this.Xo=sn.Uo(),this.Zo=!0,this.Jo=new z,this.Go=new z,this.Qo=new z,this.tl=null,this.il=null,this.nl=[],this.zi=i,this.qu=n,this.sl=i.rightOffset,this.hl=i.barSpacing,this.pi=t,this.rl()}return t.prototype.W=function(){return this.zi},t.prototype.el=function(t){T(this.qu,t),this.ul(),this.rl()},t.prototype.Pr=function(t,i){var n;T(this.zi,t),this.zi.fixLeftEdge&&this.al(),this.zi.fixRightEdge&&this.ol(),void 0!==t.barSpacing&&this.pi.gn(t.barSpacing),void 0!==t.rightOffset&&this.pi.yn(t.rightOffset),void 0!==t.minBarSpacing&&this.pi.gn(null!==(n=t.barSpacing)&&void 0!==n?n:this.hl),this.ul(),this.rl(),this.Qo.m()},t.prototype.gi=function(t){var i;return(null===(i=this.Yo[t])||void 0===i?void 0:i.rt)||null},t.prototype.Ze=function(t,i){if(this.Yo.length<1)return null;if(t.So>this.Yo[this.Yo.length-1].rt.So)return i?this.Yo.length-1:null;var n=wt(this.Yo,t.So,function(t,i){return t.rt.So<i});return t.So<this.Yo[n].rt.So?i?n:null:n},t.prototype.wi=function(){return 0===this.hh||0===this.Yo.length||null===this.Ho},t.prototype.ss=function(){return this.ll(),this.Xo.jo()},t.prototype.fl=function(){return this.ll(),this.Xo.qo()},t.prototype.cl=function(){var t=this.ss();if(null===t)return null;var i={from:t.In(),to:t.jn()};return this.vl(i)},t.prototype.vl=function(t){var i=Math.round(t.from),n=Math.round(t.to),e=f(this._l()),s=f(this.dl());return{from:f(this.gi(Math.max(e,i))),to:f(this.gi(Math.min(s,n)))}},t.prototype.wl=function(t){return{from:f(this.Ze(t.from,!0)),to:f(this.Ze(t.to,!0))}},t.prototype.Ht=function(){return this.hh},t.prototype.Ra=function(t){if(isFinite(t)&&!(t<=0)&&this.hh!==t){if(this.zi.lockVisibleTimeRangeOnResize&&this.hh){var i=this.hl*t/this.hh;this.hl=i}if(this.zi.fixLeftEdge){var n=this.ss();if(null!==n&&n.In()<=0){var e=this.hh-t;this.sl-=Math.round(e/this.hl)+1}}this.hh=t,this.Zo=!0,this.Ml(),this.bl()}},t.prototype.At=function(t){if(this.wi()||!D(t))return 0;var i=this.ml()+this.sl-t;return this.hh-(i+.5)*this.hl-1},t.prototype.es=function(t,i){for(var n=this.ml(),e=void 0===i?0:i.from,s=void 0===i?t.length:i.to,r=e;r<s;r++){var o=t[r].rt,h=n+this.sl-o,u=this.hh-(h+.5)*this.hl-1;t[r].tt=u}},t.prototype.pl=function(t){return Math.ceil(this.gl(t))},t.prototype.yn=function(t){this.Zo=!0,this.sl=t,this.bl(),this.pi.yl(),this.pi.Wr()},t.prototype.ws=function(){return this.hl},t.prototype.gn=function(t){this.kl(t),this.bl(),this.pi.yl(),this.pi.Wr()},t.prototype.Nl=function(){return this.sl},t.prototype.au=function(){if(this.wi())return null;if(null!==this.il)return this.il;for(var t=this.hl,i=5*(this.pi.W().layout.fontSize+4),n=Math.round(i/t),e=f(this.ss()),s=Math.max(e.In(),e.In()-n),r=Math.max(e.jn(),e.jn()-n),o=this.$o.Wo(t,i),h=this._l()+n,u=this.dl()-n,a=this.xl(),l=this.zi.fixLeftEdge||a,c=this.zi.fixRightEdge||a,p=0,v=0,d=o;v<d.length;v++){var y=d[v];if(s<=y.vs&&y.vs<=r){var w=void 0;p<this.nl.length?((w=this.nl[p]).su=this.At(y.vs),w.yu=this.Cl(y.rt,y.Po),w.Po=y.Po):(w={Sl:!1,su:this.At(y.vs),yu:this.Cl(y.rt,y.Po),Po:y.Po},this.nl.push(w)),this.hl>i/2&&!a?w.Sl=!1:w.Sl=l&&y.vs<=h||c&&y.vs>=u,p++}}return this.nl.length=p,this.il=this.nl,this.nl},t.prototype.Tl=function(){this.Zo=!0,this.gn(this.zi.barSpacing),this.yn(this.zi.rightOffset)},t.prototype.Dl=function(t){this.Zo=!0,this.Ho=t,this.bl(),this.al()},t.prototype.Al=function(t,i){var n=this.gl(t),e=this.ws(),s=e+i*(e/10);this.gn(s),this.zi.rightBarStaysOnScroll||this.yn(this.Nl()+(n-this.gl(t)))},t.prototype.da=function(t){this.zu&&this.pa(),null===this.Wu&&null===this.tl&&(this.wi()||(this.Wu=t,this.Bl()))},t.prototype.wa=function(t){if(null!==this.tl){var i=Tt(this.hh-t,0,this.hh),n=Tt(this.hh-f(this.Wu),0,this.hh);0!==i&&0!==n&&this.gn(this.tl.ws*i/n)}},t.prototype.Ma=function(){null!==this.Wu&&(this.Wu=null,this.Ll())},t.prototype.ba=function(t){null===this.zu&&null===this.tl&&(this.wi()||(this.zu=t,this.Bl()))},t.prototype.ma=function(t){if(null!==this.zu){var i=(this.zu-t)/this.ws();this.sl=f(this.tl).Nl+i,this.Zo=!0,this.bl()}},t.prototype.pa=function(){null!==this.zu&&(this.zu=null,this.Ll())},t.prototype.El=function(){this.Fl(this.zi.rightOffset)},t.prototype.Fl=function(t,i){var n=this;if(void 0===i&&(i=400),!isFinite(t))throw new RangeError(\"offset is required and must be finite number\");if(!isFinite(i)||i<=0)throw new RangeError(\"animationDuration (optional) must be finite positive number\");var e=this.sl,s=performance.now(),r=function(){var o=(performance.now()-s)/i,h=o>=1,u=h?t:e+(t-e)*o;n.yn(u),h||setTimeout(r,20)};r()},t.prototype.vt=function(t,i){this.Zo=!0,this.Yo=t,this.$o.Fo(t,i),this.bl()},t.prototype.Ol=function(){return this.Jo},t.prototype.Vl=function(){return this.Go},t.prototype.Pl=function(){return this.Qo},t.prototype.ml=function(){return this.Ho||0},t.prototype.Wl=function(t){var i=t.Lo();this.kl(this.hh/i),this.sl=t.jn()-this.ml(),this.bl(),this.Zo=!0,this.pi.yl(),this.pi.Wr()},t.prototype.zl=function(){var t=this._l(),i=this.dl();null!==t&&null!==i&&this.Wl(new qi(t,i+this.zi.rightOffset))},t.prototype.Rl=function(t){var i=new qi(t.from,t.to);this.Wl(i)},t.prototype.yi=function(t){return void 0!==this.qu.timeFormatter?this.qu.timeFormatter(t.Co||t.So):this.Il.lo(new Date(1e3*t.So))},t.prototype.xl=function(){var t=this.pi.W(),i=t.handleScroll,n=t.handleScale;return!(i.horzTouchDrag||i.mouseWheel||i.pressedMouseMove||i.vertTouchDrag||n.axisDoubleClickReset||n.axisPressedMouseMove.time||n.mouseWheel||n.pinch)},t.prototype._l=function(){return 0===this.Yo.length?null:0},t.prototype.dl=function(){return 0===this.Yo.length?null:this.Yo.length-1},t.prototype.jl=function(t){return(this.hh-1-t)/this.hl},t.prototype.gl=function(t){var i=this.jl(t),n=this.ml()+this.sl-i;return Math.round(1e6*n)/1e6},t.prototype.kl=function(t){var i=this.hl;this.hl=t,this.Ml(),i!==this.hl&&(this.Zo=!0,this.ql())},t.prototype.ll=function(){if(this.Zo)if(this.Zo=!1,this.wi())this.Ul(sn.Uo());else{var t=this.ml(),i=this.hh/this.hl,n=this.sl+t,e=new qi(n-i+1,n);this.Ul(new sn(e))}},t.prototype.Ml=function(){var t=this.Hl();if(this.hl<t&&(this.hl=t,this.Zo=!0),0!==this.hh){var i=.5*this.hh;this.hl>i&&(this.hl=i,this.Zo=!0)}},t.prototype.Hl=function(){return this.zi.fixLeftEdge&&this.zi.fixRightEdge&&0!==this.Yo.length?this.hh/this.Yo.length:this.zi.minBarSpacing},t.prototype.bl=function(){var t=this.Yl();this.sl>t&&(this.sl=t,this.Zo=!0);var i=this.$l();null!==i&&this.sl<i&&(this.sl=i,this.Zo=!0)},t.prototype.$l=function(){var t=this._l(),i=this.Ho;return null===t||null===i?null:t-i-1+(this.zi.fixLeftEdge?this.hh/this.hl:Math.min(2,this.Yo.length))},t.prototype.Yl=function(){return this.zi.fixRightEdge?0:this.hh/this.hl-Math.min(2,this.Yo.length)},t.prototype.Bl=function(){this.tl={ws:this.ws(),Nl:this.Nl()}},t.prototype.Ll=function(){this.tl=null},t.prototype.Cl=function(t,i){var n=this,e=this.Ko.get(i);return void 0===e&&(e=new $i(function(t){return n.Kl(t,i)}),this.Ko.set(i,e)),e.lo(t)},t.prototype.Kl=function(t,i){var n,e=function(t,i,n){switch(t){case 0:case 10:return i?n?4:3:2;case 20:case 21:case 22:case 30:case 31:case 32:case 33:return i?3:2;case 50:return 2;case 60:return 1;case 70:return 0}}(i,this.zi.timeVisible,this.zi.secondsVisible);return void 0!==this.zi.tickMarkFormatter?this.zi.tickMarkFormatter(null!==(n=t.Co)&&void 0!==n?n:t.So,e,this.qu.locale):function(t,i,n){var e={};switch(i){case 0:e.year=\"numeric\";break;case 1:e.month=\"short\";break;case 2:e.day=\"numeric\";break;case 3:e.hour12=!1,e.hour=\"2-digit\",e.minute=\"2-digit\";break;case 4:e.hour12=!1,e.hour=\"2-digit\",e.minute=\"2-digit\",e.second=\"2-digit\"}var s=void 0===t.Co?new Date(1e3*t.So):new Date(Date.UTC(t.Co.year,t.Co.month-1,t.Co.day));return new Date(s.getUTCFullYear(),s.getUTCMonth(),s.getUTCDate(),s.getUTCHours(),s.getUTCMinutes(),s.getUTCSeconds(),s.getUTCMilliseconds()).toLocaleString(n,e)}(t,e,this.qu.locale)},t.prototype.Ul=function(t){var i=this.Xo;this.Xo=t,tn(i.jo(),this.Xo.jo())||this.Jo.m(),tn(i.qo(),this.Xo.qo())||this.Go.m(),this.ql()},t.prototype.ql=function(){this.il=null},t.prototype.ul=function(){this.ql(),this.Ko.clear()},t.prototype.rl=function(){var t=this.qu.dateFormat;this.zi.timeVisible?this.Il=new Qi({co:t,vo:this.zi.secondsVisible?\"%h:%m:%s\":\"%h:%m\",_o:\"   \",do:this.qu.locale}):this.Il=new Ki(t,this.qu.locale)},t.prototype.al=function(){if(this.zi.fixLeftEdge){var t=this._l();if(null!==t){var i=this.ss();if(null!==i){var n=i.In()-t;if(n<0){var e=this.sl-n-1;this.yn(e)}this.Ml()}}}},t.prototype.ol=function(){this.bl(),this.Ml()},t}(),hn=function(t){function i(i){var n=t.call(this)||this;return n.Xl=new Map,n.Bt=i,n}return C(i,t),i.prototype.Y=function(t){},i.prototype.K=function(t){if(this.Bt.yt){t.save();for(var i=0,n=0,e=this.Bt.Zl;n<e.length;n++)if(0!==(u=e[n]).Gt.length){t.font=u.T;var s=this.Jl(t,u.Gt);s>this.Bt.Ht?u.Al=this.Bt.Ht/s:u.Al=1,i+=u.Gl*u.Al}var r=0;switch(this.Bt.Ql){case\"top\":r=0;break;case\"center\":r=Math.max((this.Bt.Yt-i)/2,0);break;case\"bottom\":r=Math.max(this.Bt.Yt-i,0)}t.fillStyle=this.Bt.A;for(var o=0,h=this.Bt.Zl;o<h.length;o++){var u=h[o];t.save();var a=0;switch(this.Bt.tf){case\"left\":t.textAlign=\"left\",a=u.Gl/2;break;case\"center\":t.textAlign=\"center\",a=this.Bt.Ht/2;break;case\"right\":t.textAlign=\"right\",a=this.Bt.Ht-1-u.Gl/2}t.translate(a,r),t.textBaseline=\"top\",t.font=u.T,t.scale(u.Al,u.Al),t.fillText(u.Gt,0,u.if),t.restore(),r+=u.Gl*u.Al}t.restore()}},i.prototype.Jl=function(t,i){var n=this.nf(t.font),e=n.get(i);return void 0===e&&(e=t.measureText(i).width,n.set(i,e)),e},i.prototype.nf=function(t){var i=this.Xl.get(t);return void 0===i&&(i=new Map,this.Xl.set(t,i)),i},i}(V),un=function(){function t(t){this.ft=!0,this.Wt={yt:!1,A:\"\",Yt:0,Ht:0,Zl:[],Ql:\"center\",tf:\"center\"},this.zt=new hn(this.Wt),this.Rt=t}return t.prototype.vt=function(){this.ft=!0},t.prototype.dt=function(t,i){return this.ft&&(this.wt(t,i),this.ft=!1),this.zt},t.prototype.wt=function(t,i){var n=this.Rt.W(),e=this.Wt;e.yt=n.visible,e.yt&&(e.A=n.color,e.Ht=i,e.Yt=t,e.tf=n.horzAlign,e.Ql=n.vertAlign,e.Zl=[{Gt:n.text,T:O(n.fontSize,n.fontFamily,n.fontStyle),Gl:1.2*n.fontSize,if:0,Al:0}])},t}(),an=function(t){function i(i,n){var e=t.call(this)||this;return e.zi=n,e.Hi=new un(e),e}return C(i,t),i.prototype.nn=function(){return[]},i.prototype.tn=function(){return[this.Hi]},i.prototype.W=function(){return this.zi},i.prototype.hn=function(){this.Hi.vt()},i}(st);!function(t){t[t.OnTouchEnd=0]=\"OnTouchEnd\",t[t.OnNextTap=1]=\"OnNextTap\"}(rn||(rn={}));var ln,cn,fn,pn=function(){function t(t,i){this.sf=[],this.hf=[],this.hh=0,this.rf=null,this.ef=null,this.uf=new z,this.af=new z,this.lf=null,this.ff=t,this.zi=i,this.cf=new A(this),this.Da=new on(this,i.timeScale,this.zi.localization),this.ct=new rt(this,i.crosshair),this.vf=new xi(i.crosshair),this._f=new an(this,i.watermark),this.df(),this.sf[0].za(2e3),this.wf=this.Mf(0),this.bf=this.Mf(1)}return t.prototype.Ce=function(){this.mf(new ht(3))},t.prototype.Wr=function(){this.mf(new ht(2))},t.prototype.We=function(){this.mf(new ht(1))},t.prototype.Se=function(t){var i=this.pf(t);this.mf(i)},t.prototype.gf=function(){return this.ef},t.prototype.yf=function(t){var i=this.ef;this.ef=t,null!==i&&this.Se(i.kf),null!==t&&this.Se(t.kf)},t.prototype.W=function(){return this.zi},t.prototype.Pr=function(t){T(this.zi,t),this.sf.forEach(function(i){return i.Oa(t)}),void 0!==t.timeScale&&this.Da.Pr(t.timeScale),void 0!==t.localization&&this.Da.el(t.localization),(t.leftPriceScale||t.rightPriceScale)&&this.uf.m(),this.wf=this.Mf(0),this.bf=this.Mf(1),this.Ce()},t.prototype.Nf=function(t,i){if(\"left\"!==t)if(\"right\"!==t){var n=this.xf(t);null!==n&&(n.Ct.Pr(i),this.uf.m())}else this.Pr({rightPriceScale:i});else this.Pr({leftPriceScale:i})},t.prototype.xf=function(t){for(var i=0,n=this.sf;i<n.length;i++){var e=n[i],s=e.Va(t);if(null!==s)return{It:e,Ct:s}}return null},t.prototype.bt=function(){return this.Da},t.prototype.Cf=function(){return this.sf},t.prototype.Sf=function(){return this._f},t.prototype.Tf=function(){return this.ct},t.prototype.Df=function(){return this.af},t.prototype.Af=function(t,i){t.ia(i),this.yl()},t.prototype.Ra=function(t){this.hh=t,this.Da.Ra(this.hh),this.sf.forEach(function(i){return i.Ra(t)}),this.yl()},t.prototype.df=function(t){var i=new Ii(this.Da,this);void 0!==t?this.sf.splice(t,0,i):this.sf.push(i);var n=void 0===t?this.sf.length-1:t,e=new ht(3);return e.cn(n,{vn:0,_n:!0}),this.mf(e),i},t.prototype.Ka=function(t,i,n){t.Ka(i,n)},t.prototype.Xa=function(t,i,n){t.Xa(i,n),this.Te(),this.mf(this.Bf(t,2))},t.prototype.Za=function(t,i){t.Za(i),this.mf(this.Bf(t,2))},t.prototype.Ja=function(t,i,n){i.Xu()||t.Ja(i,n)},t.prototype.Ga=function(t,i,n){i.Xu()||(t.Ga(i,n),this.Te(),this.mf(this.Bf(t,2)))},t.prototype.Qa=function(t,i){i.Xu()||(t.Qa(i),this.mf(this.Bf(t,2)))},t.prototype.no=function(t,i){t.no(i),this.mf(this.Bf(t,2))},t.prototype.Lf=function(t){this.Da.da(t)},t.prototype.Ef=function(t,i){var n=this.bt();if(!n.wi()&&0!==i){var e=n.Ht();t=Math.max(1,Math.min(t,e)),n.Al(t,i),this.yl()}},t.prototype.Ff=function(t){this.Of(0),this.Vf(t),this.Pf()},t.prototype.Wf=function(t){this.Da.wa(t),this.yl()},t.prototype.zf=function(){this.Da.Ma(),this.Wr()},t.prototype.Of=function(t){this.rf=t,this.Da.ba(t)},t.prototype.Vf=function(t){var i=!1;return null!==this.rf&&Math.abs(t-this.rf)>20&&(this.rf=null,i=!0),this.Da.ma(t),this.yl(),i},t.prototype.Pf=function(){this.Da.pa(),this.Wr(),this.rf=null},t.prototype._t=function(){return this.hf},t.prototype.Rf=function(t,i,n){this.ct.Yi(t,i);var e=NaN,s=this.Da.pl(t),r=this.Da.ss();null!==r&&(s=Math.min(Math.max(r.In(),s),r.jn()));var o=n.ji(),h=o.kt();null!==h&&(e=o.qi(i,h)),e=this.vf.Je(e,s,n),this.ct.Zi(s,e,n),this.We(),this.af.m(this.ct.Mt(),{x:t,y:i})},t.prototype.If=function(){this.Tf().Gi(),this.We(),this.af.m(null,null)},t.prototype.Te=function(){var t=this.ct.It();if(null!==t){var i=this.ct.Ki(),n=this.ct.Xi();this.Rf(i,n,t)}this.ct.hn()},t.prototype.jf=function(t,i,n){var e=this.Da.gi(0);void 0!==i&&void 0!==n&&this.Da.vt(i,n);var s=this.Da.gi(0),r=this.Da.ml(),o=this.Da.ss();if(null!==o&&null!==e&&null!==s){var h=o.Uh(r),u=e.So>s.So,a=null!==t&&t>r&&!u,l=h&&this.Da.W().shiftVisibleRangeOnNewBar;if(a&&!l){var c=t-r;this.Da.yn(this.Da.Nl()-c)}}this.Da.Dl(t)},t.prototype.Be=function(t){null!==t&&t.ho()},t.prototype.oh=function(t){var i=this.sf.find(function(i){return i.oa().includes(t)});return void 0===i?null:i},t.prototype.yl=function(){this._f.hn(),this.sf.forEach(function(t){return t.ho()}),this.Te()},t.prototype.g=function(){this.sf.forEach(function(t){return t.g()}),this.sf.length=0,this.zi.localization.priceFormatter=void 0,this.zi.localization.timeFormatter=void 0},t.prototype.qf=function(){return this.cf},t.prototype.dh=function(){return this.cf.W()},t.prototype.Pa=function(){return this.uf},t.prototype.Uf=function(t,i){var n=this.sf[0],e=this.Hf(i,t,n);return this.hf.push(e),1===this.hf.length?this.Ce():this.Wr(),e},t.prototype.Yf=function(t){var i=this.oh(t),n=this.hf.indexOf(t);l(-1!==n,\"Series not found\"),this.hf.splice(n,1),f(i).ca(t),t.g&&t.g()},t.prototype.xe=function(t,i){var n=f(this.oh(t));n.ca(t);var e=this.xf(i);if(null===e){var s=t.xi();n.la(t,i,s)}else s=e.It===n?t.xi():void 0,e.It.la(t,i,s)},t.prototype.zl=function(){var t=new ht(2);t.Mn(),this.mf(t)},t.prototype.$f=function(t){var i=new ht(2);i.mn(t),this.mf(i)},t.prototype.pn=function(){var t=new ht(2);t.pn(),this.mf(t)},t.prototype.gn=function(t){var i=new ht(2);i.gn(t),this.mf(i)},t.prototype.yn=function(t){var i=new ht(2);i.yn(t),this.mf(i)},t.prototype.Kf=function(){return this.zi.rightPriceScale.visible?\"right\":\"left\"},t.prototype.Xf=function(){return this.bf},t.prototype.Zf=function(){return this.wf},t.prototype.Dt=function(t){var i=this.bf,n=this.wf;if(i===n)return i;if(t=Math.max(0,Math.min(100,Math.round(100*t))),null===this.lf||this.lf.Pn!==n||this.lf.Wn!==i)this.lf={Pn:n,Wn:i,Jf:new Map};else{var e=this.lf.Jf.get(t);if(void 0!==e)return e}var s=function(t,i,n){var e=_(t),s=e[0],r=e[1],o=e[2],h=e[3],u=_(i),a=u[0],l=u[1],c=u[2],f=u[3],p=[d(s+n*(a-s)),d(r+n*(l-r)),d(o+n*(c-o)),y(h+n*(f-h))];return\"rgba(\".concat(p[0],\", \").concat(p[1],\", \").concat(p[2],\", \").concat(p[3],\")\")}(n,i,t/100);return this.lf.Jf.set(t,s),s},t.prototype.Bf=function(t,i){var n=new ht(i);if(null!==t){var e=this.sf.indexOf(t);n.cn(e,{vn:i})}return n},t.prototype.pf=function(t,i){return void 0===i&&(i=2),this.Bf(this.oh(t),i)},t.prototype.mf=function(t){this.ff&&this.ff(t),this.sf.forEach(function(t){return t.eo().ou().vt()})},t.prototype.Hf=function(t,i,n){var e=new Ci(this,t,i),s=void 0!==t.priceScaleId?t.priceScaleId:this.Kf();return n.la(e,s),ot(s)||e.Pr(t),e},t.prototype.Mf=function(t){var i=this.zi.layout;return\"gradient\"===i.background.type?0===t?i.background.topColor:i.background.bottomColor:i.background.color},t}();function vn(t){void 0!==t.borderColor&&(t.borderUpColor=t.borderColor,t.borderDownColor=t.borderColor),void 0!==t.wickColor&&(t.wickUpColor=t.wickColor,t.wickDownColor=t.wickColor)}function dn(t){return!k(t)&&!E(t)}function yn(t){return k(t)}!function(t){t[t.Disabled=0]=\"Disabled\",t[t.Continuous=1]=\"Continuous\",t[t.OnDataUpdate=2]=\"OnDataUpdate\"}(ln||(ln={})),function(t){t[t.LastBar=0]=\"LastBar\",t[t.LastVisible=1]=\"LastVisible\"}(cn||(cn={})),function(t){t.Solid=\"solid\",t.VerticalGradient=\"gradient\"}(fn||(fn={}));var wn=function(){function t(t,i){this.Ft=t,this.Ot=i}return t.prototype.br=function(t){return this.Ft===t.Ft&&this.Ot===t.Ot},t}();function gn(t){return t.ownerDocument&&t.ownerDocument.defaultView&&t.ownerDocument.defaultView.devicePixelRatio||1}function mn(t){var i=f(t.getContext(\"2d\"));return i.setTransform(1,0,0,1,0,0),i}function Mn(t,i){var n=t.createElement(\"canvas\"),e=gn(n);return n.style.width=\"\".concat(i.Ft,\"px\"),n.style.height=\"\".concat(i.Ot,\"px\"),n.width=i.Ft*e,n.height=i.Ot*e,n}function _n(t,i){var n=f(t.ownerDocument).createElement(\"canvas\");t.appendChild(n);var s,r=(void 0===(s={allowDownsampling:!1})&&(s=e),new h(n,s));return r.resizeCanvas({width:i.Ft,height:i.Ot}),r}function bn(t,i){return t.Gf-i.Gf}function Sn(t,i,n){var e=(t.Gf-i.Gf)/(t.rt-i.rt);return Math.sign(e)*Math.min(Math.abs(e),n)}var Cn=function(){function t(t,i,n,e){this.Qf=null,this.tc=null,this.ic=null,this.nc=null,this.sc=null,this.hc=0,this.rc=0,this.ec=!1,this.uc=t,this.ac=i,this.oc=n,this.Cn=e}return t.prototype.lc=function(t,i){if(null!==this.Qf){if(this.Qf.rt===i)return void(this.Qf.Gf=t);if(Math.abs(this.Qf.Gf-t)<this.Cn)return}this.nc=this.ic,this.ic=this.tc,this.tc=this.Qf,this.Qf={rt:i,Gf:t}},t.prototype.xh=function(t,i){if(null!==this.Qf&&null!==this.tc&&!(i-this.Qf.rt>50)){var n=0,e=Sn(this.Qf,this.tc,this.ac),s=bn(this.Qf,this.tc),r=[e],o=[s];if(n+=s,null!==this.ic){var h=Sn(this.tc,this.ic,this.ac);if(Math.sign(h)===Math.sign(e)){var u=bn(this.tc,this.ic);if(r.push(h),o.push(u),n+=u,null!==this.nc){var a=Sn(this.ic,this.nc,this.ac);if(Math.sign(a)===Math.sign(e)){var l=bn(this.ic,this.nc);r.push(a),o.push(l),n+=l}}}}for(var c,f,p,v=0,d=0;d<r.length;++d)v+=o[d]/n*r[d];Math.abs(v)<this.uc||(this.sc={Gf:t,rt:i},this.rc=v,this.hc=(c=Math.abs(v),f=this.oc,p=Math.log(f),Math.log(1*p/-c)/p))}},t.prototype.fc=function(t){var i=f(this.sc),n=t-i.rt;return i.Gf+this.rc*(Math.pow(this.oc,n)-1)/Math.log(this.oc)},t.prototype.cc=function(t){return null===this.sc||this.vc(t)===this.hc},t.prototype._c=function(){return this.ec},t.prototype.dc=function(){this.ec=!0},t.prototype.vc=function(t){var i=t-f(this.sc).rt;return Math.min(i,this.hc)},t}(),xn=\"undefined\"!=typeof window;function Bn(){return!!xn&&window.navigator.userAgent.toLowerCase().indexOf(\"firefox\")>-1}function zn(){return!!xn&&/iPhone|iPad|iPod/.test(window.navigator.platform)}var Tn=function(){function t(t,i,n){var e=this;this.wc=0,this.Mc=null,this.bc={tt:Number.NEGATIVE_INFINITY,it:Number.POSITIVE_INFINITY},this.mc=0,this.gc=null,this.yc={tt:Number.NEGATIVE_INFINITY,it:Number.POSITIVE_INFINITY},this.kc=null,this.Nc=!1,this.xc=null,this.Cc=null,this.Sc=!1,this.Tc=!1,this.Dc=!1,this.Ac=null,this.Bc=null,this.Lc=null,this.Ec=null,this.Fc=null,this.Oc=null,this.Vc=null,this.Pc=0,this.Wc=!1,this.zc=!1,this.Rc=!1,this.Ic=0,this.jc=null,this.qc=!zn(),this.Uc=function(t){e.Hc(t)},this.Yc=function(t){if(e.$c(t)){var i=e.Kc(t);++e.mc,e.gc&&e.mc>1&&(e.Zc(En(t),e.yc).Xc<30&&!e.Dc&&e.Jc(i,e.Qc.Gc),e.tv())}else i=e.Kc(t),++e.wc,e.Mc&&e.wc>1&&(e.Zc(En(t),e.bc).Xc<5&&!e.Tc&&e.iv(i,e.Qc.nv),e.sv())},this.hv=t,this.Qc=i,this.zi=n,this.rv()}return t.prototype.g=function(){null!==this.Ac&&(this.Ac(),this.Ac=null),null!==this.Bc&&(this.Bc(),this.Bc=null),null!==this.Ec&&(this.Ec(),this.Ec=null),null!==this.Fc&&(this.Fc(),this.Fc=null),null!==this.Oc&&(this.Oc(),this.Oc=null),null!==this.Lc&&(this.Lc(),this.Lc=null),this.ev(),this.sv()},t.prototype.uv=function(t){var i=this;this.Ec&&this.Ec();var n=this.av.bind(this);if(this.Ec=function(){i.hv.removeEventListener(\"mousemove\",n)},this.hv.addEventListener(\"mousemove\",n),!this.$c(t)){var e=this.Kc(t);this.iv(e,this.Qc.ov),this.qc=!0}},t.prototype.sv=function(){null!==this.Mc&&clearTimeout(this.Mc),this.wc=0,this.Mc=null,this.bc={tt:Number.NEGATIVE_INFINITY,it:Number.POSITIVE_INFINITY}},t.prototype.tv=function(){null!==this.gc&&clearTimeout(this.gc),this.mc=0,this.gc=null,this.yc={tt:Number.NEGATIVE_INFINITY,it:Number.POSITIVE_INFINITY}},t.prototype.av=function(t){if(!this.Rc&&null===this.Cc&&!this.$c(t)){var i=this.Kc(t);this.iv(i,this.Qc.lv),this.qc=!0}},t.prototype.fv=function(t){var i=Ln(t.changedTouches,f(this.jc));if(null!==i&&(this.Ic=Pn(t),null===this.Vc&&!this.zc)){this.Wc=!0;var n=this.Zc(En(i),f(this.Cc)),e=n.cv,s=n.vv,r=n.Xc;if(this.Sc||!(r<5)){if(!this.Sc){var o=.5*e,h=s>=o&&!this.zi._v(),u=o>s&&!this.zi.dv();h||u||(this.zc=!0),this.Sc=!0,this.Dc=!0,this.ev(),this.tv()}if(!this.zc){var a=this.Kc(t,i);this.Jc(a,this.Qc.wv),Dn(t)}}}},t.prototype.Mv=function(t){if(0===t.button&&(this.Zc(En(t),f(this.xc)).Xc>=5&&(this.Tc=!0,this.sv()),this.Tc)){var i=this.Kc(t);this.iv(i,this.Qc.bv)}},t.prototype.Zc=function(t,i){var n=Math.abs(i.tt-t.tt),e=Math.abs(i.it-t.it);return{cv:n,vv:e,Xc:n+e}},t.prototype.mv=function(t){var i=Ln(t.changedTouches,f(this.jc));if(null===i&&0===t.touches.length&&(i=t.changedTouches[0]),null!==i){this.jc=null,this.Ic=Pn(t),this.ev(),this.Cc=null,this.Oc&&(this.Oc(),this.Oc=null);var n=this.Kc(t,i);this.Jc(n,this.Qc.pv),++this.mc,this.gc&&this.mc>1?(this.Zc(En(i),this.yc).Xc<30&&!this.Dc&&this.Jc(n,this.Qc.Gc),this.tv()):this.Dc||(this.Jc(n,this.Qc.gv),this.Qc.gv&&Dn(t)),0===this.mc&&Dn(t),0===t.touches.length&&this.Nc&&(this.Nc=!1,Dn(t))}},t.prototype.Hc=function(t){if(0===t.button){var i=this.Kc(t);this.xc=null,this.Rc=!1,this.Fc&&(this.Fc(),this.Fc=null),Bn()&&this.hv.ownerDocument.documentElement.removeEventListener(\"mouseleave\",this.Uc),this.$c(t)||(this.iv(i,this.Qc.yv),++this.wc,this.Mc&&this.wc>1?(this.Zc(En(t),this.bc).Xc<5&&!this.Tc&&this.iv(i,this.Qc.nv),this.sv()):this.Tc||this.iv(i,this.Qc.kv))}},t.prototype.ev=function(){null!==this.kc&&(clearTimeout(this.kc),this.kc=null)},t.prototype.Nv=function(t){if(null===this.jc){var i=t.changedTouches[0];this.jc=i.identifier,this.Ic=Pn(t);var n=this.hv.ownerDocument.documentElement;this.Dc=!1,this.Sc=!1,this.zc=!1,this.Cc=En(i),this.Oc&&(this.Oc(),this.Oc=null);var e=this.fv.bind(this),s=this.mv.bind(this);this.Oc=function(){n.removeEventListener(\"touchmove\",e),n.removeEventListener(\"touchend\",s)},n.addEventListener(\"touchmove\",e,{passive:!1}),n.addEventListener(\"touchend\",s,{passive:!1}),this.ev(),this.kc=setTimeout(this.xv.bind(this,t),240);var r=this.Kc(t,i);this.Jc(r,this.Qc.Cv),this.gc||(this.mc=0,this.gc=setTimeout(this.tv.bind(this),500),this.yc=En(i))}},t.prototype.Sv=function(t){if(0===t.button){var i=this.hv.ownerDocument.documentElement;Bn()&&i.addEventListener(\"mouseleave\",this.Uc),this.Tc=!1,this.xc=En(t),this.Fc&&(this.Fc(),this.Fc=null);var n=this.Mv.bind(this),e=this.Hc.bind(this);if(this.Fc=function(){i.removeEventListener(\"mousemove\",n),i.removeEventListener(\"mouseup\",e)},i.addEventListener(\"mousemove\",n),i.addEventListener(\"mouseup\",e),this.Rc=!0,!this.$c(t)){var s=this.Kc(t);this.iv(s,this.Qc.Tv),this.Mc||(this.wc=0,this.Mc=setTimeout(this.sv.bind(this),500),this.bc=En(t))}}},t.prototype.rv=function(){var t=this;this.hv.addEventListener(\"mouseenter\",this.uv.bind(this)),this.hv.addEventListener(\"touchcancel\",this.ev.bind(this));var i=this.hv.ownerDocument,n=function(i){t.Qc.Dv&&(i.target&&t.hv.contains(i.target)||t.Qc.Dv())};this.Bc=function(){i.removeEventListener(\"touchstart\",n)},this.Ac=function(){i.removeEventListener(\"mousedown\",n)},i.addEventListener(\"mousedown\",n),i.addEventListener(\"touchstart\",n,{passive:!0}),zn()&&(this.Lc=function(){t.hv.removeEventListener(\"dblclick\",t.Yc)},this.hv.addEventListener(\"dblclick\",this.Yc)),this.hv.addEventListener(\"mouseleave\",this.Av.bind(this)),this.hv.addEventListener(\"touchstart\",this.Nv.bind(this),{passive:!0}),function(t){xn&&void 0!==window.chrome&&t.addEventListener(\"mousedown\",function(t){if(1===t.button)return t.preventDefault(),!1})}(this.hv),this.hv.addEventListener(\"mousedown\",this.Sv.bind(this)),this.Bv(),this.hv.addEventListener(\"touchmove\",function(){},{passive:!1})},t.prototype.Bv=function(){var t=this;void 0===this.Qc.Lv&&void 0===this.Qc.Ev&&void 0===this.Qc.Fv||(this.hv.addEventListener(\"touchstart\",function(i){return t.Ov(i.touches)},{passive:!0}),this.hv.addEventListener(\"touchmove\",function(i){if(2===i.touches.length&&null!==t.Vc&&void 0!==t.Qc.Ev){var n=kn(i.touches[0],i.touches[1])/t.Pc;t.Qc.Ev(t.Vc,n),Dn(i)}},{passive:!1}),this.hv.addEventListener(\"touchend\",function(i){t.Ov(i.touches)}))},t.prototype.Ov=function(t){1===t.length&&(this.Wc=!1),2!==t.length||this.Wc||this.Nc?this.Vv():this.Pv(t)},t.prototype.Pv=function(t){var i=this.hv.getBoundingClientRect()||{left:0,top:0};this.Vc={tt:(t[0].clientX-i.left+(t[1].clientX-i.left))/2,it:(t[0].clientY-i.top+(t[1].clientY-i.top))/2},this.Pc=kn(t[0],t[1]),void 0!==this.Qc.Lv&&this.Qc.Lv(),this.ev()},t.prototype.Vv=function(){null!==this.Vc&&(this.Vc=null,void 0!==this.Qc.Fv&&this.Qc.Fv())},t.prototype.Av=function(t){if(this.Ec&&this.Ec(),!this.$c(t)&&this.qc){var i=this.Kc(t);this.iv(i,this.Qc.Wv),this.qc=!zn()}},t.prototype.xv=function(t){var i=Ln(t.touches,f(this.jc));if(null!==i){var n=this.Kc(t,i);this.Jc(n,this.Qc.zv),this.Dc=!0,this.Nc=!0}},t.prototype.$c=function(t){return t.sourceCapabilities&&void 0!==t.sourceCapabilities.firesTouchEvents?t.sourceCapabilities.firesTouchEvents:Pn(t)<this.Ic+500},t.prototype.Jc=function(t,i){i&&i.call(this.Qc,t)},t.prototype.iv=function(t,i){i&&i.call(this.Qc,t)},t.prototype.Kc=function(t,i){var n=i||t,e=this.hv.getBoundingClientRect()||{left:0,top:0};return{Rv:n.clientX,Iv:n.clientY,jv:n.pageX,qv:n.pageY,Uv:n.screenX,Hv:n.screenY,Yv:n.clientX-e.left,$v:n.clientY-e.top,Kv:t.ctrlKey,Xv:t.altKey,Zv:t.shiftKey,Jv:t.metaKey,Gv:!t.type.startsWith(\"mouse\")&&\"contextmenu\"!==t.type&&\"click\"!==t.type,Qv:t.type,t_:n.target,i_:t.view,n_:function(){\"touchstart\"!==t.type&&Dn(t)}}},t}();function kn(t,i){var n=t.clientX-i.clientX,e=t.clientY-i.clientY;return Math.sqrt(n*n+e*e)}function Dn(t){t.cancelable&&t.preventDefault()}function En(t){return{tt:t.pageX,it:t.pageY}}function Pn(t){return t.timeStamp||performance.now()}function Ln(t,i){for(var n=0;n<t.length;++n)if(t[n].identifier===i)return t[n];return null}var Wn=function(){function t(t,i,n,e){this.rh=new Yt(200),this.R=0,this.s_=\"\",this.Yh=\"\",this.th=[],this.h_=new Map,this.R=t,this.s_=i,this.Yh=O(t,n,e)}return t.prototype.g=function(){this.rh.ih(),this.th=[],this.h_.clear()},t.prototype.r_=function(t,i,n,e,s){var r=this.e_(t,i);if(\"left\"!==s){var o=gn(t.canvas);n-=Math.floor(r.u_*o)}e-=Math.floor(r.Yt/2),t.drawImage(r.a_,n,e,r.Ht,r.Yt)},t.prototype.e_=function(t,i){var n,e=this;if(this.h_.has(i))n=c(this.h_.get(i));else{if(this.th.length>=200){var s=c(this.th.shift());this.h_.delete(s)}var r=gn(t.canvas),o=Math.ceil(this.R/4.5),h=Math.round(this.R/10),u=Math.ceil(this.rh.Qt(t,i)),a=Et(Math.round(u+2*o)),l=Et(this.R+2*o),f=Mn(document,new wn(a,l));n={Gt:i,u_:Math.round(Math.max(1,u)),Ht:Math.ceil(a*r),Yt:Math.ceil(l*r),a_:f},0!==u&&(this.th.push(n.Gt),this.h_.set(n.Gt,n)),K(t=mn(n.a_),r,function(){t.font=e.Yh,t.fillStyle=e.s_,t.fillText(i,0,l-o-h)})}return n},t}(),Nn=function(){function t(t,i,n,e){var s=this;this._i=null,this.o_=null,this.l_=!1,this.f_=new Yt(50),this.c_=new Wn(11,\"#000\"),this.s_=null,this.Yh=null,this.v_=0,this.__=!1,this.d_=function(){s.w_(s.cf.W()),s.__||s.Di.M_().jt().Wr()},this.b_=function(){s.__||s.Di.M_().jt().Wr()},this.Di=t,this.zi=i,this.cf=n,this.m_=\"left\"===e,this.p_=document.createElement(\"div\"),this.p_.style.height=\"100%\",this.p_.style.overflow=\"hidden\",this.p_.style.width=\"25px\",this.p_.style.left=\"0\",this.p_.style.position=\"relative\",this.g_=_n(this.p_,new wn(16,16)),this.g_.subscribeCanvasConfigured(this.d_);var r=this.g_.canvas;r.style.position=\"absolute\",r.style.zIndex=\"1\",r.style.left=\"0\",r.style.top=\"0\",this.y_=_n(this.p_,new wn(16,16)),this.y_.subscribeCanvasConfigured(this.b_);var o=this.y_.canvas;o.style.position=\"absolute\",o.style.zIndex=\"2\",o.style.left=\"0\",o.style.top=\"0\";var h={Tv:this.k_.bind(this),Cv:this.k_.bind(this),bv:this.N_.bind(this),wv:this.N_.bind(this),Dv:this.x_.bind(this),yv:this.C_.bind(this),pv:this.C_.bind(this),nv:this.S_.bind(this),Gc:this.S_.bind(this),ov:this.T_.bind(this),Wv:this.D_.bind(this)};this.A_=new Tn(this.y_.canvas,h,{_v:function(){return!1},dv:function(){return!0}})}return t.prototype.g=function(){this.A_.g(),this.y_.unsubscribeCanvasConfigured(this.b_),this.y_.destroy(),this.g_.unsubscribeCanvasConfigured(this.d_),this.g_.destroy(),null!==this._i&&this._i._a().M(this),this._i=null,this.c_.g()},t.prototype.B_=function(){return this.p_},t.prototype.ht=function(){return f(this._i).W().borderColor},t.prototype.L_=function(){return this.zi.textColor},t.prototype.S=function(){return this.zi.fontSize},t.prototype.E_=function(){return O(this.S(),this.zi.fontFamily)},t.prototype.F_=function(){var t=this.cf.W(),i=this.s_!==t.A,n=this.Yh!==t.T;return(i||n)&&(this.w_(t),this.s_=t.A),n&&(this.f_.ih(),this.Yh=t.T),t},t.prototype.O_=function(){if(null===this._i)return 0;var t=0,i=this.F_(),n=mn(this.g_.canvas),e=this._i.au();n.font=this.E_(),e.length>0&&(t=Math.max(this.f_.Qt(n,e[0].yu),this.f_.Qt(n,e[e.length-1].yu)));for(var s=this.V_(),r=s.length;r--;){var o=this.f_.Qt(n,s[r].Gt());o>t&&(t=o)}var h=this._i.kt();if(null!==h&&null!==this.o_){var u=this._i.qi(1,h),a=this._i.qi(this.o_.Ot-2,h);t=Math.max(t,this.f_.Qt(n,this._i.Mi(Math.floor(Math.min(u,a))+.11111111111111,h)),this.f_.Qt(n,this._i.Mi(Math.ceil(Math.max(u,a))-.11111111111111,h)))}var l=t||34,c=Math.ceil(i.N+i.C+i.L+i.F+l);return c+c%2},t.prototype.P_=function(t){if(t.Ft<0||t.Ot<0)throw new Error(\"Try to set invalid size to PriceAxisWidget \"+JSON.stringify(t));null!==this.o_&&this.o_.br(t)||(this.o_=t,this.__=!0,this.g_.resizeCanvas({width:t.Ft,height:t.Ot}),this.y_.resizeCanvas({width:t.Ft,height:t.Ot}),this.__=!1,this.p_.style.width=t.Ft+\"px\",this.p_.style.height=t.Ot+\"px\",this.p_.style.minWidth=t.Ft+\"px\")},t.prototype.W_=function(){return f(this.o_).Ft},t.prototype.Si=function(t){this._i!==t&&(null!==this._i&&this._i._a().M(this),this._i=t,t._a().u(this.Eu.bind(this),this))},t.prototype.Ct=function(){return this._i},t.prototype.ih=function(){var t=this.Di.z_();this.Di.M_().jt().no(t,f(this.Ct()))},t.prototype.R_=function(t){if(null!==this.o_){if(1!==t){var i=mn(this.g_.canvas);this.I_(),this.j_(i,this.g_.pixelRatio),this.Rs(i,this.g_.pixelRatio),this.q_(i,this.g_.pixelRatio),this.U_(i,this.g_.pixelRatio)}var n=mn(this.y_.canvas),e=this.o_.Ft,s=this.o_.Ot;K(n,this.y_.pixelRatio,function(){n.clearRect(0,0,e,s)}),this.H_(n,this.y_.pixelRatio)}},t.prototype.Y_=function(){return this.g_.canvas},t.prototype.vt=function(){var t;null===(t=this._i)||void 0===t||t.au()},t.prototype.k_=function(t){if(null!==this._i&&!this._i.wi()&&this.Di.M_().W().handleScale.axisPressedMouseMove.price){var i=this.Di.M_().jt(),n=this.Di.z_();this.l_=!0,i.Ka(n,this._i,t.$v)}},t.prototype.N_=function(t){if(null!==this._i&&this.Di.M_().W().handleScale.axisPressedMouseMove.price){var i=this.Di.M_().jt(),n=this.Di.z_(),e=this._i;i.Xa(n,e,t.$v)}},t.prototype.x_=function(){if(null!==this._i&&this.Di.M_().W().handleScale.axisPressedMouseMove.price){var t=this.Di.M_().jt(),i=this.Di.z_(),n=this._i;this.l_&&(this.l_=!1,t.Za(i,n))}},t.prototype.C_=function(t){if(null!==this._i&&this.Di.M_().W().handleScale.axisPressedMouseMove.price){var i=this.Di.M_().jt(),n=this.Di.z_();this.l_=!1,i.Za(n,this._i)}},t.prototype.S_=function(t){this.Di.M_().W().handleScale.axisDoubleClickReset&&this.ih()},t.prototype.T_=function(t){null!==this._i&&(!this.Di.M_().jt().W().handleScale.axisPressedMouseMove.price||this._i.vr()||this._i.Zu()||this.K_(1))},t.prototype.D_=function(t){this.K_(0)},t.prototype.V_=function(){var t=this,i=[],n=null===this._i?void 0:this._i;return function(e){for(var s=0;s<e.length;++s)for(var r=e[s].nn(t.Di.z_(),n),o=0;o<r.length;o++)i.push(r[o])}(this.Di.z_().oa()),i},t.prototype.j_=function(t,i){var n=this;if(null!==this.o_){var e=this.o_.Ft,s=this.o_.Ot;K(t,i,function(){var i=n.Di.z_().jt(),r=i.Zf(),o=i.Xf();r===o?H(t,0,0,e,s,r):U(t,0,0,e,s,r,o)})}},t.prototype.Rs=function(t,i){if(null!==this.o_&&null!==this._i&&this._i.W().borderVisible){t.save(),t.fillStyle=this.ht();var n,e=Math.max(1,Math.floor(this.F_().N*i));n=this.m_?Math.floor(this.o_.Ft*i)-e:0,t.fillRect(n,0,e,Math.ceil(this.o_.Ot*i)),t.restore()}},t.prototype.q_=function(t,i){if(null!==this.o_&&null!==this._i){var n=this._i.au();t.save(),t.strokeStyle=this.ht(),t.font=this.E_(),t.fillStyle=this.ht();var e=this.F_(),s=this._i.W().borderVisible&&this._i.W().drawTicks,r=this.m_?Math.floor((this.o_.Ft-e.C)*i-e.N*i):Math.floor(e.N*i),o=this.m_?Math.round(r-e.L*i):Math.round(r+e.C*i+e.L*i),h=this.m_?\"right\":\"left\",u=Math.max(1,Math.floor(i)),a=Math.floor(.5*i);if(s){var l=Math.round(e.C*i);t.beginPath();for(var c=0,f=n;c<f.length;c++){var p=f[c];t.rect(r,Math.round(p.su*i)-a,l,u)}t.fill()}t.fillStyle=this.L_();for(var v=0,d=n;v<d.length;v++)p=d[v],this.c_.r_(t,p.yu,o,Math.round(p.su*i),h);t.restore()}},t.prototype.I_=function(){if(null!==this.o_&&null!==this._i){var t=this.o_.Ot/2,i=[],n=this._i.oa().slice(),e=this.Di.z_(),s=this.F_();this._i===e.fh()&&this.Di.z_().oa().forEach(function(t){e.lh(t)&&n.push(t)});var r=this._i.Ge()[0],o=this._i;n.forEach(function(n){var s=n.nn(e,o);s.forEach(function(t){t.oi(null),t.li()&&i.push(t)}),r===n&&s.length>0&&(t=s[0].ti())});var h=i.filter(function(i){return i.ti()<=t}),u=i.filter(function(i){return i.ti()>t});if(h.sort(function(t,i){return i.ti()-t.ti()}),h.length&&u.length&&u.push(h[0]),u.sort(function(t,i){return t.ti()-i.ti()}),i.forEach(function(t){return t.oi(t.ti())}),this._i.W().alignLabels){for(var a=1;a<h.length;a++){var l=h[a],c=(p=h[a-1]).Yt(s,!1);l.ti()>(v=p.ai())-c&&l.oi(v-c)}for(var f=1;f<u.length;f++){var p,v;l=u[f],c=(p=u[f-1]).Yt(s,!0),l.ti()<(v=p.ai())+c&&l.oi(v+c)}}}},t.prototype.U_=function(t,i){var n=this;if(null!==this.o_){t.save();var e=this.o_,s=this.V_(),r=this.F_(),o=this.m_?\"right\":\"left\";s.forEach(function(s){if(s.fi()){var h=s.dt(f(n._i));t.save(),h.H(t,r,n.f_,e.Ft,o,i),t.restore()}}),t.restore()}},t.prototype.H_=function(t,i){var n=this;if(null!==this.o_&&null!==this._i){t.save();var e=this.o_,s=this.Di.M_().jt(),r=[],o=this.Di.z_(),h=s.Tf().nn(o,this._i);h.length&&r.push(h);var u=this.F_(),a=this.m_?\"right\":\"left\";r.forEach(function(s){s.forEach(function(s){t.save(),s.dt(f(n._i)).H(t,u,n.f_,e.Ft,a,i),t.restore()})}),t.restore()}},t.prototype.K_=function(t){this.p_.style.cursor=1===t?\"ns-resize\":\"default\"},t.prototype.Eu=function(){var t=this.O_();this.v_<t&&this.Di.M_().jt().Ce(),this.v_=t},t.prototype.w_=function(t){this.c_.g(),this.c_=new Wn(t.S,t.A,t.D)},t}();function Rn(t,i,n,e,s){t.$&&t.$(i,n,e,s)}function On(t,i,n,e,s){t.H(i,n,e,s)}function An(t,i){return t.tn(i)}function Gn(t,i){return void 0!==t.Pe?t.Pe(i):[]}var Vn=function(){function t(t,i){var n=this;this.o_=new wn(0,0),this.X_=null,this.Z_=null,this.J_=null,this.G_=!1,this.Q_=new z,this.td=0,this.nd=!1,this.sd=null,this.hd=!1,this.rd=null,this.ed=null,this.__=!1,this.d_=function(){n.__||null===n.ud||n.pi().Wr()},this.b_=function(){n.__||null===n.ud||n.pi().Wr()},this.ad=t,this.ud=i,this.ud.ro().u(this.od.bind(this),this,!0),this.ld=document.createElement(\"td\"),this.ld.style.padding=\"0\",this.ld.style.position=\"relative\";var e=document.createElement(\"div\");e.style.width=\"100%\",e.style.height=\"100%\",e.style.position=\"relative\",e.style.overflow=\"hidden\",this.fd=document.createElement(\"td\"),this.fd.style.padding=\"0\",this.vd=document.createElement(\"td\"),this.vd.style.padding=\"0\",this.ld.appendChild(e),this.g_=_n(e,new wn(16,16)),this.g_.subscribeCanvasConfigured(this.d_);var s=this.g_.canvas;s.style.position=\"absolute\",s.style.zIndex=\"1\",s.style.left=\"0\",s.style.top=\"0\",this.y_=_n(e,new wn(16,16)),this.y_.subscribeCanvasConfigured(this.b_);var r=this.y_.canvas;r.style.position=\"absolute\",r.style.zIndex=\"2\",r.style.left=\"0\",r.style.top=\"0\",this._d=document.createElement(\"tr\"),this._d.appendChild(this.fd),this._d.appendChild(this.ld),this._d.appendChild(this.vd),this.dd(),this.A_=new Tn(this.y_.canvas,this,{_v:function(){return null===n.sd&&!n.ad.W().handleScroll.vertTouchDrag},dv:function(){return null===n.sd&&!n.ad.W().handleScroll.horzTouchDrag}})}return t.prototype.g=function(){null!==this.X_&&this.X_.g(),null!==this.Z_&&this.Z_.g(),this.y_.unsubscribeCanvasConfigured(this.b_),this.y_.destroy(),this.g_.unsubscribeCanvasConfigured(this.d_),this.g_.destroy(),null!==this.ud&&this.ud.ro().M(this),this.A_.g()},t.prototype.z_=function(){return f(this.ud)},t.prototype.wd=function(i){null!==this.ud&&this.ud.ro().M(this),this.ud=i,null!==this.ud&&this.ud.ro().u(t.prototype.od.bind(this),this,!0),this.dd()},t.prototype.M_=function(){return this.ad},t.prototype.B_=function(){return this._d},t.prototype.dd=function(){if(null!==this.ud&&(this.Md(),0!==this.pi()._t().length)){if(null!==this.X_){var t=this.ud.Ya();this.X_.Si(f(t))}if(null!==this.Z_){var i=this.ud.$a();this.Z_.Si(f(i))}}},t.prototype.bd=function(){null!==this.X_&&this.X_.vt(),null!==this.Z_&&this.Z_.vt()},t.prototype.Wa=function(){return null!==this.ud?this.ud.Wa():0},t.prototype.za=function(t){this.ud&&this.ud.za(t)},t.prototype.ov=function(t){if(this.ud){this.md();var i=t.Yv,n=t.$v;this.pd(i,n)}},t.prototype.Tv=function(t){this.md(),this.gd(),this.pd(t.Yv,t.$v)},t.prototype.lv=function(t){if(this.ud){this.md();var i=t.Yv,n=t.$v;this.pd(i,n);var e=this.$h(i,n);this.pi().yf(e&&{kf:e.kf,yd:e.yd})}},t.prototype.kv=function(t){if(null!==this.ud){this.md();var i=t.Yv,n=t.$v;if(this.Q_.p()){var e=this.pi().Tf().Mt();this.Q_.m(e,{x:i,y:n})}}},t.prototype.bv=function(t){this.md(),this.kd(t),this.pd(t.Yv,t.$v)},t.prototype.yv=function(t){null!==this.ud&&(this.md(),this.nd=!1,this.Nd(t))},t.prototype.zv=function(t){if(this.nd=!0,null===this.sd){var i={x:t.Yv,y:t.$v};this.xd(i,i)}},t.prototype.Wv=function(t){null!==this.ud&&(this.md(),this.ud.jt().yf(null),this.Cd())},t.prototype.Sd=function(){return this.Q_},t.prototype.Lv=function(){this.td=1,this.Td()},t.prototype.Ev=function(t,i){if(this.ad.W().handleScale.pinch){var n=5*(i-this.td);this.td=i,this.pi().Ef(t.tt,n)}},t.prototype.Cv=function(t){if(this.nd=!1,this.hd=null!==this.sd,this.gd(),null!==this.sd){var i=this.pi().Tf();this.rd={x:i.$t(),y:i.Kt()},this.sd={x:t.Yv,y:t.$v}}},t.prototype.wv=function(t){if(null!==this.ud){var i=t.Yv,n=t.$v;if(null===this.sd)this.kd(t);else{this.hd=!1;var e=f(this.rd),s=e.x+(i-this.sd.x),r=e.y+(n-this.sd.y);this.pd(s,r)}}},t.prototype.pv=function(t){0===this.M_().W().trackingMode.exitMode&&(this.hd=!0),this.Dd(),this.Nd(t)},t.prototype.$h=function(t,i){var n=this.ud;if(null===n)return null;for(var e=0,s=n.oa();e<s.length;e++){var r=s[e],o=this.Ad(r.tn(n),t,i);if(null!==o)return{kf:r,i_:o.i_,yd:o.yd}}return null},t.prototype.Bd=function(t,i){f(\"left\"===i?this.X_:this.Z_).P_(new wn(t,this.o_.Ot))},t.prototype.Ld=function(){return this.o_},t.prototype.P_=function(t){if(t.Ft<0||t.Ot<0)throw new Error(\"Try to set invalid size to PaneWidget \"+JSON.stringify(t));this.o_.br(t)||(this.o_=t,this.__=!0,this.g_.resizeCanvas({width:t.Ft,height:t.Ot}),this.y_.resizeCanvas({width:t.Ft,height:t.Ot}),this.__=!1,this.ld.style.width=t.Ft+\"px\",this.ld.style.height=t.Ot+\"px\")},t.prototype.Ed=function(){var t=f(this.ud);t.Ha(t.Ya()),t.Ha(t.$a());for(var i=0,n=t.Ge();i<n.length;i++){var e=n[i];if(t.lh(e)){var s=e.Ct();null!==s&&t.Ha(s),e.hn()}}},t.prototype.Y_=function(){return this.g_.canvas},t.prototype.R_=function(t){if(0!==t&&null!==this.ud){if(t>1&&this.Ed(),null!==this.X_&&this.X_.R_(t),null!==this.Z_&&this.Z_.R_(t),1!==t){var i=mn(this.g_.canvas);i.save(),this.j_(i,this.g_.pixelRatio),this.ud&&(this.Fd(i,this.g_.pixelRatio),this.Od(i,this.g_.pixelRatio),this.Vd(i,this.g_.pixelRatio,An)),i.restore()}var n=mn(this.y_.canvas);n.clearRect(0,0,Math.ceil(this.o_.Ft*this.y_.pixelRatio),Math.ceil(this.o_.Ot*this.y_.pixelRatio)),this.Vd(n,this.g_.pixelRatio,Gn),this.Pd(n,this.y_.pixelRatio)}},t.prototype.Wd=function(){return this.X_},t.prototype.zd=function(){return this.Z_},t.prototype.od=function(){null!==this.ud&&this.ud.ro().M(this),this.ud=null},t.prototype.j_=function(t,i){var n=this;K(t,i,function(){var i=n.pi(),e=i.Zf(),s=i.Xf();e===s?H(t,0,0,n.o_.Ft,n.o_.Ot,s):U(t,0,0,n.o_.Ft,n.o_.Ot,e,s)})},t.prototype.Fd=function(t,i){var n=f(this.ud),e=n.eo().ou().dt(n.Yt(),n.Ht());null!==e&&(t.save(),e.H(t,i,!1),t.restore())},t.prototype.Od=function(t,i){var n=this.pi().Sf();this.Rd(t,i,An,Rn,n),this.Rd(t,i,An,On,n)},t.prototype.Pd=function(t,i){this.Rd(t,i,An,On,this.pi().Tf())},t.prototype.Vd=function(t,i,n){for(var e=f(this.ud).oa(),s=0,r=e;s<r.length;s++){var o=r[s];this.Rd(t,i,n,Rn,o)}for(var h=0,u=e;h<u.length;h++)o=u[h],this.Rd(t,i,n,On,o)},t.prototype.Rd=function(t,i,n,e,s){for(var r=f(this.ud),o=n(s,r),h=r.Yt(),u=r.Ht(),a=r.jt().gf(),l=null!==a&&a.kf===s,c=null!==a&&l&&void 0!==a.yd?a.yd.Kh:void 0,p=0,v=o;p<v.length;p++){var d=v[p].dt(h,u);null!==d&&(t.save(),e(d,t,i,l,c),t.restore())}},t.prototype.Ad=function(t,i,n){for(var e=0,s=t;e<s.length;e++){var r=s[e],o=r.dt(this.o_.Ot,this.o_.Ft);if(null!==o&&o.$h){var h=o.$h(i,n);if(null!==h)return{i_:r,yd:h}}}return null},t.prototype.Md=function(){if(null!==this.ud){var t=this.ad,i=this.ud.Ya().W().visible,n=this.ud.$a().W().visible;i||null===this.X_||(this.fd.removeChild(this.X_.B_()),this.X_.g(),this.X_=null),n||null===this.Z_||(this.vd.removeChild(this.Z_.B_()),this.Z_.g(),this.Z_=null);var e=t.jt().qf();i&&null===this.X_&&(this.X_=new Nn(this,t.W().layout,e,\"left\"),this.fd.appendChild(this.X_.B_())),n&&null===this.Z_&&(this.Z_=new Nn(this,t.W().layout,e,\"right\"),this.vd.appendChild(this.Z_.B_()))}},t.prototype.Id=function(t){return t.Gv&&this.nd||null!==this.sd},t.prototype.jd=function(t){return Math.max(0,Math.min(t,this.o_.Ft-1))},t.prototype.qd=function(t){return Math.max(0,Math.min(t,this.o_.Ot-1))},t.prototype.pd=function(t,i){this.pi().Rf(this.jd(t),this.qd(i),f(this.ud))},t.prototype.Cd=function(){this.pi().If()},t.prototype.Dd=function(){this.hd&&(this.sd=null,this.Cd())},t.prototype.xd=function(t,i){this.sd=t,this.hd=!1,this.pd(i.x,i.y);var n=this.pi().Tf();this.rd={x:n.$t(),y:n.Kt()}},t.prototype.pi=function(){return this.ad.jt()},t.prototype.Ud=function(){var t=this.pi(),i=this.z_(),n=i.ji();t.Qa(i,n),t.Pf(),this.J_=null,this.G_=!1},t.prototype.Nd=function(t){var i=this;if(this.G_){var n=performance.now();if(null!==this.ed&&this.ed.xh(t.Yv,n),null===this.ed||this.ed.cc(n))this.Ud();else{var e=this.pi(),s=e.bt(),r=this.ed,o=function(){if(!r._c()){var t=performance.now(),n=r.cc(t);if(!r._c()){var h=s.Nl();e.Vf(r.fc(t)),h===s.Nl()&&(n=!0,i.ed=null)}n?i.Ud():requestAnimationFrame(o)}};requestAnimationFrame(o)}}},t.prototype.md=function(){this.sd=null},t.prototype.gd=function(){if(this.ud){if(this.Td(),document.activeElement!==document.body&&document.activeElement!==document.documentElement)f(document.activeElement).blur();else{var t=document.getSelection();null!==t&&t.removeAllRanges()}!this.ud.ji().wi()&&this.pi().bt().wi()}},t.prototype.kd=function(t){if(null!==this.ud){var i=this.pi();if(!i.bt().wi()){var n=this.ad.W(),e=n.handleScroll,s=n.kineticScroll;if(e.pressedMouseMove&&!t.Gv||(e.horzTouchDrag||e.vertTouchDrag)&&t.Gv){var r=this.ud.ji(),o=performance.now();null!==this.J_||this.Id(t)||(this.J_={x:t.Rv,y:t.Iv,So:o,Yv:t.Yv,$v:t.$v}),null!==this.ed&&this.ed.lc(t.Yv,o),null===this.J_||this.G_||this.J_.x===t.Rv&&this.J_.y===t.Iv||(null===this.ed&&(t.Gv&&s.touch||!t.Gv&&s.mouse)&&(this.ed=new Cn(.2,7,.997,15),this.ed.lc(this.J_.Yv,this.J_.So),this.ed.lc(t.Yv,o)),r.wi()||i.Ja(this.ud,r,t.$v),i.Of(t.Yv),this.G_=!0),this.G_&&(r.wi()||i.Ga(this.ud,r,t.$v),i.Vf(t.Yv))}}}},t.prototype.Td=function(){var t=performance.now(),i=null===this.ed||this.ed.cc(t);null!==this.ed&&(i||this.Ud()),null!==this.ed&&(this.ed.dc(),this.ed=null)},t}(),Fn=function(){function t(t,i,n,e,s){var r=this;this.ft=!0,this.o_=new wn(0,0),this.d_=function(){return r.R_(3)},this.m_=\"left\"===t,this.cf=n.qf,this.zi=i,this.Hd=e,this.Yd=s,this.p_=document.createElement(\"div\"),this.p_.style.width=\"25px\",this.p_.style.height=\"100%\",this.p_.style.overflow=\"hidden\",this.g_=_n(this.p_,new wn(16,16)),this.g_.subscribeCanvasConfigured(this.d_)}return t.prototype.g=function(){this.g_.unsubscribeCanvasConfigured(this.d_),this.g_.destroy()},t.prototype.B_=function(){return this.p_},t.prototype.Ld=function(){return this.o_},t.prototype.P_=function(t){if(t.Ft<0||t.Ot<0)throw new Error(\"Try to set invalid size to PriceAxisStub \"+JSON.stringify(t));this.o_.br(t)||(this.o_=t,this.g_.resizeCanvas({width:t.Ft,height:t.Ot}),this.p_.style.width=\"\".concat(t.Ft,\"px\"),this.p_.style.minWidth=\"\".concat(t.Ft,\"px\"),this.p_.style.height=\"\".concat(t.Ot,\"px\"),this.ft=!0)},t.prototype.R_=function(t){if((!(t<3)||this.ft)&&0!==this.o_.Ft&&0!==this.o_.Ot){this.ft=!1;var i=mn(this.g_.canvas);this.j_(i,this.g_.pixelRatio),this.Rs(i,this.g_.pixelRatio)}},t.prototype.Y_=function(){return this.g_.canvas},t.prototype.Rs=function(t,i){if(this.Hd()){var n=this.o_.Ft;t.save(),t.fillStyle=this.zi.timeScale.borderColor;var e=Math.floor(this.cf.W().N*i),s=this.m_?Math.round(n*i)-e:0;t.fillRect(s,0,e,e),t.restore()}},t.prototype.j_=function(t,i){var n=this;K(t,i,function(){H(t,0,0,n.o_.Ft,n.o_.Ot,n.Yd())})},t}();function jn(t,i){return t.Po>i.Po?t:i}var Xn=function(){function t(t){var i=this;this.$d=null,this.Kd=null,this.k=null,this.Xd=!1,this.o_=new wn(0,0),this.Zd=new z,this.f_=new Yt(5),this.__=!1,this.d_=function(){i.__||i.ad.jt().Wr()},this.b_=function(){i.__||i.ad.jt().Wr()},this.ad=t,this.zi=t.W().layout,this.Jd=document.createElement(\"tr\"),this.Gd=document.createElement(\"td\"),this.Gd.style.padding=\"0\",this.Qd=document.createElement(\"td\"),this.Qd.style.padding=\"0\",this.p_=document.createElement(\"td\"),this.p_.style.height=\"25px\",this.p_.style.padding=\"0\",this.tw=document.createElement(\"div\"),this.tw.style.width=\"100%\",this.tw.style.height=\"100%\",this.tw.style.position=\"relative\",this.tw.style.overflow=\"hidden\",this.p_.appendChild(this.tw),this.g_=_n(this.tw,new wn(16,16)),this.g_.subscribeCanvasConfigured(this.d_);var n=this.g_.canvas;n.style.position=\"absolute\",n.style.zIndex=\"1\",n.style.left=\"0\",n.style.top=\"0\",this.y_=_n(this.tw,new wn(16,16)),this.y_.subscribeCanvasConfigured(this.b_);var e=this.y_.canvas;e.style.position=\"absolute\",e.style.zIndex=\"2\",e.style.left=\"0\",e.style.top=\"0\",this.Jd.appendChild(this.Gd),this.Jd.appendChild(this.p_),this.Jd.appendChild(this.Qd),this.iw(),this.ad.jt().Pa().u(this.iw.bind(this),this),this.A_=new Tn(this.y_.canvas,this,{_v:function(){return!0},dv:function(){return!1}})}return t.prototype.g=function(){this.A_.g(),null!==this.$d&&this.$d.g(),null!==this.Kd&&this.Kd.g(),this.y_.unsubscribeCanvasConfigured(this.b_),this.y_.destroy(),this.g_.unsubscribeCanvasConfigured(this.d_),this.g_.destroy()},t.prototype.B_=function(){return this.Jd},t.prototype.nw=function(){return this.$d},t.prototype.sw=function(){return this.Kd},t.prototype.Tv=function(t){if(!this.Xd){this.Xd=!0;var i=this.ad.jt();!i.bt().wi()&&this.ad.W().handleScale.axisPressedMouseMove.time&&i.Lf(t.Yv)}},t.prototype.Cv=function(t){this.Tv(t)},t.prototype.Dv=function(){var t=this.ad.jt();!t.bt().wi()&&this.Xd&&(this.Xd=!1,this.ad.W().handleScale.axisPressedMouseMove.time&&t.zf())},t.prototype.bv=function(t){var i=this.ad.jt();!i.bt().wi()&&this.ad.W().handleScale.axisPressedMouseMove.time&&i.Wf(t.Yv)},t.prototype.wv=function(t){this.bv(t)},t.prototype.yv=function(){this.Xd=!1;var t=this.ad.jt();t.bt().wi()&&!this.ad.W().handleScale.axisPressedMouseMove.time||t.zf()},t.prototype.pv=function(){this.yv()},t.prototype.nv=function(){this.ad.W().handleScale.axisDoubleClickReset&&this.ad.jt().pn()},t.prototype.Gc=function(){this.nv()},t.prototype.ov=function(){this.ad.jt().W().handleScale.axisPressedMouseMove.time&&this.K_(1)},t.prototype.Wv=function(){this.K_(0)},t.prototype.Ld=function(){return this.o_},t.prototype.hw=function(){return this.Zd},t.prototype.rw=function(t,i,n){this.o_&&this.o_.br(t)||(this.o_=t,this.__=!0,this.g_.resizeCanvas({width:t.Ft,height:t.Ot}),this.y_.resizeCanvas({width:t.Ft,height:t.Ot}),this.__=!1,this.p_.style.width=t.Ft+\"px\",this.p_.style.height=t.Ot+\"px\",this.Zd.m(t)),null!==this.$d&&this.$d.P_(new wn(i,t.Ot)),null!==this.Kd&&this.Kd.P_(new wn(n,t.Ot))},t.prototype.ew=function(){var t=this.uw();return Math.ceil(t.N+t.C+t.S+t.O+t.B)},t.prototype.vt=function(){this.ad.jt().bt().au()},t.prototype.Y_=function(){return this.g_.canvas},t.prototype.R_=function(t){if(0!==t){if(1!==t){var i=mn(this.g_.canvas);this.j_(i,this.g_.pixelRatio),this.Rs(i,this.g_.pixelRatio),this.q_(i,this.g_.pixelRatio),null!==this.$d&&this.$d.R_(t),null!==this.Kd&&this.Kd.R_(t)}var n=mn(this.y_.canvas),e=this.y_.pixelRatio;n.clearRect(0,0,Math.ceil(this.o_.Ft*e),Math.ceil(this.o_.Ot*e)),this.aw([this.ad.jt().Tf()],n,e)}},t.prototype.j_=function(t,i){var n=this;K(t,i,function(){H(t,0,0,n.o_.Ft,n.o_.Ot,n.ad.jt().Xf())})},t.prototype.Rs=function(t,i){if(this.ad.W().timeScale.borderVisible){t.save(),t.fillStyle=this.ow();var n=Math.max(1,Math.floor(this.uw().N*i));t.fillRect(0,0,Math.ceil(this.o_.Ft*i),n),t.restore()}},t.prototype.q_=function(t,i){var n=this,e=this.ad.jt().bt().au();if(e&&0!==e.length){var s=e.reduce(jn,e[0]).Po;s>30&&s<50&&(s=30),t.save(),t.strokeStyle=this.ow();var r=this.uw(),o=r.N+r.C+r.O+r.S-r.V;t.textAlign=\"center\",t.fillStyle=this.ow();var h=Math.floor(this.uw().N*i),u=Math.max(1,Math.floor(i)),a=Math.floor(.5*i);if(this.ad.jt().bt().W().borderVisible){t.beginPath();for(var l=Math.round(r.C*i),c=e.length;c--;){var f=Math.round(e[c].su*i);t.rect(f-a,h,u,l)}t.fill()}t.fillStyle=this.j(),K(t,i,function(){t.font=n.lw();for(var i=0,r=e;i<r.length;i++)if((l=r[i]).Po<s){var h=l.Sl?n.fw(t,l.su,l.yu):l.su;t.fillText(l.yu,h,o)}t.font=n.cw();for(var u=0,a=e;u<a.length;u++){var l;(l=a[u]).Po>=s&&(h=l.Sl?n.fw(t,l.su,l.yu):l.su,t.fillText(l.yu,h,o))}}),t.restore()}},t.prototype.fw=function(t,i,n){var e=this.f_.Qt(t,n),s=e/2,r=Math.floor(i-s)+.5;return r<0?i+=Math.abs(0-r):r+e>this.o_.Ft&&(i-=Math.abs(this.o_.Ft-(r+e))),i},t.prototype.aw=function(t,i,n){for(var e=this.uw(),s=0,r=t;s<r.length;s++)for(var o=0,h=r[s].Ti();o<h.length;o++){var u=h[o];i.save(),u.dt().H(i,e,n),i.restore()}},t.prototype.ow=function(){return this.ad.W().timeScale.borderColor},t.prototype.j=function(){return this.zi.textColor},t.prototype.R=function(){return this.zi.fontSize},t.prototype.lw=function(){return O(this.R(),this.zi.fontFamily)},t.prototype.cw=function(){return O(this.R(),this.zi.fontFamily,\"bold\")},t.prototype.uw=function(){null===this.k&&(this.k={N:1,V:NaN,O:NaN,B:NaN,mi:NaN,C:3,S:NaN,T:\"\",bi:new Yt});var t=this.k,i=this.lw();if(t.T!==i){var n=this.R();t.S=n,t.T=i,t.O=Math.ceil(n/2.5),t.B=t.O,t.mi=Math.ceil(n/2),t.V=Math.round(this.R()/5),t.bi.ih()}return this.k},t.prototype.K_=function(t){this.p_.style.cursor=1===t?\"ew-resize\":\"default\"},t.prototype.iw=function(){var t=this.ad.jt(),i=t.W();i.leftPriceScale.visible||null===this.$d||(this.Gd.removeChild(this.$d.B_()),this.$d.g(),this.$d=null),i.rightPriceScale.visible||null===this.Kd||(this.Qd.removeChild(this.Kd.B_()),this.Kd.g(),this.Kd=null);var n={qf:this.ad.jt().qf()},e=function(){return i.leftPriceScale.borderVisible&&t.bt().W().borderVisible},s=function(){return t.Xf()};i.leftPriceScale.visible&&null===this.$d&&(this.$d=new Fn(\"left\",i,n,e,s),this.Gd.appendChild(this.$d.B_())),i.rightPriceScale.visible&&null===this.Kd&&(this.Kd=new Fn(\"right\",i,n,e,s),this.Qd.appendChild(this.Kd.B_()))},t}(),Yn=function(){function t(t,i){var n;this._w=[],this.dw=0,this.xu=0,this.hh=0,this.ww=0,this.Mw=0,this.bw=null,this.mw=!1,this.Q_=new z,this.af=new z,this.zi=i,this.Jd=document.createElement(\"div\"),this.Jd.classList.add(\"tv-lightweight-charts\"),this.Jd.style.overflow=\"hidden\",this.Jd.style.width=\"100%\",this.Jd.style.height=\"100%\",(n=this.Jd).style.userSelect=\"none\",n.style.webkitUserSelect=\"none\",n.style.msUserSelect=\"none\",n.style.MozUserSelect=\"none\",n.style.webkitTapHighlightColor=\"transparent\",this.pw=document.createElement(\"table\"),this.pw.setAttribute(\"cellspacing\",\"0\"),this.Jd.appendChild(this.pw),this.gw=this.yw.bind(this),this.Jd.addEventListener(\"wheel\",this.gw,{passive:!1}),this.pi=new pn(this.ff.bind(this),this.zi),this.jt().Df().u(this.kw.bind(this),this),this.Nw=new Xn(this),this.pw.appendChild(this.Nw.B_());var e=this.zi.width,s=this.zi.height;if(0===e||0===s){var r=t.getBoundingClientRect();0===e&&(e=Math.floor(r.width),e-=e%2),0===s&&(s=Math.floor(r.height),s-=s%2)}this.xw(e,s),this.Cw(),t.appendChild(this.Jd),this.Sw(),this.pi.bt().Pl().u(this.pi.Ce.bind(this.pi),this),this.pi.Pa().u(this.pi.Ce.bind(this.pi),this)}return t.prototype.jt=function(){return this.pi},t.prototype.W=function(){return this.zi},t.prototype.Tw=function(){return this._w},t.prototype.Dw=function(){return this.Nw},t.prototype.g=function(){this.Jd.removeEventListener(\"wheel\",this.gw),0!==this.dw&&window.cancelAnimationFrame(this.dw),this.pi.Df().M(this),this.pi.bt().Pl().M(this),this.pi.Pa().M(this),this.pi.g();for(var t=0,i=this._w;t<i.length;t++){var n=i[t];this.pw.removeChild(n.B_()),n.Sd().M(this),n.g()}this._w=[],f(this.Nw).g(),null!==this.Jd.parentElement&&this.Jd.parentElement.removeChild(this.Jd),this.af.g(),this.Q_.g()},t.prototype.xw=function(t,i,n){if(void 0===n&&(n=!1),this.xu!==i||this.hh!==t){this.xu=i,this.hh=t;var e=i+\"px\",s=t+\"px\";f(this.Jd).style.height=e,f(this.Jd).style.width=s,this.pw.style.height=e,this.pw.style.width=s,n?this.Aw(new ht(3)):this.pi.Ce()}},t.prototype.R_=function(t){void 0===t&&(t=new ht(3));for(var i=0;i<this._w.length;i++)this._w[i].R_(t.wn(i).vn);this.zi.timeScale.visible&&this.Nw.R_(t.dn())},t.prototype.Pr=function(t){this.pi.Pr(t),this.Sw();var i=t.width||this.hh,n=t.height||this.xu;this.xw(i,n)},t.prototype.Sd=function(){return this.Q_},t.prototype.Df=function(){return this.af},t.prototype.Bw=function(){var t=this;null!==this.bw&&(this.Aw(this.bw),this.bw=null);var i=this._w[0],n=Mn(document,new wn(this.hh,this.xu)),e=mn(n),s=gn(n);return K(e,s,function(){var n=0,s=0,r=function(i){for(var r=0;r<t._w.length;r++){var o=t._w[r],h=o.Ld().Ot,u=f(\"left\"===i?o.Wd():o.zd()),a=u.Y_();e.drawImage(a,n,s,u.W_(),h),s+=h}};t.Lw()&&(r(\"left\"),n=f(i.Wd()).W_()),s=0;for(var o=0;o<t._w.length;o++){var h=t._w[o],u=h.Ld(),a=h.Y_();e.drawImage(a,n,s,u.Ft,u.Ot),s+=u.Ot}n+=i.Ld().Ft,t.Ew()&&(s=0,r(\"right\"));var l=function(i){var r=f(\"left\"===i?t.Nw.nw():t.Nw.sw()),o=r.Ld(),h=r.Y_();e.drawImage(h,n,s,o.Ft,o.Ot)};if(t.zi.timeScale.visible){n=0,t.Lw()&&(l(\"left\"),n=f(i.Wd()).W_());var c=t.Nw.Ld();a=t.Nw.Y_(),e.drawImage(a,n,s,c.Ft,c.Ot),t.Ew()&&(n+=i.Ld().Ft,l(\"right\"),e.restore())}}),n},t.prototype.Fw=function(t){return\"none\"===t?0:\"left\"===t&&!this.Lw()||\"right\"===t&&!this.Ew()?0:0===this._w.length?0:f(\"left\"===t?this._w[0].Wd():this._w[0].zd()).W_()},t.prototype.Ow=function(){for(var t=0,i=0,n=0,e=0,s=this._w;e<s.length;e++){var r=s[e];this.Lw()&&(i=Math.max(i,f(r.Wd()).O_())),this.Ew()&&(n=Math.max(n,f(r.zd()).O_())),t+=r.Wa()}var o=this.hh,h=this.xu,u=Math.max(o-i-n,0),a=this.zi.timeScale.visible,l=a?this.Nw.ew():0;l%2&&(l+=1);for(var c=0+l,p=h<c?0:h-c,v=p/t,d=0,y=0;y<this._w.length;++y){(r=this._w[y]).wd(this.pi.Cf()[y]);var w,g;g=y===this._w.length-1?p-d:Math.round(r.Wa()*v),d+=w=Math.max(g,2),r.P_(new wn(u,w)),this.Lw()&&r.Bd(i,\"left\"),this.Ew()&&r.Bd(n,\"right\"),r.z_()&&this.pi.Af(r.z_(),w)}this.Nw.rw(new wn(a?u:0,l),a?i:0,a?n:0),this.pi.Ra(u),this.ww!==i&&(this.ww=i),this.Mw!==n&&(this.Mw=n)},t.prototype.yw=function(t){var i=t.deltaX/100,n=-t.deltaY/100;if(0!==i&&this.zi.handleScroll.mouseWheel||0!==n&&this.zi.handleScale.mouseWheel){switch(t.cancelable&&t.preventDefault(),t.deltaMode){case t.DOM_DELTA_PAGE:i*=120,n*=120;break;case t.DOM_DELTA_LINE:i*=32,n*=32}if(0!==n&&this.zi.handleScale.mouseWheel){var e=Math.sign(n)*Math.min(1,Math.abs(n)),s=t.clientX-this.Jd.getBoundingClientRect().left;this.jt().Ef(s,e)}0!==i&&this.zi.handleScroll.mouseWheel&&this.jt().Ff(-80*i)}},t.prototype.Aw=function(t){var i,n=t.dn();3===n&&this.Vw(),3!==n&&2!==n||(this.Pw(t),this.Ww(t),this.Nw.vt(),this._w.forEach(function(t){t.bd()}),3===(null===(i=this.bw)||void 0===i?void 0:i.dn())&&(this.bw.Nn(t),this.Vw(),this.Pw(this.bw),this.Ww(this.bw),t=this.bw,this.bw=null)),this.R_(t)},t.prototype.Ww=function(t){for(var i=0,n=t.kn();i<n.length;i++){var e=n[i];this.xn(e)}},t.prototype.Pw=function(t){for(var i=this.pi.Cf(),n=0;n<i.length;n++)t.wn(n)._n&&i[n].so()},t.prototype.xn=function(t){var i=this.pi.bt();switch(t.bn){case 0:i.zl();break;case 1:i.Rl(t.St);break;case 2:i.gn(t.St);break;case 3:i.yn(t.St);break;case 4:i.Tl()}},t.prototype.ff=function(t){var i=this;null!==this.bw?this.bw.Nn(t):this.bw=t,this.mw||(this.mw=!0,this.dw=window.requestAnimationFrame(function(){if(i.mw=!1,i.dw=0,null!==i.bw){var t=i.bw;i.bw=null,i.Aw(t)}}))},t.prototype.Vw=function(){this.Cw()},t.prototype.Cw=function(){for(var t=this.pi.Cf(),i=t.length,n=this._w.length,e=i;e<n;e++){var s=c(this._w.pop());this.pw.removeChild(s.B_()),s.Sd().M(this),s.g()}for(e=n;e<i;e++)(s=new Vn(this,t[e])).Sd().u(this.zw.bind(this),this),this._w.push(s),this.pw.insertBefore(s.B_(),this.Nw.B_());for(e=0;e<i;e++){var r=t[e];(s=this._w[e]).z_()!==r?s.wd(r):s.dd()}this.Sw(),this.Ow()},t.prototype.Rw=function(t,i){var n,e=new Map;if(null!==t&&this.pi._t().forEach(function(i){var n=i.er(t);null!==n&&e.set(i,n)}),null!==t){var s=this.pi.bt().gi(t);null!==s&&(n=s)}var r=this.jt().gf(),o=null!==r&&r.kf instanceof Ci?r.kf:void 0,h=null!==r&&void 0!==r.yd?r.yd.Zh:void 0;return{rt:n,Iw:i||void 0,jw:o,qw:e,Uw:h}},t.prototype.zw=function(t,i){var n=this;this.Q_.m(function(){return n.Rw(t,i)})},t.prototype.kw=function(t,i){var n=this;this.af.m(function(){return n.Rw(t,i)})},t.prototype.Sw=function(){var t=this.zi.timeScale.visible?\"\":\"none\";this.Nw.B_().style.display=t},t.prototype.Lw=function(){return this._w[0].z_().Ya().W().visible},t.prototype.Ew=function(){return this._w[0].z_().$a().W().visible},t}();function Jn(t,i,n){var e=n.value;return{vs:i,rt:t,St:[e,e,e,e]}}function In(t,i,n){var e=n.value,s={vs:i,rt:t,St:[e,e,e,e]};return\"color\"in n&&void 0!==n.color&&(s.A=n.color),s}function Zn(t){return void 0!==t.St}function Kn(t){return function(i,n,e){return void 0===(s=e).open&&void 0===s.value?{rt:i,vs:n}:t(i,n,e);var s}}var Hn={Candlestick:Kn(function(t,i,n){var e={vs:i,rt:t,St:[n.open,n.high,n.low,n.close]};return\"color\"in n&&void 0!==n.color&&(e.A=n.color),\"borderColor\"in n&&void 0!==n.borderColor&&(e.Tt=n.borderColor),\"wickColor\"in n&&void 0!==n.wickColor&&(e.qs=n.wickColor),e}),Bar:Kn(function(t,i,n){var e={vs:i,rt:t,St:[n.open,n.high,n.low,n.close]};return\"color\"in n&&void 0!==n.color&&(e.A=n.color),e}),Area:Kn(Jn),Baseline:Kn(Jn),Histogram:Kn(In),Line:Kn(In)};function Un(t){return Hn[t]}function Qn(t){return 60*t*60*1e3}function $n(t){return 60*t*1e3}var qn=[{Hw:(1,1e3),Po:10},{Hw:$n(1),Po:20},{Hw:$n(5),Po:21},{Hw:$n(30),Po:22},{Hw:Qn(1),Po:30},{Hw:Qn(3),Po:31},{Hw:Qn(6),Po:32},{Hw:Qn(12),Po:33}];function te(t,i){if(t.getUTCFullYear()!==i.getUTCFullYear())return 70;if(t.getUTCMonth()!==i.getUTCMonth())return 60;if(t.getUTCDate()!==i.getUTCDate())return 50;for(var n=qn.length-1;n>=0;--n)if(Math.floor(i.getTime()/qn[n].Hw)!==Math.floor(t.getTime()/qn[n].Hw))return qn[n].Po;return 0}function ie(t,i){if(void 0===i&&(i=0),0!==t.length){for(var n=0===i?null:t[i-1].rt.So,e=null!==n?new Date(1e3*n):null,s=0,r=i;r<t.length;++r){var o=t[r],h=new Date(1e3*o.rt.So);null!==e&&(o.Vo=te(h,e)),s+=o.rt.So-(n||o.rt.So),n=o.rt.So,e=h}if(0===i&&t.length>1){var u=Math.ceil(s/(t.length-1)),a=new Date(1e3*(t[0].rt.So-u));t[0].Vo=te(new Date(1e3*t[0].rt.So),a)}}}function ne(t){if(!dn(t))throw new Error(\"time must be of type BusinessDay\");var i=new Date(Date.UTC(t.year,t.month-1,t.day,0,0,0,0));return{So:Math.round(i.getTime()/1e3),Co:t}}function ee(t){if(!yn(t))throw new Error(\"time must be of type isUTCTimestamp\");return{So:t}}function se(t){return 0===t.length?null:dn(t[0].time)?ne:ee}function re(t){return yn(t)?ee(t):dn(t)?ne(t):ne(oe(t))}function oe(t){var i=new Date(t);if(isNaN(i.getTime()))throw new Error(\"Invalid date string=\".concat(t,\", expected format=yyyy-mm-dd\"));return{day:i.getUTCDate(),month:i.getUTCMonth()+1,year:i.getUTCFullYear()}}function he(t){E(t.time)&&(t.time=oe(t.time))}function ue(t){return{vs:0,Yw:new Map,Ve:t}}function ae(t){if(void 0!==t&&0!==t.length)return{$w:t[0].rt.So,Kw:t[t.length-1].rt.So}}var le=function(){function t(){this.Xw=new Map,this.Zw=new Map,this.Jw=new Map,this.Gw=[]}return t.prototype.g=function(){this.Xw.clear(),this.Zw.clear(),this.Jw.clear(),this.Gw=[]},t.prototype.Qw=function(t,i){var n=this,e=0!==this.Xw.size,s=!1,r=this.Zw.get(t);if(void 0!==r)if(1===this.Zw.size)e=!1,s=!0,this.Xw.clear();else for(var o=0,h=this.Gw;o<h.length;o++)h[o].pointData.Yw.delete(t)&&(s=!0);var u=[];if(0!==i.length){i.forEach(he);var a=f(se(i)),l=Un(t.Rr());u=i.map(function(i){var e=a(i.time),r=n.Xw.get(e.So);void 0===r&&(r=ue(e),n.Xw.set(e.So,r),s=!0);var o=l(e,r.vs,i);return r.Yw.set(t,o),o})}e&&this.tM(),this.iM(t,u);var c=-1;if(s){var p=[];this.Xw.forEach(function(t){p.push({Vo:0,rt:t.Ve,pointData:t})}),p.sort(function(t,i){return t.rt.So-i.rt.So}),c=this.nM(p)}return this.sM(t,c,function(t,i){var n=ae(t),e=ae(i);if(void 0!==n&&void 0!==e)return{Ae:n.Kw>=e.Kw&&n.$w>=e.$w}}(this.Zw.get(t),r))},t.prototype.Yf=function(t){return this.Qw(t,[])},t.prototype.hM=function(t,i){he(i);var n=f(se([i]))(i.time),e=this.Jw.get(t);if(void 0!==e&&n.So<e.So)throw new Error(\"Cannot update oldest data, last time=\".concat(e.So,\", new time=\").concat(n.So));var s=this.Xw.get(n.So),r=void 0===s;void 0===s&&(s=ue(n),this.Xw.set(n.So,s));var o=Un(t.Rr())(n,s.vs,i);s.Yw.set(t,o),this.rM(t,o);var h={Ae:Zn(o)};if(!r)return this.sM(t,-1,h);var u={Vo:0,rt:s.Ve,pointData:s},a=wt(this.Gw,u.rt.So,function(t,i){return t.rt.So<i});this.Gw.splice(a,0,u);for(var l=a;l<this.Gw.length;++l)ce(this.Gw[l].pointData,l);return ie(this.Gw,a),this.sM(t,a,h)},t.prototype.rM=function(t,i){var n=this.Zw.get(t);void 0===n&&(n=[],this.Zw.set(t,n));var e=0!==n.length?n[n.length-1]:null;null===e||i.rt.So>e.rt.So?Zn(i)&&n.push(i):Zn(i)?n[n.length-1]=i:n.splice(-1,1),this.Jw.set(t,i.rt)},t.prototype.iM=function(t,i){0!==i.length?(this.Zw.set(t,i.filter(Zn)),this.Jw.set(t,i[i.length-1].rt)):(this.Zw.delete(t),this.Jw.delete(t))},t.prototype.tM=function(){for(var t=0,i=this.Gw;t<i.length;t++){var n=i[t];0===n.pointData.Yw.size&&this.Xw.delete(n.rt.So)}},t.prototype.nM=function(t){for(var i=-1,n=0;n<this.Gw.length&&n<t.length;++n){var e=this.Gw[n],s=t[n];if(e.rt.So!==s.rt.So){i=n;break}s.Vo=e.Vo,ce(s.pointData,n)}if(-1===i&&this.Gw.length!==t.length&&(i=Math.min(this.Gw.length,t.length)),-1===i)return-1;for(n=i;n<t.length;++n)ce(t[n].pointData,n);return ie(t,i),this.Gw=t,i},t.prototype.eM=function(){if(0===this.Zw.size)return null;var t=0;return this.Zw.forEach(function(i){0!==i.length&&(t=Math.max(t,i[i.length-1].vs))}),t},t.prototype.sM=function(t,i,n){var e={uM:new Map,bt:{ml:this.eM()}};if(-1!==i)this.Zw.forEach(function(i,s){e.uM.set(s,{ph:i,aM:s===t?n:void 0})}),this.Zw.has(t)||e.uM.set(t,{ph:[],aM:n}),e.bt.oM=this.Gw,e.bt.lM=i;else{var s=this.Zw.get(t);e.uM.set(t,{ph:s||[],aM:n})}return e},t}();function ce(t,i){t.vs=i,t.Yw.forEach(function(t){t.vs=i})}var fe={color:\"#FF0000\",price:0,lineStyle:2,lineWidth:1,lineVisible:!0,axisLabelVisible:!0,title:\"\"},pe=function(){function t(t){this.Lr=t}return t.prototype.applyOptions=function(t){this.Lr.Pr(t)},t.prototype.options=function(){return this.Lr.W()},t.prototype.fM=function(){return this.Lr},t}();function ve(t){var i=t.overlay,n=function(t,i){var n={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&i.indexOf(e)<0&&(n[e]=t[e]);if(null!=t&&\"function\"==typeof Object.getOwnPropertySymbols){var s=0;for(e=Object.getOwnPropertySymbols(t);s<e.length;s++)i.indexOf(e[s])<0&&Object.prototype.propertyIsEnumerable.call(t,e[s])&&(n[e[s]]=t[e[s]])}return n}(t,[\"overlay\"]);return i&&(n.priceScaleId=\"\"),n}var de=function(){function t(t,i,n){this.Kn=t,this.cM=i,this.vM=n}return t.prototype.priceFormatter=function(){return this.Kn.qe()},t.prototype.priceToCoordinate=function(t){var i=this.Kn.kt();return null===i?null:this.Kn.Ct().xt(t,i.St)},t.prototype.coordinateToPrice=function(t){var i=this.Kn.kt();return null===i?null:this.Kn.Ct().qi(t,i.St)},t.prototype.barsInLogicalRange=function(t){if(null===t)return null;var i=new sn(new qi(t.from,t.to)).jo(),n=this.Kn.an();if(n.wi())return null;var e=n.ne(i.In(),1),s=n.ne(i.jn(),-1),r=f(n.Qr()),o=f(n.un());if(null!==e&&null!==s&&e.vs>s.vs)return{barsBefore:t.from-r,barsAfter:o-t.to};var h={barsBefore:null===e||e.vs===r?t.from-r:e.vs-r,barsAfter:null===s||s.vs===o?o-t.to:o-s.vs};return null!==e&&null!==s&&(h.from=e.rt.Co||e.rt.So,h.to=s.rt.Co||s.rt.So),h},t.prototype.setData=function(t){this.Kn.Rr(),this.cM._M(this.Kn,t)},t.prototype.update=function(t){this.Kn.Rr(),this.cM.dM(this.Kn,t)},t.prototype.setMarkers=function(t){var i=t.map(function(t){return x(x({},t),{time:re(t.time)})});this.Kn.Le(i)},t.prototype.applyOptions=function(t){var i=ve(t);this.Kn.Pr(i)},t.prototype.options=function(){return L(this.Kn.W())},t.prototype.priceScale=function(){return this.vM.priceScale(this.Kn.Ct().Ke())},t.prototype.createPriceLine=function(t){var i=T(L(fe),t),n=this.Kn.Ee(i);return new pe(n)},t.prototype.removePriceLine=function(t){this.Kn.Fe(t.fM())},t.prototype.seriesType=function(){return this.Kn.Rr()},t}(),ye=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return C(i,t),i.prototype.applyOptions=function(i){vn(i),t.prototype.applyOptions.call(this,i)},i}(de),we={autoScale:!0,mode:0,invertScale:!1,alignLabels:!0,borderVisible:!0,borderColor:\"#2B2B43\",entireTextOnly:!1,visible:!1,drawTicks:!0,scaleMargins:{bottom:.1,top:.2}},ge={color:\"rgba(0, 0, 0, 0)\",visible:!1,fontSize:48,fontFamily:R,fontStyle:\"\",text:\"\",horzAlign:\"center\",vertAlign:\"center\"},me={width:0,height:0,layout:{background:{type:\"solid\",color:\"#FFFFFF\"},textColor:\"#191919\",fontSize:11,fontFamily:R},crosshair:{vertLine:{color:\"#758696\",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:\"#4c525e\"},horzLine:{color:\"#758696\",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:\"#4c525e\"},mode:1},grid:{vertLines:{color:\"#D6DCDE\",style:0,visible:!0},horzLines:{color:\"#D6DCDE\",style:0,visible:!0}},overlayPriceScales:x({},we),leftPriceScale:x(x({},we),{visible:!1}),rightPriceScale:x(x({},we),{visible:!0}),timeScale:{rightOffset:0,barSpacing:6,minBarSpacing:.5,fixLeftEdge:!1,fixRightEdge:!1,lockVisibleTimeRangeOnResize:!1,rightBarStaysOnScroll:!1,borderVisible:!0,borderColor:\"#2B2B43\",visible:!0,timeVisible:!1,secondsVisible:!0,shiftVisibleRangeOnNewBar:!0},watermark:ge,localization:{locale:xn?navigator.language:\"\",dateFormat:\"dd MMM 'yy\"},handleScroll:{mouseWheel:!0,pressedMouseMove:!0,horzTouchDrag:!0,vertTouchDrag:!0},handleScale:{axisPressedMouseMove:{time:!0,price:!0},axisDoubleClickReset:!0,mouseWheel:!0,pinch:!0},kineticScroll:{mouse:!1,touch:!0},trackingMode:{exitMode:1}},Me={upColor:\"#26a69a\",downColor:\"#ef5350\",wickVisible:!0,borderVisible:!0,borderColor:\"#378658\",borderUpColor:\"#26a69a\",borderDownColor:\"#ef5350\",wickColor:\"#737375\",wickUpColor:\"#26a69a\",wickDownColor:\"#ef5350\"},_e={upColor:\"#26a69a\",downColor:\"#ef5350\",openVisible:!0,thinBars:!0},be={color:\"#2196f3\",lineStyle:0,lineWidth:3,lineType:0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:\"\",crosshairMarkerBackgroundColor:\"\",lastPriceAnimation:0},Se={topColor:\"rgba( 46, 220, 135, 0.4)\",bottomColor:\"rgba( 40, 221, 100, 0)\",lineColor:\"#33D778\",lineStyle:0,lineWidth:3,lineType:0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:\"\",crosshairMarkerBackgroundColor:\"\",lastPriceAnimation:0},Ce={baseValue:{type:\"price\",price:0},topFillColor1:\"rgba(38, 166, 154, 0.28)\",topFillColor2:\"rgba(38, 166, 154, 0.05)\",topLineColor:\"rgba(38, 166, 154, 1)\",bottomFillColor1:\"rgba(239, 83, 80, 0.05)\",bottomFillColor2:\"rgba(239, 83, 80, 0.28)\",bottomLineColor:\"rgba(239, 83, 80, 1)\",lineWidth:3,lineStyle:0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:\"\",crosshairMarkerBackgroundColor:\"\",lastPriceAnimation:0},xe={color:\"#26a69a\",base:0},Be={title:\"\",visible:!0,lastValueVisible:!0,priceLineVisible:!0,priceLineSource:0,priceLineWidth:1,priceLineColor:\"\",priceLineStyle:2,baseLineVisible:!0,baseLineWidth:1,baseLineColor:\"#B2B5BE\",baseLineStyle:0,priceFormat:{type:\"price\",precision:2,minMove:.01}},ze=function(){function t(t,i){this.wM=t,this.MM=i}return t.prototype.applyOptions=function(t){this.wM.jt().Nf(this.MM,t)},t.prototype.options=function(){return this._i().W()},t.prototype.width=function(){return ot(this.MM)?this.wM.Fw(\"left\"===this.MM?\"left\":\"right\"):0},t.prototype._i=function(){return f(this.wM.jt().xf(this.MM)).Ct},t}(),Te=function(){function t(t,i){this.bM=new z,this.Go=new z,this.Zd=new z,this.pi=t,this.Da=t.bt(),this.Nw=i,this.Da.Ol().u(this.mM.bind(this)),this.Da.Vl().u(this.pM.bind(this)),this.Nw.hw().u(this.gM.bind(this))}return t.prototype.g=function(){this.Da.Ol().M(this),this.Da.Vl().M(this),this.Nw.hw().M(this),this.bM.g(),this.Go.g(),this.Zd.g()},t.prototype.scrollPosition=function(){return this.Da.Nl()},t.prototype.scrollToPosition=function(t,i){i?this.Da.Fl(t,1e3):this.pi.yn(t)},t.prototype.scrollToRealTime=function(){this.Da.El()},t.prototype.getVisibleRange=function(){var t,i,n=this.Da.cl();return null===n?null:{from:null!==(t=n.from.Co)&&void 0!==t?t:n.from.So,to:null!==(i=n.to.Co)&&void 0!==i?i:n.to.So}},t.prototype.setVisibleRange=function(t){var i={from:re(t.from),to:re(t.to)},n=this.Da.wl(i);this.pi.$f(n)},t.prototype.getVisibleLogicalRange=function(){var t=this.Da.fl();return null===t?null:{from:t.In(),to:t.jn()}},t.prototype.setVisibleLogicalRange=function(t){l(t.from<=t.to,\"The from index cannot be after the to index.\"),this.pi.$f(t)},t.prototype.resetTimeScale=function(){this.pi.pn()},t.prototype.fitContent=function(){this.pi.zl()},t.prototype.logicalToCoordinate=function(t){var i=this.pi.bt();return i.wi()?null:i.At(t)},t.prototype.coordinateToLogical=function(t){return this.Da.wi()?null:this.Da.pl(t)},t.prototype.timeToCoordinate=function(t){var i=re(t),n=this.Da.Ze(i,!1);return null===n?null:this.Da.At(n)},t.prototype.coordinateToTime=function(t){var i,n=this.pi.bt(),e=n.pl(t),s=n.gi(e);return null===s?null:null!==(i=s.Co)&&void 0!==i?i:s.So},t.prototype.width=function(){return this.Nw.Ld().Ft},t.prototype.height=function(){return this.Nw.Ld().Ot},t.prototype.subscribeVisibleTimeRangeChange=function(t){this.bM.u(t)},t.prototype.unsubscribeVisibleTimeRangeChange=function(t){this.bM._(t)},t.prototype.subscribeVisibleLogicalRangeChange=function(t){this.Go.u(t)},t.prototype.unsubscribeVisibleLogicalRangeChange=function(t){this.Go._(t)},t.prototype.subscribeSizeChange=function(t){this.Zd.u(t)},t.prototype.unsubscribeSizeChange=function(t){this.Zd._(t)},t.prototype.applyOptions=function(t){this.Da.Pr(t)},t.prototype.options=function(){return L(this.Da.W())},t.prototype.mM=function(){this.bM.p()&&this.bM.m(this.getVisibleRange())},t.prototype.pM=function(){this.Go.p()&&this.Go.m(this.getVisibleLogicalRange())},t.prototype.gM=function(t){this.Zd.m(t.Ft,t.Ot)},t}();function ke(t){if(void 0!==t&&\"custom\"!==t.type){var i=t;void 0!==i.minMove&&void 0===i.precision&&(i.precision=function(t){if(t>=1)return 0;for(var i=0;i<8;i++){var n=Math.round(t);if(Math.abs(n-t)<1e-8)return i;t*=10}return i}(i.minMove))}}function De(t){return function(t){if(P(t.handleScale)){var i=t.handleScale;t.handleScale={axisDoubleClickReset:i,axisPressedMouseMove:{time:i,price:i},mouseWheel:i,pinch:i}}else if(void 0!==t.handleScale&&P(t.handleScale.axisPressedMouseMove)){var n=t.handleScale.axisPressedMouseMove;t.handleScale.axisPressedMouseMove={time:n,price:n}}var e=t.handleScroll;P(e)&&(t.handleScroll={horzTouchDrag:e,vertTouchDrag:e,mouseWheel:e,pressedMouseMove:e})}(t),function(t){if(t.priceScale){t.leftPriceScale=t.leftPriceScale||{},t.rightPriceScale=t.rightPriceScale||{};var i=t.priceScale.position;delete t.priceScale.position,t.leftPriceScale=T(t.leftPriceScale,t.priceScale),t.rightPriceScale=T(t.rightPriceScale,t.priceScale),\"left\"===i&&(t.leftPriceScale.visible=!0,t.rightPriceScale.visible=!1),\"right\"===i&&(t.leftPriceScale.visible=!1,t.rightPriceScale.visible=!0),\"none\"===i&&(t.leftPriceScale.visible=!1,t.rightPriceScale.visible=!1),t.overlayPriceScales=t.overlayPriceScales||{},void 0!==t.priceScale.invertScale&&(t.overlayPriceScales.invertScale=t.priceScale.invertScale),void 0!==t.priceScale.scaleMargins&&(t.overlayPriceScales.scaleMargins=t.priceScale.scaleMargins)}}(t),function(t){t.layout&&t.layout.backgroundColor&&!t.layout.background&&(t.layout.background={type:\"solid\",color:t.layout.backgroundColor})}(t),t}var Ee=function(){function t(t,i){var n=this;this.yM=new le,this.kM=new Map,this.NM=new Map,this.xM=new z,this.CM=new z;var e=void 0===i?L(me):T(L(me),De(i));this.wM=new Yn(t,e),this.wM.Sd().u(function(t){n.xM.p()&&n.xM.m(n.SM(t()))},this),this.wM.Df().u(function(t){n.CM.p()&&n.CM.m(n.SM(t()))},this);var s=this.wM.jt();this.TM=new Te(s,this.wM.Dw())}return t.prototype.remove=function(){this.wM.Sd().M(this),this.wM.Df().M(this),this.TM.g(),this.wM.g(),this.kM.clear(),this.NM.clear(),this.xM.g(),this.CM.g(),this.yM.g()},t.prototype.resize=function(t,i,n){this.wM.xw(t,i,n)},t.prototype.addAreaSeries=function(t){void 0===t&&(t={}),ke((t=ve(t)).priceFormat);var i=T(L(Be),Se,t),n=this.wM.jt().Uf(\"Area\",i),e=new de(n,this,this);return this.kM.set(e,n),this.NM.set(n,e),e},t.prototype.addBaselineSeries=function(t){void 0===t&&(t={}),ke((t=ve(t)).priceFormat);var i=T(L(Be),L(Ce),t),n=this.wM.jt().Uf(\"Baseline\",i),e=new de(n,this,this);return this.kM.set(e,n),this.NM.set(n,e),e},t.prototype.addBarSeries=function(t){void 0===t&&(t={}),ke((t=ve(t)).priceFormat);var i=T(L(Be),_e,t),n=this.wM.jt().Uf(\"Bar\",i),e=new de(n,this,this);return this.kM.set(e,n),this.NM.set(n,e),e},t.prototype.addCandlestickSeries=function(t){void 0===t&&(t={}),vn(t=ve(t)),ke(t.priceFormat);var i=T(L(Be),Me,t),n=this.wM.jt().Uf(\"Candlestick\",i),e=new ye(n,this,this);return this.kM.set(e,n),this.NM.set(n,e),e},t.prototype.addHistogramSeries=function(t){void 0===t&&(t={}),ke((t=ve(t)).priceFormat);var i=T(L(Be),xe,t),n=this.wM.jt().Uf(\"Histogram\",i),e=new de(n,this,this);return this.kM.set(e,n),this.NM.set(n,e),e},t.prototype.addLineSeries=function(t){void 0===t&&(t={}),ke((t=ve(t)).priceFormat);var i=T(L(Be),be,t),n=this.wM.jt().Uf(\"Line\",i),e=new de(n,this,this);return this.kM.set(e,n),this.NM.set(n,e),e},t.prototype.removeSeries=function(t){var i=c(this.kM.get(t)),n=this.yM.Yf(i);this.wM.jt().Yf(i),this.DM(n),this.kM.delete(t),this.NM.delete(i)},t.prototype._M=function(t,i){this.DM(this.yM.Qw(t,i))},t.prototype.dM=function(t,i){this.DM(this.yM.hM(t,i))},t.prototype.subscribeClick=function(t){this.xM.u(t)},t.prototype.unsubscribeClick=function(t){this.xM._(t)},t.prototype.subscribeCrosshairMove=function(t){this.CM.u(t)},t.prototype.unsubscribeCrosshairMove=function(t){this.CM._(t)},t.prototype.priceScale=function(t){return void 0===t&&(t=this.wM.jt().Kf()),new ze(this.wM,t)},t.prototype.timeScale=function(){return this.TM},t.prototype.applyOptions=function(t){this.wM.Pr(De(t))},t.prototype.options=function(){return this.wM.W()},t.prototype.takeScreenshot=function(){return this.wM.Bw()},t.prototype.DM=function(t){var i=this.wM.jt();i.jf(t.bt.ml,t.bt.oM,t.bt.lM),t.uM.forEach(function(t,i){return i.Z(t.ph,t.aM)}),i.yl()},t.prototype.AM=function(t){return c(this.NM.get(t))},t.prototype.SM=function(t){var i=this,n=new Map;t.qw.forEach(function(t,e){n.set(i.AM(e),t)});var e=void 0===t.jw?void 0:this.AM(t.jw);return{time:t.rt&&(t.rt.Co||t.rt.So),point:t.Iw,hoveredSeries:e,hoveredMarkerId:t.Uw,seriesPrices:n}},t}();function Pe(t,i){var n;if(E(t)){var e=document.getElementById(t);l(null!==e,\"Cannot find element in DOM with id=\".concat(t)),n=e}else n=t;return new Ee(n,i)}}}]);", "extractedComments": []}